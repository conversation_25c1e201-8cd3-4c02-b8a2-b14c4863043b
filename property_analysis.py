#!/usr/bin/env python3
"""
📊 PropertyGuru Data Analysis
Analyzes scraped property data and provides market insights
"""

import json
import statistics
from datetime import datetime
from collections import Counter

class PropertyAnalyzer:
    def __init__(self, json_file):
        self.json_file = json_file
        self.properties = self.load_properties()
        
    def load_properties(self):
        """Load properties from JSON file"""
        try:
            with open(self.json_file, 'r', encoding='utf-8') as f:
                properties = json.load(f)
            print(f"📁 Loaded {len(properties)} properties from {self.json_file}")
            return properties
        except Exception as e:
            print(f"❌ Error loading file: {e}")
            return []
    
    def analyze_all(self):
        """Perform comprehensive analysis"""
        if not self.properties:
            print("❌ No properties to analyze")
            return
        
        print("🏠 PROPERTYGURU MARKET ANALYSIS")
        print("=" * 60)
        
        self.basic_statistics()
        self.price_analysis()
        self.bedroom_analysis()
        self.area_analysis()
        self.price_per_sqft_analysis()
        self.market_segments()
        
    def basic_statistics(self):
        """Basic property statistics"""
        print(f"\n📊 BASIC STATISTICS")
        print("-" * 30)
        print(f"Total Properties: {len(self.properties)}")
        print(f"Data Source: PropertyGuru Singapore")
        print(f"Extraction Time: {self.properties[0].get('extraction_timestamp', 'Unknown')}")
        
    def price_analysis(self):
        """Analyze property prices"""
        prices = [p['price'] for p in self.properties if 'price' in p]
        
        if not prices:
            print("❌ No price data available")
            return
        
        print(f"\n💰 PRICE ANALYSIS")
        print("-" * 30)
        print(f"Average Price: S$ {statistics.mean(prices):,.0f}")
        print(f"Median Price: S$ {statistics.median(prices):,.0f}")
        print(f"Lowest Price: S$ {min(prices):,.0f}")
        print(f"Highest Price: S$ {max(prices):,.0f}")
        print(f"Price Range: S$ {max(prices) - min(prices):,.0f}")
        
        # Price ranges
        price_ranges = {
            "Under S$ 1M": len([p for p in prices if p < 1000000]),
            "S$ 1M - 2M": len([p for p in prices if 1000000 <= p < 2000000]),
            "S$ 2M - 5M": len([p for p in prices if 2000000 <= p < 5000000]),
            "S$ 5M - 10M": len([p for p in prices if 5000000 <= p < 10000000]),
            "Above S$ 10M": len([p for p in prices if p >= 10000000])
        }
        
        print(f"\n💵 Price Distribution:")
        for range_name, count in price_ranges.items():
            percentage = (count / len(prices)) * 100
            print(f"   {range_name}: {count} properties ({percentage:.1f}%)")
    
    def bedroom_analysis(self):
        """Analyze bedroom distribution"""
        bedrooms = [p['bedrooms'] for p in self.properties if 'bedrooms' in p]
        
        if not bedrooms:
            print("❌ No bedroom data available")
            return
        
        bedroom_counts = Counter(bedrooms)
        
        print(f"\n🛏️ BEDROOM ANALYSIS")
        print("-" * 30)
        print(f"Average Bedrooms: {statistics.mean(bedrooms):.1f}")
        
        print(f"\nBedroom Distribution:")
        for beds in sorted(bedroom_counts.keys()):
            count = bedroom_counts[beds]
            percentage = (count / len(bedrooms)) * 100
            print(f"   {beds} Bedrooms: {count} properties ({percentage:.1f}%)")
    
    def area_analysis(self):
        """Analyze property areas"""
        areas = [p['area'] for p in self.properties if 'area' in p]
        
        if not areas:
            print("❌ No area data available")
            return
        
        print(f"\n📐 AREA ANALYSIS")
        print("-" * 30)
        print(f"Average Area: {statistics.mean(areas):,.0f} sqft")
        print(f"Median Area: {statistics.median(areas):,.0f} sqft")
        print(f"Smallest Unit: {min(areas):,.0f} sqft")
        print(f"Largest Unit: {max(areas):,.0f} sqft")
        
        # Area ranges
        area_ranges = {
            "Under 500 sqft": len([a for a in areas if a < 500]),
            "500 - 750 sqft": len([a for a in areas if 500 <= a < 750]),
            "750 - 1000 sqft": len([a for a in areas if 750 <= a < 1000]),
            "1000 - 1500 sqft": len([a for a in areas if 1000 <= a < 1500]),
            "1500 - 2000 sqft": len([a for a in areas if 1500 <= a < 2000]),
            "Above 2000 sqft": len([a for a in areas if a >= 2000])
        }
        
        print(f"\n📏 Area Distribution:")
        for range_name, count in area_ranges.items():
            percentage = (count / len(areas)) * 100
            print(f"   {range_name}: {count} properties ({percentage:.1f}%)")
    
    def price_per_sqft_analysis(self):
        """Analyze price per square foot"""
        psf_data = []
        
        for p in self.properties:
            if 'price' in p and 'area' in p and p['area'] > 0:
                psf = p['price'] / p['area']
                psf_data.append(psf)
        
        if not psf_data:
            print("❌ No PSF data available")
            return
        
        print(f"\n💲 PRICE PER SQUARE FOOT ANALYSIS")
        print("-" * 30)
        print(f"Average PSF: S$ {statistics.mean(psf_data):,.0f}")
        print(f"Median PSF: S$ {statistics.median(psf_data):,.0f}")
        print(f"Lowest PSF: S$ {min(psf_data):,.0f}")
        print(f"Highest PSF: S$ {max(psf_data):,.0f}")
        
        # PSF ranges
        psf_ranges = {
            "Under S$ 1000/sqft": len([p for p in psf_data if p < 1000]),
            "S$ 1000-1500/sqft": len([p for p in psf_data if 1000 <= p < 1500]),
            "S$ 1500-2000/sqft": len([p for p in psf_data if 1500 <= p < 2000]),
            "S$ 2000-3000/sqft": len([p for p in psf_data if 2000 <= p < 3000]),
            "Above S$ 3000/sqft": len([p for p in psf_data if p >= 3000])
        }
        
        print(f"\nPSF Distribution:")
        for range_name, count in psf_ranges.items():
            percentage = (count / len(psf_data)) * 100
            print(f"   {range_name}: {count} properties ({percentage:.1f}%)")
    
    def market_segments(self):
        """Analyze market segments"""
        print(f"\n🎯 MARKET SEGMENTS")
        print("-" * 30)
        
        # Segment by bedroom and price
        segments = {
            "Affordable 1-2BR": [],
            "Mid-range 2-3BR": [],
            "Premium 3-4BR": [],
            "Luxury 4+BR": []
        }
        
        for p in self.properties:
            if 'price' in p and 'bedrooms' in p:
                price = p['price']
                beds = p['bedrooms']
                
                if beds <= 2 and price < 1500000:
                    segments["Affordable 1-2BR"].append(p)
                elif beds <= 3 and 1500000 <= price < 3000000:
                    segments["Mid-range 2-3BR"].append(p)
                elif beds <= 4 and 3000000 <= price < 8000000:
                    segments["Premium 3-4BR"].append(p)
                elif beds >= 4 and price >= 8000000:
                    segments["Luxury 4+BR"].append(p)
        
        for segment_name, properties in segments.items():
            if properties:
                avg_price = statistics.mean([p['price'] for p in properties])
                avg_area = statistics.mean([p['area'] for p in properties if 'area' in p])
                print(f"\n{segment_name}:")
                print(f"   Count: {len(properties)} properties")
                print(f"   Avg Price: S$ {avg_price:,.0f}")
                if avg_area:
                    print(f"   Avg Area: {avg_area:,.0f} sqft")
                    print(f"   Avg PSF: S$ {avg_price/avg_area:,.0f}")
    
    def find_best_deals(self):
        """Find properties with best value"""
        print(f"\n🏆 BEST VALUE PROPERTIES")
        print("-" * 30)
        
        # Calculate PSF for all properties
        properties_with_psf = []
        for p in self.properties:
            if 'price' in p and 'area' in p and p['area'] > 0:
                psf = p['price'] / p['area']
                p_copy = p.copy()
                p_copy['psf'] = psf
                properties_with_psf.append(p_copy)
        
        # Sort by PSF (lowest first)
        properties_with_psf.sort(key=lambda x: x['psf'])
        
        print("Top 5 Best Value Properties (Lowest PSF):")
        for i, p in enumerate(properties_with_psf[:5], 1):
            print(f"\n   {i}. Property ID: {p['id']}")
            print(f"      Price: S$ {p['price']:,}")
            print(f"      Bedrooms: {p['bedrooms']}")
            print(f"      Area: {p['area']:,} sqft")
            print(f"      PSF: S$ {p['psf']:,.0f}")
    
    def export_summary(self):
        """Export analysis summary to file"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        summary_file = f'property_analysis_summary_{timestamp}.txt'
        
        # This would contain the analysis summary
        # For now, just indicate completion
        print(f"\n📄 Analysis complete!")
        print(f"💾 Consider exporting detailed analysis to: {summary_file}")

def main():
    # Analyze the latest scraped data
    json_file = "smart_extraction_20250712_212608.json"
    
    analyzer = PropertyAnalyzer(json_file)
    analyzer.analyze_all()
    analyzer.find_best_deals()
    analyzer.export_summary()

if __name__ == "__main__":
    main()
