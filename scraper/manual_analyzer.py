"""
Manual HTML analyzer for PropertyGuru
Paste HTML content to analyze structure
"""
from bs4 import BeautifulSoup
import json
import re
from typing import Dict, List, Any


class PropertyGuruAnalyzer:
    """Analyze PropertyGuru HTML structure manually"""

    def analyze_html(self, html_content: str) -> Dict[str, Any]:
        """Analyze HTML content and extract property data structure"""
        soup = BeautifulSoup(html_content, 'html.parser')

        analysis = {
            "page_info": self._get_page_info(soup),
            "property_cards": self._find_property_cards(soup),
            "data_patterns": self._analyze_data_patterns(soup),
            "schema_suggestions": {}
        }

        # Generate schema suggestions based on findings
        analysis["schema_suggestions"] = self._generate_schema_suggestions(analysis)

        return analysis

    def _get_page_info(self, soup: BeautifulSoup) -> Dict[str, Any]:
        """Get basic page information"""
        return {
            "title": soup.title.string if soup.title else "No title",
            "meta_description": self._get_meta_content(soup, "description"),
            "meta_keywords": self._get_meta_content(soup, "keywords"),
            "total_text_length": len(soup.get_text()),
            "total_links": len(soup.find_all('a')),
            "total_images": len(soup.find_all('img'))
        }

    def _get_meta_content(self, soup: BeautifulSoup, name: str) -> str:
        """Get meta tag content"""
        meta = soup.find('meta', attrs={'name': name}) or soup.find('meta', attrs={'property': f'og:{name}'})
        return meta.get('content', '') if meta else ''

    def _find_property_cards(self, soup: BeautifulSoup) -> List[Dict[str, Any]]:
        """Find and analyze property cards"""
        cards = []

        # Look for elements that might be property cards
        potential_cards = []

        # Strategy 1: Find elements with property-related text
        price_elements = soup.find_all(text=re.compile(r'\$[\d,]+|\bS\$|\bSGD|psf|/mo'))
        for elem in price_elements:
            parent = elem.parent
            while parent and parent.name not in ['body', 'html']:
                text_length = len(parent.get_text(strip=True))
                if 50 < text_length < 2000:  # Reasonable card size
                    potential_cards.append(parent)
                    break
                parent = parent.parent

        # Strategy 2: Find elements with bedroom/bathroom info
        room_elements = soup.find_all(text=re.compile(r'\d+\s*(bed|bath|room)', re.I))
        for elem in room_elements:
            parent = elem.parent
            while parent and parent.name not in ['body', 'html']:
                text_length = len(parent.get_text(strip=True))
                if 50 < text_length < 2000:
                    potential_cards.append(parent)
                    break
                parent = parent.parent

        # Remove duplicates and analyze each card
        unique_cards = list(set(potential_cards))

        for i, card in enumerate(unique_cards[:10]):  # Analyze first 10
            card_data = self._analyze_card(card, i)
            if card_data:
                cards.append(card_data)

        return cards

    def _analyze_card(self, card_element, index: int) -> Dict[str, Any]:
        """Analyze individual property card"""
        try:
            text = card_element.get_text(strip=True)

            card_data = {
                "index": index,
                "tag": card_element.name,
                "classes": card_element.get('class', []),
                "id": card_element.get('id', ''),
                "text_length": len(text),
                "text_sample": text[:200] + "..." if len(text) > 200 else text,
                "extracted_data": {}
            }

            # Extract specific data patterns
            card_data["extracted_data"] = {
                "prices": self._extract_prices(text),
                "rooms": self._extract_rooms(text),
                "area": self._extract_area(text),
                "location": self._extract_location(text),
                "property_type": self._extract_property_type(text),
                "links": [a.get('href') for a in card_element.find_all('a', href=True)],
                "images": [img.get('src') or img.get('data-src') for img in card_element.find_all('img')]
            }

            return card_data
        except Exception as e:
            return {"error": str(e), "index": index}

    def _extract_prices(self, text: str) -> List[str]:
        """Extract price information with enhanced patterns"""
        price_patterns = [
            r'S\$\s*[\d,]+(?:\.\d{2})?(?!\d)',  # S$ followed by numbers, not more digits
            r'\$\s*[\d,]+(?:\.\d{2})?(?!\d)',   # $ followed by numbers, not more digits
            r'SGD\s*[\d,]+(?!\d)',              # SGD followed by numbers
            r'[\d,]+\s*/\s*psf',                # Price per sqft
            r'[\d,]+\s*/\s*mo(?:nth)?',         # Monthly rent
            r'(?:Price|price):\s*S\$\s*[\d,]+', # "Price: S$" format
            r'(?:From|from)\s*S\$\s*[\d,]+',    # "From S$" format
        ]

        prices = []
        for pattern in price_patterns:
            matches = re.findall(pattern, text, re.I)
            prices.extend(matches)

        # Clean up prices - remove duplicates and sort by length (longer = more specific)
        unique_prices = list(set(prices))
        unique_prices.sort(key=len, reverse=True)

        return unique_prices[:5]  # Return top 5 most specific prices

    def _extract_rooms(self, text: str) -> Dict[str, List[str]]:
        """Extract room information"""
        patterns = {
            "bedrooms": r'(\d+)\s*(?:bed|bedroom)s?',
            "bathrooms": r'(\d+)\s*(?:bath|bathroom)s?',
            "rooms": r'(\d+)\s*rooms?'
        }

        rooms = {}
        for room_type, pattern in patterns.items():
            matches = re.findall(pattern, text, re.I)
            rooms[room_type] = matches

        return rooms

    def _extract_area(self, text: str) -> List[str]:
        """Extract area information"""
        area_patterns = [
            r'[\d,]+\s*(?:sq\s*ft|sqft|square\s*feet)',
            r'[\d,]+\s*(?:sq\s*m|sqm|square\s*meters?)'
        ]

        areas = []
        for pattern in area_patterns:
            matches = re.findall(pattern, text, re.I)
            areas.extend(matches)

        return areas

    def _extract_location(self, text: str) -> List[str]:
        """Extract location information"""
        location_patterns = [
            r'District\s*\d+',
            r'\b\d{6}\b',  # Postal codes
            r'[A-Z][a-z]+\s+(?:Road|Street|Avenue|Drive|Lane|Walk|Park|Gardens?)',
            r'(?:MRT|LRT)\s*[A-Z][a-z]+',
        ]

        locations = []
        for pattern in location_patterns:
            matches = re.findall(pattern, text)
            locations.extend(matches)

        return locations

    def _extract_property_type(self, text: str) -> List[str]:
        """Extract property type"""
        types = []
        property_types = ['HDB', 'Condo', 'Condominium', 'Landed', 'Apartment', 'Executive', 'Terrace', 'Semi-D', 'Bungalow']

        for prop_type in property_types:
            if prop_type.lower() in text.lower():
                types.append(prop_type)

        return types

    def _analyze_data_patterns(self, soup: BeautifulSoup) -> Dict[str, Any]:
        """Analyze overall data patterns on the page"""
        text = soup.get_text()

        return {
            "price_formats": list(set(re.findall(r'S\$\s*[\d,]+(?:\.\d{2})?|\$\s*[\d,]+', text)[:10])),
            "room_formats": list(set(re.findall(r'\d+\s*(?:bed|bath|room)s?', text, re.I)[:10])),
            "area_formats": list(set(re.findall(r'[\d,]+\s*(?:sq\s*ft|sqft)', text, re.I)[:10])),
            "common_classes": self._get_common_classes(soup),
            "data_attributes": self._get_data_attributes(soup)
        }

    def _get_common_classes(self, soup: BeautifulSoup) -> List[str]:
        """Get most common CSS classes"""
        all_classes = []
        for elem in soup.find_all(class_=True):
            all_classes.extend(elem.get('class', []))

        # Count frequency
        class_counts = {}
        for cls in all_classes:
            class_counts[cls] = class_counts.get(cls, 0) + 1

        # Return top 20 most common
        return sorted(class_counts.items(), key=lambda x: x[1], reverse=True)[:20]

    def _get_data_attributes(self, soup: BeautifulSoup) -> List[str]:
        """Get data-* attributes"""
        data_attrs = set()
        for elem in soup.find_all():
            for attr in elem.attrs:
                if attr.startswith('data-'):
                    data_attrs.add(attr)

        return list(data_attrs)[:20]

    def _generate_schema_suggestions(self, analysis: Dict[str, Any]) -> Dict[str, Any]:
        """Generate database schema suggestions based on analysis"""
        suggestions = {
            "required_fields": [],
            "optional_fields": [],
            "data_types": {},
            "indexes_needed": []
        }

        # Analyze found data patterns
        cards = analysis.get("property_cards", [])
        if cards:
            # Required fields based on what we found
            common_fields = ["title", "price", "address", "property_type"]
            suggestions["required_fields"] = common_fields

            # Optional fields
            optional_fields = ["bedrooms", "bathrooms", "floor_area_sqft", "images", "agent_info"]
            suggestions["optional_fields"] = optional_fields

            # Data types
            suggestions["data_types"] = {
                "price": "DECIMAL(12,2)",
                "bedrooms": "INTEGER",
                "bathrooms": "INTEGER",
                "floor_area_sqft": "INTEGER",
                "images": "JSON",
                "amenities": "JSON"
            }

            # Indexes
            suggestions["indexes_needed"] = ["price", "bedrooms", "property_type", "district"]

        return suggestions


def main():
    """Main function for manual analysis"""
    print("PropertyGuru Manual HTML Analyzer")
    print("=" * 40)
    print("Instructions:")
    print("1. Go to PropertyGuru in your browser")
    print("2. Navigate to search results page")
    print("3. Right-click -> View Page Source")
    print("4. Copy the HTML content")
    print("5. Save it as 'sample_page.html' in this directory")
    print("6. Run this script again")
    print()

    # Try to read sample HTML file
    try:
        with open('sample_page.html', 'r', encoding='utf-8') as f:
            html_content = f.read()

        print("✅ Found sample_page.html, analyzing...")

        analyzer = PropertyGuruAnalyzer()
        results = analyzer.analyze_html(html_content)

        # Save results
        with open('analysis_results.json', 'w') as f:
            json.dump(results, f, indent=2, default=str)

        # Print summary
        print(f"\n📊 ANALYSIS RESULTS:")
        print(f"- Page title: {results['page_info']['title']}")
        print(f"- Property cards found: {len(results['property_cards'])}")
        print(f"- Common price formats: {results['data_patterns']['price_formats'][:3]}")
        print(f"- Common room formats: {results['data_patterns']['room_formats'][:3]}")

        print(f"\n💾 Detailed results saved to: analysis_results.json")

        # Print schema suggestions
        schema = results['schema_suggestions']
        print(f"\n🗄️  DATABASE SCHEMA SUGGESTIONS:")
        print(f"Required fields: {schema.get('required_fields', [])}")
        print(f"Optional fields: {schema.get('optional_fields', [])}")
        print(f"Suggested indexes: {schema.get('indexes_needed', [])}")

        return results

    except FileNotFoundError:
        print("❌ sample_page.html not found")
        print("Please save PropertyGuru page source as 'sample_page.html' and run again")
        return None


if __name__ == "__main__":
    main()
