"""
Simple Google-Authenticated Selenium Driver for PropertyGuru Scraper
Uses your Google account (<EMAIL>) to bypass Cloudflare
"""

import time
import logging
import tempfile
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from webdriver_manager.chrome import ChromeDriverManager

logger = logging.getLogger(__name__)


class SimpleGoogleAuth:
    """Simple Google authentication for PropertyGuru scraping"""
    
    def __init__(self, headless: bool = False):
        self.driver = None
        self.headless = headless
        self.google_email = "<EMAIL>"
        self.authenticated = False
        
    def create_driver(self) -> webdriver.Chrome:
        """Create Chrome WebDriver with fresh profile"""
        try:
            options = Options()
            
            # Create fresh temporary profile
            temp_dir = tempfile.mkdtemp(prefix="chrome_scraper_")
            options.add_argument(f"--user-data-dir={temp_dir}")
            
            # Basic settings
            if self.headless:
                options.add_argument("--headless=new")
            
            options.add_argument("--window-size=1920,1080")
            options.add_argument("--start-maximized")
            options.add_argument("--no-sandbox")
            options.add_argument("--disable-dev-shm-usage")
            
            # Minimal automation hiding
            options.add_experimental_option("excludeSwitches", ["enable-automation"])
            options.add_experimental_option('useAutomationExtension', False)
            
            # Create driver
            service = Service(ChromeDriverManager().install())
            driver = webdriver.Chrome(service=service, options=options)
            
            # Set timeouts
            driver.implicitly_wait(10)
            driver.set_page_load_timeout(60)
            
            self.driver = driver
            logger.info("✅ Chrome WebDriver created successfully")
            return driver
            
        except Exception as e:
            logger.error(f"❌ Failed to create WebDriver: {e}")
            raise
    
    def login_to_google(self) -> bool:
        """Login to Google account"""
        try:
            logger.info(f"🔐 Starting Google login for: {self.google_email}")
            
            # Navigate to Google login
            self.driver.get("https://accounts.google.com/signin")
            time.sleep(3)
            
            # Enter email
            try:
                email_input = WebDriverWait(self.driver, 15).until(
                    EC.element_to_be_clickable((By.ID, "identifierId"))
                )
                email_input.clear()
                email_input.send_keys(self.google_email)
                
                # Click Next
                next_button = self.driver.find_element(By.ID, "identifierNext")
                next_button.click()
                time.sleep(3)
                
                logger.info("✅ Email entered successfully")
                
                # Prompt user for password
                print(f"\n🔐 Google Login Required")
                print("=" * 50)
                print(f"📧 Account: {self.google_email}")
                print("📝 Please complete the login process:")
                print("   1. Enter your password in the browser")
                print("   2. Complete 2FA if prompted")
                print("   3. Wait for successful login")
                print("   4. Return here and press Enter")
                print()
                
                input("⏳ Press Enter after completing Google login...")
                
                # Verify login
                time.sleep(2)
                if self.verify_login():
                    self.authenticated = True
                    logger.info("✅ Google authentication successful!")
                    return True
                else:
                    logger.error("❌ Google authentication failed")
                    return False
                    
            except TimeoutException:
                logger.error("❌ Could not find Google login form")
                return False
                
        except Exception as e:
            logger.error(f"❌ Google login error: {e}")
            return False
    
    def verify_login(self) -> bool:
        """Verify that Google login was successful"""
        try:
            # Check current URL
            current_url = self.driver.current_url
            
            # If we're still on accounts.google.com, check for profile indicators
            if "accounts.google.com" in current_url:
                # Look for profile picture or account menu
                profile_selectors = [
                    "img[alt*='profile']",
                    "[data-testid='avatar']",
                    ".gb_d",  # Google account button
                    "[aria-label*='Account']"
                ]
                
                for selector in profile_selectors:
                    try:
                        element = self.driver.find_element(By.CSS_SELECTOR, selector)
                        if element.is_displayed():
                            logger.info("✅ Found profile indicator - login successful")
                            return True
                    except NoSuchElementException:
                        continue
            
            # Try navigating to Google to verify
            self.driver.get("https://www.google.com")
            time.sleep(3)
            
            # Look for sign-in button (if present, not logged in)
            try:
                sign_in = self.driver.find_element(By.XPATH, "//a[contains(text(), 'Sign in')]")
                if sign_in.is_displayed():
                    logger.warning("⚠️ Still see 'Sign in' button - may not be logged in")
                    return False
            except NoSuchElementException:
                # No sign-in button found, likely logged in
                logger.info("✅ No 'Sign in' button found - likely logged in")
                return True
            
            return True
            
        except Exception as e:
            logger.debug(f"Login verification error: {e}")
            return True  # Assume success if we can't verify
    
    def navigate_to_propertyguru(self, url: str) -> bool:
        """Navigate to PropertyGuru with Google session"""
        try:
            logger.info("🌐 Navigating to PropertyGuru with Google session...")
            
            # Ensure we're logged into Google
            if not self.authenticated:
                logger.info("🔐 Google authentication required...")
                if not self.login_to_google():
                    logger.error("❌ Google authentication failed")
                    return False
            
            # Simulate realistic browsing pattern
            logger.info("🔍 Simulating realistic browsing...")
            
            # Start from Google
            self.driver.get("https://www.google.com")
            time.sleep(2)
            
            # Search for PropertyGuru
            try:
                search_box = self.driver.find_element(By.NAME, "q")
                search_box.clear()
                search_box.send_keys("PropertyGuru Singapore property for sale")
                search_box.send_keys(Keys.RETURN)
                time.sleep(3)
                
                # Click on PropertyGuru result
                try:
                    pg_link = WebDriverWait(self.driver, 10).until(
                        EC.element_to_be_clickable((By.XPATH, "//a[contains(@href, 'propertyguru.com')]"))
                    )
                    pg_link.click()
                    time.sleep(5)
                    logger.info("✅ Clicked PropertyGuru from search results")
                except TimeoutException:
                    logger.info("🔗 Direct navigation to PropertyGuru...")
                    self.driver.get("https://www.propertyguru.com.sg")
                    time.sleep(5)
                    
            except NoSuchElementException:
                logger.info("🔗 Direct navigation to PropertyGuru...")
                self.driver.get("https://www.propertyguru.com.sg")
                time.sleep(5)
            
            # Now navigate to target URL
            logger.info(f"🎯 Navigating to target URL...")
            self.driver.get(url)
            time.sleep(5)
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Navigation error: {e}")
            return False
    
    def check_for_blocking(self) -> bool:
        """Check if we're being blocked"""
        try:
            page_title = self.driver.title.lower()
            page_source = self.driver.page_source.lower()
            
            blocking_indicators = [
                "attention required",
                "cloudflare",
                "access denied",
                "blocked",
                "captcha",
                "security check"
            ]
            
            for indicator in blocking_indicators:
                if indicator in page_title or indicator in page_source:
                    logger.warning(f"🚫 Blocking detected: {indicator}")
                    return True
            
            # Check for property listings
            try:
                WebDriverWait(self.driver, 10).until(
                    EC.presence_of_element_located((By.CSS_SELECTOR, 
                        "[data-testid='listing-card'], .listing-card, .property-card, .search-result"))
                )
                logger.info("✅ Property listings found - no blocking detected")
                return False
            except TimeoutException:
                logger.warning("⚠️ No property listings found")
                return True
                
        except Exception as e:
            logger.error(f"Error checking blocking: {e}")
            return True
    
    def close(self):
        """Close the WebDriver"""
        if self.driver:
            try:
                self.driver.quit()
                logger.info("🔒 WebDriver closed")
            except Exception as e:
                logger.error(f"Error closing WebDriver: {e}")


def test_google_auth():
    """Test Google authentication"""
    print("🧪 Testing Google Authentication")
    print("=" * 40)
    
    auth = SimpleGoogleAuth(headless=False)
    
    try:
        # Create driver
        driver = auth.create_driver()
        print("✅ Driver created")
        
        # Test Google login
        if auth.login_to_google():
            print("✅ Google login successful")
            
            # Test PropertyGuru navigation
            test_url = "https://www.propertyguru.com.sg/property-for-sale"
            if auth.navigate_to_propertyguru(test_url):
                print("✅ PropertyGuru navigation successful")
                
                # Check for blocking
                if not auth.check_for_blocking():
                    print("🎉 SUCCESS: No blocking detected!")
                    print("🚀 Ready for scraping!")
                else:
                    print("❌ Still being blocked")
            else:
                print("❌ PropertyGuru navigation failed")
        else:
            print("❌ Google login failed")
        
        input("\n⏳ Press Enter to close...")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
    finally:
        auth.close()


if __name__ == "__main__":
    test_google_auth()
