# 🚀 Comprehensive PropertyGuru Scraper

## Overview

The Comprehensive PropertyGuru Scraper is an advanced web scraping solution designed to extract all **52,383 properties** from PropertyGuru Singapore across all districts (D01-D28). This scraper implements sophisticated anti-detection measures, VPN integration, and comprehensive data extraction capabilities.

## 🎯 Key Features

### 🔒 Advanced Anti-Detection
- **SurfShark VPN Integration**: Automatic IP rotation across 5 server locations
- **Stealth WebDriver**: Advanced browser fingerprinting evasion
- **Human Behavior Simulation**: Random mouse movements, scrolling patterns, and delays
- **Session Rotation**: Intelligent session management to avoid detection
- **User Agent Rotation**: Dynamic user agent switching

### 📊 Comprehensive Data Extraction
- **60+ Fields Per Property** (vs 20 in basic scraper)
- **Individual Property Page Scraping** for detailed information
- **Agent Contact Details**: Name, phone, CEA number, agency
- **Building Specifications**: Developer, architect, facilities, tenure
- **Location Intelligence**: MRT distance, nearby schools, amenities
- **Media Content**: Image galleries, virtual tours, floor plans
- **Market Context**: Comparable sales, price trends

### 🛡️ Robust Error Handling
- **Retry Mechanisms**: Automatic retry with exponential backoff
- **Cloudflare Detection**: Multiple bypass strategies
- **Session Recovery**: Automatic session restoration on failures
- **Data Validation**: Quality scoring and validation for all extracted data

## 🏗️ Architecture

```
ComprehensivePropertyGuruScraper
├── VPNManager                    # SurfShark VPN CLI integration
├── StealthWebDriver             # Advanced anti-detection WebDriver
├── PropertyData                 # Comprehensive data structure (60+ fields)
├── Data Extraction Pipeline     # Multi-level scraping strategy
│   ├── Level 1: Search Results  # Property listing pages
│   ├── Level 2: Property Details # Individual property pages
│   └── Level 3: Market Context  # Additional market data
└── Results Processing           # Quality scoring and JSON output
```

## 🚀 Quick Start

### 1. Installation

```bash
# Install dependencies
pip install -r requirements.txt

# Optional: Install SurfShark VPN CLI for IP rotation
# Follow SurfShark documentation for CLI setup
```

### 2. Basic Usage

```bash
# Quick test (2 pages, no VPN, visible browser)
python run_comprehensive_scraper.py --config test

# Development run (10 pages, with VPN, headless)
python run_comprehensive_scraper.py --config development

# Full production scrape (unlimited pages, all features)
python run_comprehensive_scraper.py --config production
```

### 3. Custom Configuration

```bash
# Custom scraping with specific parameters
python run_comprehensive_scraper.py \
    --pages 20 \
    --vpn \
    --headless \
    --delay-min 5 \
    --delay-max 15 \
    --session-duration 45
```

## ⚙️ Configuration Options

### Predefined Configurations

| Config | Pages | VPN | Headless | Delay Range | Session Duration |
|--------|-------|-----|----------|-------------|------------------|
| **test** | 2 | No | No | 2-5s | 10 min |
| **development** | 10 | Yes | Yes | 5-12s | 30 min |
| **production** | Unlimited | Yes | Yes | 8-20s | 60 min |

### Custom Parameters

- `--pages`: Maximum pages to scrape (default: unlimited for production)
- `--vpn` / `--no-vpn`: Enable/disable VPN rotation
- `--headless` / `--visible`: Browser display mode
- `--delay-min` / `--delay-max`: Request delay range in seconds
- `--session-duration`: Session duration before rotation (minutes)

## 🔒 VPN Integration

### SurfShark VPN CLI Setup

1. **Install SurfShark VPN CLI**:
   ```bash
   # Follow SurfShark documentation for your OS
   # https://support.surfshark.com/hc/en-us/articles/360011051133
   ```

2. **Configure Authentication**:
   ```bash
   surfshark-vpn login
   ```

3. **Available Server Locations**:
   - Singapore (`sg-sng.prod.surfshark.com`)
   - Malaysia (`my-kul.prod.surfshark.com`)
   - Thailand (`th-bkk.prod.surfshark.com`)
   - Indonesia (`id-jkt.prod.surfshark.com`)
   - Philippines (`ph-mnl.prod.surfshark.com`)

### VPN Features

- **Automatic IP Rotation**: Changes IP every session or on detection
- **Connection Monitoring**: Automatic reconnection on failures
- **Geographic Distribution**: Rotates across Southeast Asian servers
- **Session Isolation**: Each scraping session uses different IP

## 📊 Data Structure

### PropertyData Fields (60+ fields)

```python
@dataclass
class PropertyData:
    # Basic Information (8 fields)
    property_id, title, description, property_type, property_subtype,
    listing_type, status, listing_date
    
    # Pricing Intelligence (7 fields)
    price, price_psf, original_price, price_change_percentage,
    maintenance_fee, rental_yield, days_on_market
    
    # Property Specifications (12 fields)
    bedrooms, bathrooms, floor_area_sqft, land_area_sqft, balcony_area_sqft,
    unit_number, floor_level, total_floors, facing_direction, furnishing,
    parking_lots, built_year
    
    # Building & Development (8 fields)
    building_name, developer_name, architect, total_units, tenure,
    top_date, facilities, security_features
    
    # Location Intelligence (10 fields)
    address, postal_code, district, subzone, planning_area,
    latitude, longitude, nearest_mrt, mrt_distance_m, mrt_walking_time
    
    # Market Context (6 fields)
    nearby_schools, nearby_malls, nearby_hospitals, bus_stops,
    comparable_sales, market_trends
    
    # Agent & Media (8 fields)
    agent_id, agent_name, agent_phone, agent_cea_number, agent_agency,
    images, virtual_tour_url, floor_plan_images
    
    # Metadata (3 fields)
    scraped_at, data_quality_score, source_url
```

## 🧪 Testing

### Run Test Suite

```bash
# Run comprehensive test suite
python tests/test_comprehensive_scraper.py

# Run with pytest for detailed output
pytest tests/test_comprehensive_scraper.py -v
```

### Test Coverage

- **VPN Manager**: Connection, disconnection, server rotation
- **Stealth WebDriver**: Anti-detection measures, human behavior simulation
- **Data Extraction**: Price parsing, property specifications, location intelligence
- **Quality Scoring**: Data completeness and validation
- **Session Management**: Rotation logic, error handling

## 📈 Performance Metrics

### Expected Performance

| Metric | Test Config | Development Config | Production Config |
|--------|-------------|-------------------|-------------------|
| **Pages** | 2 | 10 | Unlimited |
| **Properties** | ~40 | ~200 | 52,383 |
| **Duration** | 5-10 min | 30-60 min | 24-48 hours |
| **Data Quality** | 60-70% | 70-80% | 80-95% |
| **Success Rate** | 90-95% | 85-90% | 80-85% |

### Optimization Features

- **Parallel Processing**: Multiple extraction pipelines
- **Intelligent Caching**: Avoid re-scraping unchanged properties
- **Progressive Enhancement**: Basic data first, detailed data second
- **Memory Management**: Efficient data structures and garbage collection

## 🚨 Important Notes

### Legal Considerations

- **Respect robots.txt**: Always check and comply with site policies
- **Rate Limiting**: Built-in delays to avoid overwhelming servers
- **Data Usage**: Ensure compliance with data protection regulations
- **Commercial Use**: Verify licensing requirements for commercial applications

### Technical Requirements

- **Python 3.8+**: Required for modern async/await syntax
- **Chrome/Chromium**: Required for Selenium WebDriver
- **Memory**: Minimum 4GB RAM for large-scale scraping
- **Storage**: ~1GB for complete Singapore property database
- **Network**: Stable internet connection, preferably high-speed

### Troubleshooting

1. **Cloudflare Blocking**: Try VPN rotation or longer delays
2. **Memory Issues**: Reduce batch size or enable data streaming
3. **VPN Connection Failures**: Check SurfShark CLI configuration
4. **WebDriver Crashes**: Update Chrome and ChromeDriver versions

## 📞 Support

For issues, improvements, or questions:

1. **Check Logs**: Review `comprehensive_scraper.log` for detailed error information
2. **Test Configuration**: Start with `test` config to verify setup
3. **Update Dependencies**: Ensure all packages are up to date
4. **Monitor Resources**: Check CPU, memory, and network usage

## 🔮 Future Enhancements

- **Distributed Scraping**: Multi-server deployment for faster processing
- **Real-time Monitoring**: Live dashboard for scraping progress
- **Machine Learning**: Intelligent property categorization and valuation
- **API Integration**: Direct integration with property management systems
- **Mobile App**: Companion app for real-time property alerts
