"""
Google-Authenticated Selenium Driver for PropertyGuru Scraper
Uses Google account (<EMAIL>) to appear as legitimate user
"""

import os
import time
import logging
import tempfile
from pathlib import Path
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from webdriver_manager.chrome import ChromeDriverManager

logger = logging.getLogger(__name__)


class GoogleAuthenticatedDriver:
    """
    Selenium WebDriver that uses Google account authentication
    to maintain logged-in session and appear as legitimate user
    """

    def __init__(self, headless: bool = False, google_email: str = "<EMAIL>"):
        self.driver = None
        self.headless = headless
        self.google_email = google_email
        self.authenticated = False
        
    def login_to_google(self) -> bool:
        """Login to Google with the specified account"""
        try:
            logger.info(f"🔐 Logging into Google account: {self.google_email}")

            # Navigate to Google login
            self.driver.get("https://accounts.google.com/signin")
            time.sleep(3)

            # Enter email
            try:
                email_input = WebDriverWait(self.driver, 10).until(
                    EC.presence_of_element_located((By.ID, "identifierId"))
                )
                email_input.clear()
                email_input.send_keys(self.google_email)

                # Click Next
                next_button = self.driver.find_element(By.ID, "identifierNext")
                next_button.click()
                time.sleep(3)

                logger.info("✅ Email entered, waiting for password...")

                # Wait for password field and prompt user
                print(f"\n🔐 Google Login Required")
                print("=" * 40)
                print(f"Account: {self.google_email}")
                print("1. Enter your password in the browser window")
                print("2. Complete any 2FA if required")
                print("3. Wait for successful login")
                print("4. Return here and press Enter")

                input("\n⏳ Press Enter after completing Google login...")

                # Verify login success
                time.sleep(2)
                if self.verify_google_session():
                    self.authenticated = True
                    logger.info("✅ Google authentication successful")
                    return True
                else:
                    logger.error("❌ Google authentication failed")
                    return False

            except TimeoutException:
                logger.error("❌ Could not find Google login elements")
                return False

        except Exception as e:
            logger.error(f"Google login failed: {e}")
            return False
    
    def create_driver(self) -> webdriver.Chrome:
        """Create Chrome WebDriver with Google authentication"""
        try:
            options = Options()
            
            # Use existing Chrome profile for Google authentication
            if self.use_existing_profile:
                profile_path = self.get_chrome_profile_path()
                if profile_path:
                    # Create a temporary copy to avoid conflicts
                    import tempfile
                    import shutil

                    temp_dir = tempfile.mkdtemp(prefix="chrome_profile_")
                    temp_profile = Path(temp_dir) / "Default"

                    try:
                        # Copy essential profile files for authentication
                        essential_files = [
                            "Cookies", "Login Data", "Web Data", "Preferences",
                            "Local State", "Secure Preferences"
                        ]

                        temp_profile.mkdir(parents=True, exist_ok=True)

                        for file_name in essential_files:
                            src_file = Path(profile_path) / file_name
                            if src_file.exists():
                                shutil.copy2(src_file, temp_profile / file_name)

                        options.add_argument(f"--user-data-dir={temp_dir}")
                        options.add_argument("--profile-directory=Default")
                        logger.info(f"Using temporary Chrome profile: {temp_dir}")

                    except Exception as e:
                        logger.warning(f"Failed to copy profile, using fresh profile: {e}")
                        # Use a fresh temporary profile
                        options.add_argument(f"--user-data-dir={temp_dir}")
                else:
                    logger.warning("Using fresh Chrome profile")
            
            # Basic options for legitimate browsing
            if self.headless:
                options.add_argument("--headless=new")
            
            # Realistic browser settings
            options.add_argument("--window-size=1920,1080")
            options.add_argument("--start-maximized")
            
            # Disable automation indicators (but keep it subtle)
            options.add_experimental_option("excludeSwitches", ["enable-automation"])
            options.add_experimental_option('useAutomationExtension', False)
            
            # Allow notifications and location (like normal browsing)
            options.add_argument("--disable-web-security")
            options.add_argument("--allow-running-insecure-content")
            
            # Performance optimizations
            options.add_argument("--no-sandbox")
            options.add_argument("--disable-dev-shm-usage")
            
            # Create service
            service = Service(ChromeDriverManager().install())
            
            # Create driver
            driver = webdriver.Chrome(service=service, options=options)
            
            # Set realistic timeouts
            driver.implicitly_wait(10)
            driver.set_page_load_timeout(60)  # Longer timeout for authenticated sessions
            
            self.driver = driver
            logger.info("Google-authenticated WebDriver created successfully")
            return driver
            
        except Exception as e:
            logger.error(f"Failed to create Google-authenticated WebDriver: {e}")
            raise
    
    def verify_google_session(self) -> bool:
        """Verify that Google session is active"""
        try:
            logger.info("🔍 Verifying Google authentication...")
            
            # Navigate to Google to check authentication
            self.driver.get("https://accounts.google.com")
            time.sleep(3)
            
            # Check if we're logged in
            try:
                # Look for profile picture or account menu
                profile_elements = [
                    "//img[@data-testid='avatar']",
                    "//div[@data-ved]//img",
                    "//a[@aria-label='Google Account']",
                    "//div[contains(@class, 'gb_d')]"
                ]
                
                for xpath in profile_elements:
                    try:
                        element = self.driver.find_element(By.XPATH, xpath)
                        if element.is_displayed():
                            logger.info("✅ Google session verified - user is logged in")
                            return True
                    except NoSuchElementException:
                        continue
                
                # Check if we see sign-in button (means not logged in)
                try:
                    sign_in = self.driver.find_element(By.XPATH, "//a[contains(text(), 'Sign in')]")
                    if sign_in.is_displayed():
                        logger.warning("⚠️ Not logged into Google - will prompt for login")
                        return False
                except NoSuchElementException:
                    pass
                
                logger.info("✅ Google session appears active")
                return True
                
            except Exception as e:
                logger.debug(f"Google session check error: {e}")
                return True  # Assume it's working
                
        except Exception as e:
            logger.error(f"Error verifying Google session: {e}")
            return False
    
    def prompt_google_login(self) -> bool:
        """Prompt user to log into Google if needed"""
        try:
            logger.info("🔐 Google login required...")
            
            print("\n🔐 Google Authentication Required")
            print("=" * 50)
            print("To bypass Cloudflare protection, please log into your Google account:")
            print()
            print("1. A Chrome browser window will open")
            print("2. Log into your Google account if not already logged in")
            print("3. Keep the browser window open")
            print("4. Return here and press Enter to continue")
            print()
            
            # Navigate to Google login
            self.driver.get("https://accounts.google.com/signin")
            
            input("⏳ Press Enter after logging into Google...")
            
            # Verify login
            if self.verify_google_session():
                logger.info("✅ Google authentication successful")
                return True
            else:
                logger.error("❌ Google authentication failed")
                return False
                
        except Exception as e:
            logger.error(f"Google login prompt failed: {e}")
            return False
    
    def navigate_to_propertyguru(self, url: str) -> bool:
        """Navigate to PropertyGuru with Google authentication"""
        try:
            logger.info("🌐 Navigating to PropertyGuru with Google session...")
            
            # First, ensure we have Google session
            if not self.verify_google_session():
                if not self.prompt_google_login():
                    return False
            
            # Add some realistic browsing behavior
            logger.info("🔍 Simulating realistic browsing pattern...")
            
            # Visit Google first (like normal browsing)
            self.driver.get("https://www.google.com")
            time.sleep(2)
            
            # Search for PropertyGuru (like normal user)
            try:
                search_box = self.driver.find_element(By.NAME, "q")
                search_box.send_keys("PropertyGuru Singapore")
                search_box.submit()
                time.sleep(3)
                
                # Click on PropertyGuru result
                try:
                    pg_link = self.driver.find_element(By.XPATH, "//a[contains(@href, 'propertyguru.com')]")
                    pg_link.click()
                    time.sleep(5)
                except NoSuchElementException:
                    # Direct navigation if search doesn't work
                    logger.info("Direct navigation to PropertyGuru...")
                    self.driver.get("https://www.propertyguru.com.sg")
                    time.sleep(5)
                    
            except NoSuchElementException:
                # Direct navigation fallback
                logger.info("Direct navigation to PropertyGuru...")
                self.driver.get("https://www.propertyguru.com.sg")
                time.sleep(5)
            
            # Now navigate to target URL
            logger.info(f"🎯 Navigating to target URL...")
            self.driver.get(url)
            time.sleep(5)
            
            return True
            
        except Exception as e:
            logger.error(f"Error navigating to PropertyGuru: {e}")
            return False
    
    def check_for_blocking(self) -> bool:
        """Check if we're being blocked by Cloudflare"""
        try:
            page_title = self.driver.title.lower()
            page_source = self.driver.page_source.lower()
            
            blocking_indicators = [
                "attention required",
                "cloudflare",
                "access denied",
                "blocked",
                "captcha",
                "security check",
                "bot detection"
            ]
            
            for indicator in blocking_indicators:
                if indicator in page_title or indicator in page_source:
                    logger.warning(f"🚫 Blocking detected: {indicator}")
                    return True
            
            # Check if we can find property listings
            try:
                WebDriverWait(self.driver, 10).until(
                    EC.presence_of_element_located((By.CSS_SELECTOR, 
                        "[data-testid='listing-card'], .listing-card, .property-card, .search-result"))
                )
                logger.info("✅ Property listings found - no blocking detected")
                return False
            except TimeoutException:
                logger.warning("⚠️ No property listings found - possible blocking")
                return True
                
        except Exception as e:
            logger.error(f"Error checking for blocking: {e}")
            return True
    
    def close(self):
        """Close the WebDriver"""
        if self.driver:
            try:
                self.driver.quit()
                logger.info("Google-authenticated WebDriver closed")
            except Exception as e:
                logger.error(f"Error closing WebDriver: {e}")


def test_google_auth_driver():
    """Test the Google-authenticated driver"""
    print("🧪 Testing Google-Authenticated Driver")
    print("=" * 40)
    
    driver_manager = GoogleAuthenticatedDriver(headless=False)
    
    try:
        # Create driver
        driver = driver_manager.create_driver()
        print("✅ Driver created successfully")
        
        # Test Google session
        if driver_manager.verify_google_session():
            print("✅ Google session verified")
        else:
            print("⚠️ Google session not verified")
        
        # Test PropertyGuru navigation
        test_url = "https://www.propertyguru.com.sg/property-for-sale"
        if driver_manager.navigate_to_propertyguru(test_url):
            print("✅ PropertyGuru navigation successful")
            
            # Check for blocking
            if not driver_manager.check_for_blocking():
                print("✅ No blocking detected - ready for scraping!")
            else:
                print("❌ Blocking detected")
        else:
            print("❌ PropertyGuru navigation failed")
        
        input("\n⏳ Press Enter to close browser...")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
    finally:
        driver_manager.close()


if __name__ == "__main__":
    test_google_auth_driver()
