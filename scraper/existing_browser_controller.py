"""
Existing Browser Controller for PropertyGuru Scraper
Takes control of your existing Chrome browser to avoid detection
"""

import time
import json
import logging
import requests
import websocket
from typing import Dict, List, Any, Optional
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC

logger = logging.getLogger(__name__)


class ExistingBrowserController:
    """
    Controller that connects to your existing Chrome browser
    Uses Chrome DevTools Protocol to avoid opening new windows
    """
    
    def __init__(self):
        self.driver = None
        self.debug_port = 9222
        self.connected = False
        
    def connect_to_existing_browser(self) -> bool:
        """Connect to existing Chrome browser with remote debugging"""
        try:
            logger.info("🔗 Attempting to connect to existing Chrome browser...")
            
            # First, try to connect to existing debugging session
            if self.try_existing_debug_session():
                return True
            
            # If no existing session, try to connect via Selenium with existing browser
            return self.connect_via_selenium()
            
        except Exception as e:
            logger.error(f"❌ Failed to connect to existing browser: {e}")
            return False
    
    def try_existing_debug_session(self) -> bool:
        """Try to connect to existing Chrome debug session"""
        try:
            # Check if Chrome is running with debug port
            response = requests.get(f"http://localhost:{self.debug_port}/json", timeout=5)
            
            if response.status_code == 200:
                tabs = response.json()
                logger.info(f"✅ Found {len(tabs)} Chrome tabs")
                
                # Find PropertyGuru tab or use first available
                target_tab = None
                for tab in tabs:
                    if "propertyguru" in tab.get("url", "").lower():
                        target_tab = tab
                        logger.info(f"🎯 Found PropertyGuru tab: {tab['title']}")
                        break
                
                if not target_tab and tabs:
                    target_tab = tabs[0]
                    logger.info(f"📄 Using first available tab: {target_tab['title']}")
                
                if target_tab:
                    # Connect via Selenium to the existing browser
                    return self.connect_to_debug_browser(target_tab)
                
            return False
            
        except requests.exceptions.ConnectionError:
            logger.info("ℹ️ No Chrome debug session found")
            return False
        except Exception as e:
            logger.debug(f"Debug session check failed: {e}")
            return False
    
    def connect_to_debug_browser(self, tab_info: Dict) -> bool:
        """Connect Selenium to existing Chrome debug session"""
        try:
            options = Options()
            options.add_experimental_option("debuggerAddress", f"localhost:{self.debug_port}")
            
            # Create driver connected to existing browser
            self.driver = webdriver.Chrome(options=options)
            
            # Switch to the target tab if needed
            current_url = self.driver.current_url
            target_url = tab_info.get("url", "")
            
            if target_url and target_url != current_url:
                self.driver.get(target_url)
                time.sleep(2)
            
            self.connected = True
            logger.info("✅ Successfully connected to existing Chrome browser")
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to connect to debug browser: {e}")
            return False
    
    def connect_via_selenium(self) -> bool:
        """Connect via Selenium to existing browser (fallback method)"""
        try:
            logger.info("🔄 Trying alternative connection method...")
            
            # This will prompt user to manually navigate
            print("\n🌐 Manual Browser Setup Required")
            print("=" * 50)
            print("Since we couldn't connect to your existing browser automatically,")
            print("please help us set up the connection:")
            print()
            print("1. In your existing Chrome browser:")
            print("   - Navigate to: chrome://settings/")
            print("   - Search for 'Remote debugging'")
            print("   - OR restart Chrome with: --remote-debugging-port=9222")
            print()
            print("2. Then navigate to PropertyGuru:")
            print("   https://www.propertyguru.com.sg/property-for-sale")
            print()
            print("3. Return here and we'll take control")
            
            response = input("\n⏳ Press Enter when ready, or 'skip' to use new browser: ").strip().lower()
            
            if response == 'skip':
                return self.create_new_stealth_browser()
            
            # Try debug connection again
            return self.try_existing_debug_session()
            
        except Exception as e:
            logger.error(f"❌ Alternative connection failed: {e}")
            return False
    
    def create_new_stealth_browser(self) -> bool:
        """Create new stealth browser as last resort"""
        try:
            logger.info("🕷️ Creating new stealth browser...")
            
            options = Options()
            options.add_argument("--start-maximized")
            options.add_argument("--disable-blink-features=AutomationControlled")
            options.add_experimental_option("excludeSwitches", ["enable-automation"])
            options.add_experimental_option('useAutomationExtension', False)
            
            self.driver = webdriver.Chrome(options=options)
            
            # Execute stealth script
            self.driver.execute_script("""
                Object.defineProperty(navigator, 'webdriver', {
                    get: () => undefined,
                });
            """)
            
            self.connected = True
            logger.info("✅ Stealth browser created")
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to create stealth browser: {e}")
            return False
    
    def navigate_to_propertyguru(self, url: str) -> bool:
        """Navigate to PropertyGuru URL"""
        try:
            if not self.connected:
                logger.error("❌ Not connected to browser")
                return False
            
            logger.info(f"🌐 Navigating to: {url}")
            self.driver.get(url)
            time.sleep(3)
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Navigation failed: {e}")
            return False
    
    def check_for_blocking(self) -> bool:
        """Check if we're being blocked"""
        try:
            page_title = self.driver.title.lower()
            page_source = self.driver.page_source.lower()
            
            blocking_indicators = [
                "attention required",
                "cloudflare",
                "access denied",
                "blocked",
                "captcha",
                "security check"
            ]
            
            for indicator in blocking_indicators:
                if indicator in page_title or indicator in page_source:
                    logger.warning(f"🚫 Blocking detected: {indicator}")
                    return True
            
            # Check for property listings
            try:
                WebDriverWait(self.driver, 10).until(
                    EC.presence_of_element_located((By.CSS_SELECTOR, 
                        "[data-testid='listing-card'], .listing-card, .property-card"))
                )
                logger.info("✅ Property listings found - no blocking detected")
                return False
            except:
                logger.warning("⚠️ No property listings found")
                return True
                
        except Exception as e:
            logger.error(f"Error checking blocking: {e}")
            return True
    
    def scroll_and_extract_properties(self) -> List[Dict[str, Any]]:
        """Scroll page and extract properties"""
        try:
            logger.info("📜 Starting property extraction with scrolling...")
            
            properties = []
            last_height = 0
            scroll_attempts = 0
            max_scrolls = 10
            
            while scroll_attempts < max_scrolls:
                # Extract properties from current view
                current_properties = self.extract_visible_properties()
                
                # Add new properties (avoid duplicates)
                for prop in current_properties:
                    if prop not in properties:
                        properties.append(prop)
                
                logger.info(f"📊 Found {len(properties)} total properties so far...")
                
                # Scroll down
                self.driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
                time.sleep(2)
                
                # Check if page height changed (more content loaded)
                new_height = self.driver.execute_script("return document.body.scrollHeight")
                if new_height == last_height:
                    logger.info("📄 Reached end of page")
                    break
                
                last_height = new_height
                scroll_attempts += 1
                
                # Human-like delay
                time.sleep(random.uniform(1, 3))
            
            logger.info(f"✅ Extraction complete: {len(properties)} properties found")
            return properties
            
        except Exception as e:
            logger.error(f"❌ Scrolling extraction failed: {e}")
            return []
    
    def extract_visible_properties(self) -> List[Dict[str, Any]]:
        """Extract properties currently visible on page"""
        try:
            properties = []
            
            # Try different selectors
            selectors = [
                "[data-testid='listing-card']",
                ".listing-card",
                ".property-card",
                ".search-result"
            ]
            
            property_elements = []
            for selector in selectors:
                try:
                    elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    if elements:
                        property_elements = elements
                        break
                except:
                    continue
            
            for element in property_elements:
                try:
                    prop_data = {}
                    
                    # Extract title
                    try:
                        title_elem = element.find_element(By.CSS_SELECTOR, "h3, .title, .property-title")
                        prop_data["title"] = title_elem.text.strip()
                    except:
                        prop_data["title"] = "Unknown"
                    
                    # Extract price
                    try:
                        price_elem = element.find_element(By.CSS_SELECTOR, ".price, .listing-price")
                        prop_data["price"] = price_elem.text.strip()
                    except:
                        prop_data["price"] = "Unknown"
                    
                    # Extract location
                    try:
                        location_elem = element.find_element(By.CSS_SELECTOR, ".location, .address")
                        prop_data["location"] = location_elem.text.strip()
                    except:
                        prop_data["location"] = "Unknown"
                    
                    # Extract link
                    try:
                        link_elem = element.find_element(By.CSS_SELECTOR, "a")
                        prop_data["url"] = link_elem.get_attribute("href")
                    except:
                        prop_data["url"] = ""
                    
                    if prop_data["title"] != "Unknown":
                        properties.append(prop_data)
                        
                except Exception as e:
                    logger.debug(f"Error extracting property: {e}")
                    continue
            
            return properties
            
        except Exception as e:
            logger.error(f"Error extracting visible properties: {e}")
            return []
    
    def close(self):
        """Close connection (but keep browser open)"""
        if self.driver:
            try:
                # Don't quit the browser, just disconnect
                self.driver = None
                self.connected = False
                logger.info("🔌 Disconnected from browser (browser remains open)")
            except Exception as e:
                logger.error(f"Error disconnecting: {e}")


def test_existing_browser_control():
    """Test controlling existing browser"""
    print("🎮 Existing Browser Controller Test")
    print("=" * 40)
    
    controller = ExistingBrowserController()
    
    try:
        # Connect to existing browser
        if controller.connect_to_existing_browser():
            print("✅ Connected to existing browser")
            
            # Navigate to PropertyGuru
            url = "https://www.propertyguru.com.sg/property-for-sale?districtCode=D01&districtCode=D09"
            if controller.navigate_to_propertyguru(url):
                print("✅ Navigation successful")
                
                # Check for blocking
                if not controller.check_for_blocking():
                    print("✅ No blocking detected")
                    
                    # Extract properties
                    properties = controller.scroll_and_extract_properties()
                    print(f"✅ Extracted {len(properties)} properties")
                    
                    # Show sample
                    for i, prop in enumerate(properties[:3], 1):
                        print(f"   {i}. {prop['title'][:50]}...")
                        print(f"      Price: {prop['price']}")
                        print(f"      Location: {prop['location']}")
                        print()
                else:
                    print("❌ Still being blocked")
            else:
                print("❌ Navigation failed")
        else:
            print("❌ Could not connect to existing browser")
        
        input("Press Enter to finish...")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
    finally:
        controller.close()


if __name__ == "__main__":
    import random
    test_existing_browser_control()
