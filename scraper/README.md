# PropertyGuru Automated Scraper

A comprehensive, automated scraper for PropertyGuru Singapore that can crawl the entire website systematically.

## 🚀 Features

- **Fully Automated**: Crawls through all property listings automatically
- **Anti-Detection**: Built-in delays, user agent rotation, and human-like behavior
- **Database Integration**: Saves properties directly to PostgreSQL database
- **Configurable**: Multiple preset configurations for different use cases
- **Monitoring**: Built-in monitoring and scheduling capabilities
- **Error Handling**: Robust error handling with retry mechanisms
- **Data Analysis**: Automatic data structure analysis and schema suggestions

## 📁 Files Overview

- `automated_scraper.py` - Main scraper engine
- `run_scraper.py` - Command-line interface
- `scraper_config.py` - Configuration settings
- `scraping_monitor.py` - Monitoring and scheduling
- `manual_analyzer.py` - HTML analysis tool

## 🛠️ Quick Start

### 1. Basic Usage

```bash
# Quick test (2 pages, sale properties only)
python run_scraper.py --config test

# Development scraping (10 pages per type)
python run_scraper.py --config development

# Full production scrape (unlimited pages)
python run_scraper.py --config production
```

### 2. Custom Options

```bash
# Custom scraping with specific parameters
python run_scraper.py --pages 5 --types sale rent --headless false

# Scrape without saving to database
python run_scraper.py --config development --no-database

# Show configuration without running
python run_scraper.py --config production --dry-run
```

## ⚙️ Configuration Options

### Preset Configurations

| Config | Pages | Types | Use Case |
|--------|-------|-------|----------|
| `test` | 2 | sale | Quick testing |
| `development` | 10 | sale, rent | Development work |
| `production` | unlimited | sale, rent | Full scraping |
| `quick_sample` | 1 | sale | Fast sampling |

### Custom Parameters

```bash
--config PRESET          # Use preset configuration
--headless true/false     # Browser visibility
--pages N                 # Max pages per listing type
--max-properties N        # Max total properties
--types sale rent         # Listing types to scrape
--delay MIN MAX           # Delay range between requests
--no-database            # Skip database saving
--output-dir PATH        # Output directory
```

## 🔄 How It Works

### 1. **Navigation Strategy**
- Starts from PropertyGuru homepage
- Navigates to search results pages
- Handles pagination automatically
- Scrolls to load dynamic content

### 2. **Data Extraction**
- Uses BeautifulSoup for HTML parsing
- Extracts structured property data
- Handles multiple property card formats
- Validates and cleans extracted data

### 3. **Anti-Detection Measures**
- Random delays between requests (2-5 seconds)
- User agent rotation
- Human-like scrolling behavior
- Respectful request patterns

### 4. **Data Processing**
- Converts raw HTML to structured data
- Validates property information
- Generates unique PropertyGuru IDs
- Saves to database and JSON files

## 📊 Monitoring & Scheduling

### Run Monitoring

```bash
# Show current status
python scraping_monitor.py --status

# Run single scraping job
python scraping_monitor.py --run-once

# Start scheduled monitoring
python scraping_monitor.py --schedule
```

### Scheduled Jobs
- **Daily full scrape**: 2:00 AM
- **Quick updates**: Every 6 hours
- **Error monitoring**: Continuous

## 🗄️ Database Integration

Properties are automatically saved to your PostgreSQL database with this structure:

```sql
-- Core property data
properties (
    id, property_guru_id, title, description,
    property_type, listing_type, price, price_psf,
    bedrooms, bathrooms, floor_area_sqft,
    address, district, latitude, longitude,
    mrt_distance_m, nearest_mrt,
    status, agent_id, images, amenities,
    created_at, updated_at, scraped_at
)
```

## 📈 Output Files

### JSON Results
```
scraped_data/
├── scraping_results_20240111_143022.json
├── complete_scraping_results.json
└── scraping_stats.json
```

### Database Tables
- `properties` - Main property data
- `agents` - Real estate agent information
- `property_history` - Price and status changes
- `scraping_jobs` - Job tracking and statistics

## 🛡️ Best Practices

### 1. **Respectful Scraping**
- Built-in delays between requests
- Limited concurrent requests
- Respects robots.txt guidelines
- Human-like browsing patterns

### 2. **Error Handling**
- Automatic retries on failures
- Graceful handling of missing data
- Comprehensive logging
- Continue on individual errors

### 3. **Data Quality**
- Duplicate detection and prevention
- Data validation and cleaning
- Structured data extraction
- Comprehensive error logging

## 🔧 Advanced Usage

### Custom Filters

```python
# In scraper_config.py, add custom search filters
CUSTOM_FILTERS = {
    "luxury_condos": {
        "property_types": ["Condo"],
        "min_price": 2000000,
        "districts": [9, 10, 11]
    }
}
```

### Proxy Support

```python
# Enable proxy rotation in config
config.proxy_rotation = True
config.proxy_list = ["proxy1:port", "proxy2:port"]
```

### Custom Data Processing

```python
# Override extract_structured_property method
def custom_extract_property(self, card_data):
    # Your custom extraction logic
    return property_data
```

## 📋 Example Output

```json
{
  "scraping_started": "2024-01-11T14:30:22",
  "total_properties": 1247,
  "results": {
    "sale": {
      "total_pages_scraped": 25,
      "properties": [...],
      "total_properties": 623
    },
    "rent": {
      "total_pages_scraped": 31,
      "properties": [...],
      "total_properties": 624
    }
  }
}
```

## ⚠️ Important Notes

### Legal Compliance
- Review PropertyGuru's Terms of Service
- Respect rate limits and robots.txt
- Use scraped data responsibly
- Consider contacting PropertyGuru for API access

### Performance
- Full scraping can take several hours
- Monitor system resources during scraping
- Use appropriate delays to avoid overloading servers
- Consider running during off-peak hours

### Maintenance
- Update selectors if website structure changes
- Monitor for anti-bot measures
- Keep Chrome and ChromeDriver updated
- Regular database maintenance

## 🐛 Troubleshooting

### Common Issues

1. **ChromeDriver not found**
   ```bash
   pip install webdriver-manager
   ```

2. **Database connection failed**
   ```bash
   # Check database is running
   python -c "from database.connection import check_database_connection; print(check_database_connection())"
   ```

3. **No properties found**
   - Website structure may have changed
   - Check if site is accessible
   - Review scraper logs for errors

4. **Bot detection**
   - Increase delay ranges
   - Enable proxy rotation
   - Use different user agents

## 📞 Support

For issues or questions:
1. Check the logs in `scraper.log`
2. Review the troubleshooting section
3. Test with `--config test` first
4. Check database connectivity

---

**Remember**: Always scrape responsibly and respect website terms of service! 🤖✨
