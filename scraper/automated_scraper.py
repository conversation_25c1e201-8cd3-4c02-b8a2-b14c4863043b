"""
Automated PropertyGuru scraper that crawls the entire website
"""
import time
import json
import random
from datetime import datetime
from typing import Dict, List, Any, Optional
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from webdriver_manager.chrome import ChromeDriverManager
from bs4 import BeautifulSoup
import logging
from urllib.parse import urljoin, urlparse
import sys
import os

# Add parent directory to path for imports
sys.path.append(os.path.dirname(os.path.dirname(__file__)))
from database.connection import get_db, DatabaseManager
from database.repositories.property_repository import PropertyRepository
from database.schemas import PropertyCreate
from scraper.manual_analyzer import PropertyGuruAnalyzer

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class AutomatedPropertyGuruScraper:
    """Automated scraper for PropertyGuru website"""

    def __init__(self, headless=True, delay_range=(2, 5), max_pages=None):
        self.driver = None
        self.headless = headless
        self.delay_range = delay_range
        self.max_pages = max_pages
        self.base_url = "https://www.propertyguru.com.sg"
        self.visited_urls = set()
        self.scraped_properties = []
        self.analyzer = PropertyGuruAnalyzer()
        self.setup_driver()

    def setup_driver(self):
        """Setup Chrome WebDriver with anti-detection measures"""
        try:
            chrome_options = Options()
            if self.headless:
                chrome_options.add_argument("--headless")

            # Enhanced anti-detection options
            chrome_options.add_argument("--no-sandbox")
            chrome_options.add_argument("--disable-dev-shm-usage")
            chrome_options.add_argument("--disable-blink-features=AutomationControlled")
            chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
            chrome_options.add_experimental_option('useAutomationExtension', False)
            chrome_options.add_argument("--window-size=1366,768")  # More common resolution

            # Additional stealth options
            chrome_options.add_argument("--disable-web-security")
            chrome_options.add_argument("--allow-running-insecure-content")
            chrome_options.add_argument("--disable-extensions")
            chrome_options.add_argument("--disable-plugins")
            chrome_options.add_argument("--disable-default-apps")
            chrome_options.add_argument("--disable-background-timer-throttling")
            chrome_options.add_argument("--disable-backgrounding-occluded-windows")
            chrome_options.add_argument("--disable-renderer-backgrounding")
            chrome_options.add_argument("--disable-features=TranslateUI")
            chrome_options.add_argument("--disable-ipc-flooding-protection")

            # Rotate user agents
            user_agents = [
                "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
                "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
            ]
            chrome_options.add_argument(f"--user-agent={random.choice(user_agents)}")

            service = Service(ChromeDriverManager().install())
            self.driver = webdriver.Chrome(service=service, options=chrome_options)

            # Execute enhanced stealth scripts
            stealth_js = """
                Object.defineProperty(navigator, 'webdriver', {get: () => undefined});
                Object.defineProperty(navigator, 'plugins', {get: () => [1, 2, 3, 4, 5]});
                Object.defineProperty(navigator, 'languages', {get: () => ['en-US', 'en']});
                window.chrome = {runtime: {}};
                Object.defineProperty(navigator, 'permissions', {
                    get: () => ({query: () => Promise.resolve({state: 'granted'})})
                });
                delete navigator.__proto__.webdriver;
            """
            self.driver.execute_script(stealth_js)

            logger.info("Chrome WebDriver initialized successfully")
        except Exception as e:
            logger.error(f"Failed to setup WebDriver: {e}")
            raise

    def random_delay(self, extra_factor=1.0):
        """Enhanced random delay with human-like patterns"""
        base_delay = random.uniform(*self.delay_range)

        # Add extra randomness for production
        if extra_factor > 1.0:
            base_delay *= random.uniform(1.0, extra_factor)

        # Occasionally add longer pauses (simulate human breaks)
        if random.random() < 0.15:  # 15% chance
            base_delay += random.uniform(20, 60)  # 20s-1min break
            logger.info(f"Taking a human-like break: {base_delay:.1f}s")
        elif random.random() < 0.3:  # 30% chance of medium pause
            base_delay += random.uniform(5, 15)  # 5-15s pause
            logger.debug(f"Taking a thinking pause: {base_delay:.1f}s")

        # Add micro-delays to simulate human typing/clicking
        micro_delays = [0.1, 0.2, 0.3, 0.5, 0.8]
        for _ in range(random.randint(2, 5)):
            time.sleep(random.choice(micro_delays))

        logger.debug(f"Waiting {base_delay:.2f} seconds...")
        time.sleep(base_delay)

    def scrape_all_properties(self, listing_types=["sale", "rent"]):
        """Scrape all properties from PropertyGuru"""
        logger.info("Starting automated PropertyGuru scraping...")

        all_results = {
            "scraping_started": datetime.now().isoformat(),
            "listing_types": listing_types,
            "results": {}
        }

        for listing_type in listing_types:
            logger.info(f"Scraping {listing_type} properties...")
            results = self.scrape_listing_type(listing_type)
            all_results["results"][listing_type] = results

            # Save intermediate results
            self.save_results(all_results, f"scraping_results_{listing_type}.json")

        all_results["scraping_completed"] = datetime.now().isoformat()
        all_results["total_properties"] = sum(len(r.get("properties", [])) for r in all_results["results"].values())

        # Save final results
        self.save_results(all_results, "complete_scraping_results.json")

        return all_results

    def scrape_listing_type(self, listing_type="sale"):
        """Scrape all properties for a specific listing type with natural navigation"""
        try:
            # Start from homepage to appear more natural
            logger.info("Starting from homepage for natural navigation...")
            self.driver.get(self.base_url)
            self.random_delay(extra_factor=1.5)

            # Navigate naturally to the listing type
            if not self.navigate_to_listings_naturally(listing_type):
                # Fallback to direct URL
                if listing_type == "sale":
                    start_url = f"{self.base_url}/property-for-sale"
                else:
                    start_url = f"{self.base_url}/property-for-rent"

                logger.info(f"Direct navigation to: {start_url}")
                self.driver.get(start_url)
                self.random_delay()

            # Get all search result pages
            all_properties = []
            page_num = 1

            while True:
                logger.info(f"Scraping page {page_num}...")

                # Scrape current page
                page_properties = self.scrape_current_page()
                if not page_properties:
                    logger.info("No properties found on this page, stopping...")
                    break

                all_properties.extend(page_properties)
                logger.info(f"Found {len(page_properties)} properties on page {page_num}")

                # Check if we've hit max pages limit
                if self.max_pages and page_num >= self.max_pages:
                    logger.info(f"Reached max pages limit ({self.max_pages})")
                    break

                # Take a longer break every 10 pages
                if page_num % 10 == 0:
                    break_time = random.uniform(30, 90)
                    logger.info(f"Taking a longer break after {page_num} pages: {break_time:.1f}s")
                    time.sleep(break_time)

                # Try to go to next page
                if not self.go_to_next_page():
                    logger.info("No more pages available")
                    break

                page_num += 1
                # Use longer delays for production scraping
                self.random_delay(extra_factor=1.5)

            return {
                "listing_type": listing_type,
                "total_pages_scraped": page_num,
                "properties": all_properties,
                "total_properties": len(all_properties)
            }

        except Exception as e:
            logger.error(f"Error scraping {listing_type} properties: {e}")
            return {"error": str(e), "listing_type": listing_type}

    def scrape_current_page(self):
        """Scrape all properties from current page"""
        try:
            # Wait for page to load
            WebDriverWait(self.driver, 10).until(
                EC.presence_of_element_located((By.TAG_NAME, "body"))
            )

            # Scroll to load dynamic content
            self.scroll_page()

            # Get page source and analyze
            html_content = self.driver.page_source
            analysis = self.analyzer.analyze_html(html_content)

            properties = []
            for card_data in analysis.get("property_cards", []):
                try:
                    property_data = self.extract_structured_property(card_data)
                    if property_data:
                        properties.append(property_data)
                except Exception as e:
                    logger.warning(f"Error extracting property: {e}")
                    continue

            return properties

        except Exception as e:
            logger.error(f"Error scraping current page: {e}")
            return []

    def scroll_page(self):
        """Human-like scrolling to load dynamic content"""
        try:
            # Get page height
            page_height = self.driver.execute_script("return document.body.scrollHeight")

            # Scroll down gradually with random pauses
            scroll_positions = [0.2, 0.4, 0.6, 0.8, 1.0]
            for position in scroll_positions:
                scroll_to = int(page_height * position)
                self.driver.execute_script(f"window.scrollTo(0, {scroll_to});")

                # Random pause between scrolls
                pause = random.uniform(0.5, 2.0)
                time.sleep(pause)

                # Occasionally scroll back up a bit (human behavior)
                if random.random() < 0.3:
                    back_scroll = scroll_to - random.randint(100, 300)
                    self.driver.execute_script(f"window.scrollTo(0, {max(0, back_scroll)});")
                    time.sleep(random.uniform(0.3, 0.8))
                    self.driver.execute_script(f"window.scrollTo(0, {scroll_to});")

            # Scroll back to top gradually
            for position in [0.8, 0.6, 0.4, 0.2, 0.0]:
                scroll_to = int(page_height * position)
                self.driver.execute_script(f"window.scrollTo(0, {scroll_to});")
                time.sleep(random.uniform(0.3, 0.7))

        except Exception as e:
            logger.warning(f"Error scrolling page: {e}")

    def extract_structured_property(self, card_data):
        """Extract structured property data from card analysis"""
        try:
            extracted = card_data.get("extracted_data", {})

            # Extract price with enhanced cleaning
            prices = extracted.get("prices", [])
            price_value = None
            if prices:
                # Try multiple price strings in order of preference
                for price_candidate in prices:
                    # Clean price string and extract number
                    price_str = price_candidate.replace("S$", "").replace("$", "").replace(",", "").strip()
                    # Remove any trailing text like "/month", "/psf", etc.
                    price_str = re.sub(r'/.*$', '', price_str)  # Remove everything after /
                    price_str = re.sub(r'\s.*$', '', price_str)  # Remove everything after space

                    # Extract just the numeric part
                    import re
                    numeric_match = re.search(r'\d+', price_str)
                    if numeric_match:
                        price_str = numeric_match.group()

                    # Fix common parsing errors - handle extra digits
                    if len(price_str) > 10:  # Likely has extra digits
                        # Look for reasonable price patterns (4-8 digits for Singapore)
                        price_matches = re.findall(r'\d{4,8}', price_str)
                        if price_matches:
                            # Take the first reasonable price
                            price_str = price_matches[0]

                    try:
                        price_value = float(price_str)
                        # Validate price range (Singapore property prices)
                        if 100000 <= price_value <= 50000000:  # 100k to 50M SGD
                            break  # Found valid price, stop trying
                        else:
                            logger.debug(f"Price {price_value} outside valid range")
                            price_value = None
                    except ValueError:
                        logger.debug(f"Could not parse price: {price_str}")
                        continue

                if price_value is None and prices:
                    logger.warning(f"Could not extract valid price from: {prices[:3]}")

            # Extract rooms with validation
            rooms = extracted.get("rooms", {})
            bedrooms = None
            bathrooms = None

            if rooms.get("bedrooms"):
                try:
                    bedroom_val = int(rooms["bedrooms"][0])
                    # Validate bedroom count (reasonable range)
                    if 0 <= bedroom_val <= 10:
                        bedrooms = bedroom_val
                    else:
                        logger.warning(f"Unrealistic bedroom count: {bedroom_val}")
                except (ValueError, IndexError):
                    logger.debug("Could not parse bedroom count")

            if rooms.get("bathrooms"):
                try:
                    bathroom_val = int(rooms["bathrooms"][0])
                    # Validate bathroom count (reasonable range)
                    if 0 <= bathroom_val <= 10:
                        bathrooms = bathroom_val
                    else:
                        logger.warning(f"Unrealistic bathroom count: {bathroom_val}")
                except (ValueError, IndexError):
                    logger.debug("Could not parse bathroom count")

            # Extract area
            areas = extracted.get("area", [])
            floor_area = None
            if areas:
                area_str = areas[0].replace("sqft", "").replace(",", "").strip()
                try:
                    floor_area = int(area_str.split()[0])
                except:
                    pass

            # Extract location info
            locations = extracted.get("location", [])
            address = None
            district = None

            for loc in locations:
                if "District" in loc:
                    try:
                        district = int(loc.replace("District", "").strip())
                    except:
                        pass
                elif len(loc) > 10:  # Likely an address
                    address = loc

            # Extract property type
            property_types = extracted.get("property_type", [])
            property_type = property_types[0] if property_types else "Unknown"

            # Get title from card text with filtering
            title = card_data.get("text_sample", "").split("\n")[0] if card_data.get("text_sample") else "No Title"
            title = title[:100]  # Limit title length

            # Filter out navigation elements and invalid properties
            if self.is_navigation_element(title, card_data):
                logger.debug(f"Filtering out navigation element: {title[:50]}")
                return None

            # Determine listing type
            listing_type = "rent" if "/month" in str(prices) or "/mo" in str(prices) else "sale"

            # Create property data
            property_data = {
                "property_guru_id": f"PG_{hash(card_data.get('text_sample', ''))}", # Generate ID from content hash
                "title": title,
                "property_type": property_type,
                "listing_type": listing_type,
                "price": price_value,
                "bedrooms": bedrooms,
                "bathrooms": bathrooms,
                "floor_area_sqft": floor_area,
                "address": address or "Address not found",
                "district": district,
                "images": extracted.get("images", []),
                "scraped_at": datetime.now().isoformat(),
                "raw_data": card_data  # Keep raw data for debugging
            }

            return property_data

        except Exception as e:
            logger.error(f"Error extracting structured property: {e}")
            return None

    def is_navigation_element(self, title, card_data):
        """Check if this is a navigation element rather than a property"""
        title_lower = title.lower()
        text_sample = card_data.get("text_sample", "").lower()

        # Navigation indicators
        nav_indicators = [
            "menu", "navigation", "nav", "header", "footer",
            "search", "filter", "sort", "view all", "show more",
            "sign in", "register", "login", "account",
            "contact us", "about us", "help", "support",
            "terms", "privacy", "cookie", "advertisement",
            "sponsored", "featured", "premium", "top",
            "directory", "category", "browse", "explore"
        ]

        # Check title and text for navigation indicators
        for indicator in nav_indicators:
            if indicator in title_lower or indicator in text_sample:
                return True

        # Check if it has property-like content
        has_price = any(price_word in text_sample for price_word in ["s$", "$", "sgd", "price"])
        has_rooms = any(room_word in text_sample for room_word in ["bed", "bath", "room"])
        has_area = any(area_word in text_sample for area_word in ["sqft", "sq ft", "square"])

        # If it doesn't have property indicators, likely navigation
        if not (has_price or has_rooms or has_area):
            return True

        # Check text length - navigation elements are usually short
        if len(text_sample.strip()) < 30:
            return True

        return False

    def go_to_next_page(self):
        """Navigate to next page of results with enhanced anti-detection"""
        try:
            current_url = self.driver.current_url

            # Enhanced URL-based pagination (most reliable method)
            if "page=" in current_url:
                # Extract current page number and increment
                import re
                page_match = re.search(r'page=(\d+)', current_url)
                if page_match:
                    current_page = int(page_match.group(1))
                    next_page_url = current_url.replace(f"page={current_page}", f"page={current_page + 1}")
                else:
                    return False
            else:
                # Add page parameter
                separator = "&" if "?" in current_url else "?"
                next_page_url = f"{current_url}{separator}page=2"

            logger.info(f"Navigating to: {next_page_url}")

            # Navigate with enhanced delay
            self.driver.get(next_page_url)

            # Enhanced wait for page load
            time.sleep(random.uniform(3, 8))  # Wait for initial load

            # Check for bot detection
            if not self.validate_page_load():
                logger.warning("Page load validation failed - possible bot detection")
                return False

            # Verify navigation success
            new_url = self.driver.current_url
            if new_url != current_url:
                logger.info("✅ Successfully navigated to next page")
                return True
            else:
                logger.warning("Navigation failed - still on same page")
                return False

        except Exception as e:
            logger.error(f"Error going to next page: {e}")
            return False

    def validate_page_load(self):
        """Validate that page loaded correctly (not blocked)"""
        try:
            # Wait for page to load
            WebDriverWait(self.driver, 15).until(
                EC.presence_of_element_located((By.TAG_NAME, "body"))
            )

            page_title = self.driver.title.lower()
            page_source = self.driver.page_source.lower()

            # Check for bot detection indicators
            bot_indicators = [
                "just a moment",
                "checking your browser",
                "cloudflare",
                "please wait",
                "verifying you are human",
                "captcha",
                "blocked",
                "access denied"
            ]

            for indicator in bot_indicators:
                if indicator in page_title or indicator in page_source:
                    logger.warning(f"Bot detection indicator found: {indicator}")
                    # Try to handle Cloudflare challenge
                    if "cloudflare" in indicator or "just a moment" in indicator:
                        return self.handle_cloudflare_challenge()
                    return False

            # Check for property-related content
            property_indicators = [
                "property", "properties", "listing", "listings",
                "bedroom", "bathroom", "sqft", "singapore", "s$"
            ]

            has_property_content = any(indicator in page_source for indicator in property_indicators)

            if not has_property_content:
                logger.warning("No property-related content found on page")
                return False

            return True

        except TimeoutException:
            logger.error("Page load timeout")
            return False
        except Exception as e:
            logger.error(f"Error validating page load: {e}")
            return False

    def handle_cloudflare_challenge(self):
        """Handle Cloudflare challenge page"""
        try:
            logger.info("Attempting to handle Cloudflare challenge...")

            # Wait longer for Cloudflare to complete
            max_wait = 30  # 30 seconds
            start_time = time.time()

            while time.time() - start_time < max_wait:
                current_title = self.driver.title.lower()
                current_source = self.driver.page_source.lower()

                # Check if challenge is resolved
                if "just a moment" not in current_title and "cloudflare" not in current_source:
                    # Check for property content
                    property_indicators = ["property", "listing", "bedroom", "s$"]
                    if any(indicator in current_source for indicator in property_indicators):
                        logger.info("✅ Cloudflare challenge resolved successfully")
                        return True

                # Simulate human behavior during wait
                if random.random() < 0.3:  # 30% chance
                    # Small mouse movement
                    self.driver.execute_script("""
                        var event = new MouseEvent('mousemove', {
                            'view': window,
                            'bubbles': true,
                            'cancelable': true,
                            'clientX': Math.random() * 100 + 100,
                            'clientY': Math.random() * 100 + 100
                        });
                        document.dispatchEvent(event);
                    """)

                time.sleep(2)  # Check every 2 seconds

            logger.warning("Cloudflare challenge not resolved within timeout")
            return False

        except Exception as e:
            logger.error(f"Error handling Cloudflare challenge: {e}")
            return False

    def save_results(self, data, filename):
        """Save results to JSON file"""
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, default=str, ensure_ascii=False)
            logger.info(f"Results saved to {filename}")
        except Exception as e:
            logger.error(f"Error saving results to {filename}: {e}")

    def save_to_database(self, properties):
        """Save properties to database"""
        try:
            db = next(get_db())
            repo = PropertyRepository(db)

            saved_count = 0
            for prop_data in properties:
                try:
                    # Remove raw_data before saving
                    clean_data = {k: v for k, v in prop_data.items() if k != 'raw_data'}
                    property_create = PropertyCreate(**clean_data)

                    # Check if property already exists
                    existing = repo.get_property_by_guru_id(property_create.property_guru_id)
                    if not existing:
                        repo.create_property(property_create)
                        saved_count += 1
                    else:
                        logger.debug(f"Property {property_create.property_guru_id} already exists")

                except Exception as e:
                    logger.warning(f"Error saving property to database: {e}")
                    continue

            logger.info(f"Saved {saved_count} new properties to database")
            return saved_count

        except Exception as e:
            logger.error(f"Error saving to database: {e}")
            return 0

    def navigate_to_listings_naturally(self, listing_type):
        """Navigate to listings page naturally like a human user"""
        try:
            logger.info(f"Navigating naturally to {listing_type} listings...")

            # Look for navigation links on homepage
            if listing_type == "sale":
                search_terms = ["buy", "sale", "for sale", "purchase"]
            else:
                search_terms = ["rent", "rental", "for rent"]

            # Try to find and click navigation links
            for term in search_terms:
                try:
                    # Look for links containing the search term
                    link_xpath = f"//a[contains(translate(text(), 'ABCDEFGHIJKLMNOPQRSTUVWXYZ', 'abcdefghijklmnopqrstuvwxyz'), '{term}')]"
                    links = self.driver.find_elements(By.XPATH, link_xpath)

                    for link in links:
                        if link.is_displayed() and link.is_enabled():
                            logger.info(f"Found navigation link: {link.text}")
                            # Scroll to element and click
                            self.driver.execute_script("arguments[0].scrollIntoView(true);", link)
                            time.sleep(1)
                            link.click()
                            self.random_delay(extra_factor=1.5)
                            return True

                except Exception as e:
                    logger.debug(f"Error finding link for {term}: {e}")
                    continue

            logger.info("Natural navigation failed, will use direct URL")
            return False

        except Exception as e:
            logger.error(f"Error in natural navigation: {e}")
            return False

    def close(self):
        """Close the WebDriver"""
        if self.driver:
            self.driver.quit()
            logger.info("WebDriver closed")


def main():
    """Main scraping function"""
    print("🤖 Automated PropertyGuru Scraper")
    print("=" * 40)

    # Configuration
    config = {
        "headless": True,  # Set to False to see browser
        "delay_range": (2, 5),  # Random delay between requests
        "max_pages": 5,  # Limit pages for testing (None for unlimited)
        "listing_types": ["sale", "rent"]
    }

    scraper = AutomatedPropertyGuruScraper(
        headless=config["headless"],
        delay_range=config["delay_range"],
        max_pages=config["max_pages"]
    )

    try:
        # Start scraping
        results = scraper.scrape_all_properties(config["listing_types"])

        print(f"\n✅ Scraping completed!")
        print(f"📊 Total properties found: {results.get('total_properties', 0)}")

        # Save to database if available
        all_properties = []
        for listing_type, data in results.get("results", {}).items():
            all_properties.extend(data.get("properties", []))

        if all_properties:
            try:
                saved_count = scraper.save_to_database(all_properties)
                print(f"💾 Saved {saved_count} properties to database")
            except Exception as e:
                print(f"⚠️  Database save failed: {e}")

        return results

    except Exception as e:
        logger.error(f"Scraping failed: {e}")
        return {"error": str(e)}
    finally:
        scraper.close()


if __name__ == "__main__":
    results = main()
