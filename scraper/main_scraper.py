#!/usr/bin/env python3
"""
🧠 Smart PropertyGuru Scraper
Handles Cloudflare and extracts property data intelligently
"""

import time
import json
import re
from datetime import datetime
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException

class SmartPropertyScraper:
    def __init__(self):
        self.driver = None
        self.wait = None
        
    def connect_and_navigate(self):
        """Connect to browser and navigate to PropertyGuru"""
        try:
            # Connect to existing Chrome
            chrome_options = Options()
            chrome_options.add_experimental_option("debuggerAddress", "127.0.0.1:9222")
            self.driver = webdriver.Chrome(options=chrome_options)
            self.wait = WebDriverWait(self.driver, 15)
            
            print("✅ Connected to Chrome browser")
            
            # Navigate to PropertyGuru
            url = "https://www.propertyguru.com.sg/property-for-sale"
            print(f"🌐 Navigating to: {url}")
            self.driver.get(url)
            
            return True
            
        except Exception as e:
            print(f"❌ Connection/Navigation failed: {e}")
            return False
    
    def handle_cloudflare_and_wait(self):
        """Handle Cloudflare protection and wait for page to load"""
        print("🛡️ Checking for Cloudflare protection...")
        
        max_wait_time = 60  # Wait up to 60 seconds
        start_time = time.time()
        
        while time.time() - start_time < max_wait_time:
            try:
                current_title = self.driver.title
                current_url = self.driver.current_url
                
                print(f"⏳ Current page: {current_title}")
                
                # Check if we're past Cloudflare
                if ("just a moment" not in current_title.lower() and 
                    "propertyguru" in current_url and
                    "cloudflare" not in current_title.lower()):
                    
                    print("✅ Successfully loaded PropertyGuru!")
                    
                    # Wait a bit more for content to load
                    time.sleep(5)
                    return True
                
                # Check if we need to wait for Cloudflare
                if "just a moment" in current_title.lower() or "cloudflare" in current_title.lower():
                    print("🔄 Waiting for Cloudflare to pass...")
                    time.sleep(3)
                    continue
                
                # Check if page has property content
                try:
                    body_text = self.driver.find_element(By.TAG_NAME, "body").text
                    if "properties for sale" in body_text.lower() or "S$" in body_text:
                        print("✅ PropertyGuru content detected!")
                        return True
                except:
                    pass
                
                time.sleep(2)
                
            except Exception as e:
                print(f"⚠️ Error checking page: {e}")
                time.sleep(2)
        
        print("⚠️ Cloudflare wait timeout, proceeding anyway...")
        return False
    
    def extract_properties_smart(self):
        """Smart property extraction with multiple strategies"""
        properties = []
        
        print("🔍 Starting smart property extraction...")
        
        try:
            # Strategy 1: Look for property data in page source
            print("📊 Strategy 1: Analyzing page source...")
            page_source = self.driver.page_source
            
            # Look for JSON data in script tags (common in modern websites)
            json_matches = re.findall(r'window\.__INITIAL_STATE__\s*=\s*({.*?});', page_source, re.DOTALL)
            if not json_matches:
                json_matches = re.findall(r'window\.__NEXT_DATA__\s*=\s*({.*?});', page_source, re.DOTALL)
            if not json_matches:
                json_matches = re.findall(r'"listings":\s*(\[.*?\])', page_source, re.DOTALL)
            
            for json_str in json_matches:
                try:
                    data = json.loads(json_str)
                    extracted = self._extract_from_json_data(data)
                    if extracted:
                        properties.extend(extracted)
                        print(f"✅ Strategy 1 found {len(extracted)} properties in JSON data")
                        break
                except:
                    continue
            
            # Strategy 2: Extract from visible page text
            if not properties:
                print("📊 Strategy 2: Extracting from page text...")
                properties = self._extract_from_page_text()
            
            # Strategy 3: Look for property elements
            if not properties:
                print("📊 Strategy 3: Looking for property elements...")
                properties = self._extract_from_elements()
            
        except Exception as e:
            print(f"❌ Extraction error: {e}")
        
        return properties
    
    def _extract_from_json_data(self, data):
        """Extract properties from JSON data"""
        properties = []
        
        def find_properties_recursive(obj, path=""):
            if isinstance(obj, dict):
                for key, value in obj.items():
                    if key in ['listings', 'properties', 'results', 'data']:
                        if isinstance(value, list):
                            for item in value:
                                if isinstance(item, dict) and 'price' in str(item).lower():
                                    prop = self._parse_property_json(item)
                                    if prop:
                                        properties.append(prop)
                    else:
                        find_properties_recursive(value, f"{path}.{key}")
            elif isinstance(obj, list):
                for i, item in enumerate(obj):
                    find_properties_recursive(item, f"{path}[{i}]")
        
        find_properties_recursive(data)
        return properties
    
    def _parse_property_json(self, item):
        """Parse a single property from JSON"""
        try:
            prop = {
                'id': item.get('id', f'prop_{len(properties)}'),
                'extraction_timestamp': datetime.now().isoformat(),
                'source': 'PropertyGuru'
            }
            
            # Extract common fields
            if 'price' in item:
                prop['price'] = item['price']
            if 'bedrooms' in item:
                prop['bedrooms'] = item['bedrooms']
            if 'bathrooms' in item:
                prop['bathrooms'] = item['bathrooms']
            if 'area' in item:
                prop['area'] = item['area']
            if 'title' in item:
                prop['name'] = item['title']
            if 'address' in item:
                prop['address'] = item['address']
            
            return prop if 'price' in prop else None
            
        except Exception as e:
            return None
    
    def _extract_from_page_text(self):
        """Extract properties from visible page text"""
        properties = []
        
        try:
            # Get page text
            body_text = self.driver.find_element(By.TAG_NAME, "body").text
            
            # Look for property patterns
            # Pattern: Find lines with price, beds, and area
            lines = body_text.split('\n')
            
            current_property = {}
            for i, line in enumerate(lines):
                line = line.strip()
                
                # Look for price
                price_match = re.search(r'S\$\s*([\d,]+)', line)
                if price_match:
                    if current_property:
                        # Save previous property
                        if self._is_valid_property(current_property):
                            properties.append(current_property)
                    
                    # Start new property
                    current_property = {
                        'id': f'property_{len(properties)}',
                        'price': int(price_match.group(1).replace(',', '')),
                        'extraction_timestamp': datetime.now().isoformat(),
                        'source': 'PropertyGuru'
                    }
                
                # Look for bedrooms
                bed_match = re.search(r'(\d+)\s+Beds?', line)
                if bed_match and current_property:
                    current_property['bedrooms'] = int(bed_match.group(1))
                
                # Look for area
                area_match = re.search(r'(\d+,?\d*)\s+sqft', line)
                if area_match and current_property:
                    current_property['area'] = int(area_match.group(1).replace(',', ''))
                
                # Look for property name (usually appears before price)
                if not price_match and current_property and 'name' not in current_property:
                    name_match = re.search(r'^([A-Z][a-zA-Z\s&@]+(?:Residences?|Towers?|Hill|House|Nine|Waterfront|Handy|Paterson|Promont|Emerald|Shenton|Newton|Zion|Hijauan|Cairnhill|Attitude|Leonie|Wharf|Abode|Tribeca|Haus))$', line)
                    if name_match:
                        current_property['name'] = name_match.group(1)
            
            # Add last property
            if current_property and self._is_valid_property(current_property):
                properties.append(current_property)
            
            print(f"✅ Extracted {len(properties)} properties from page text")
            
        except Exception as e:
            print(f"❌ Text extraction error: {e}")
        
        return properties
    
    def _extract_from_elements(self):
        """Extract properties from DOM elements"""
        properties = []
        
        try:
            # Try different selectors for property containers
            selectors = [
                '[data-testid*="listing"]',
                '[class*="listing"]',
                '[class*="property"]',
                '[class*="card"]',
                'article',
                'div[class*="item"]'
            ]
            
            for selector in selectors:
                try:
                    elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    if elements:
                        print(f"   Found {len(elements)} elements with: {selector}")
                        
                        for i, element in enumerate(elements[:20]):
                            try:
                                text = element.text
                                if 'S$' in text and 'Beds' in text:
                                    prop = self._parse_element_text(text, i)
                                    if prop:
                                        properties.append(prop)
                            except:
                                continue
                        
                        if properties:
                            break
                except:
                    continue
            
        except Exception as e:
            print(f"❌ Element extraction error: {e}")
        
        return properties
    
    def _parse_element_text(self, text, index):
        """Parse property data from element text"""
        try:
            prop = {
                'id': f'property_{index}',
                'extraction_timestamp': datetime.now().isoformat(),
                'source': 'PropertyGuru'
            }
            
            # Extract price
            price_match = re.search(r'S\$\s*([\d,]+)', text)
            if price_match:
                prop['price'] = int(price_match.group(1).replace(',', ''))
            
            # Extract bedrooms
            bed_match = re.search(r'(\d+)\s+Beds?', text)
            if bed_match:
                prop['bedrooms'] = int(bed_match.group(1))
            
            # Extract area
            area_match = re.search(r'(\d+,?\d*)\s+sqft', text)
            if area_match:
                prop['area'] = int(area_match.group(1).replace(',', ''))
            
            return prop if self._is_valid_property(prop) else None
            
        except:
            return None
    
    def _is_valid_property(self, prop):
        """Check if property has minimum required data"""
        return 'price' in prop and 'bedrooms' in prop
    
    def save_properties(self, properties):
        """Save properties to JSON file"""
        if not properties:
            print("❌ No properties to save")
            return None

        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')

        # Save to data directory
        import os
        data_dir = os.path.join(os.path.dirname(__file__), '..', 'data')
        os.makedirs(data_dir, exist_ok=True)
        filename = os.path.join(data_dir, f'extraction_{timestamp}.json')

        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(properties, f, indent=2, ensure_ascii=False)

        print(f"💾 Saved {len(properties)} properties to {filename}")
        return filename
    
    def close(self):
        """Close browser connection"""
        if self.driver:
            self.driver.quit()

def main():
    scraper = SmartPropertyScraper()
    
    try:
        # Connect and navigate
        if not scraper.connect_and_navigate():
            return
        
        # Handle Cloudflare and wait for page load
        scraper.handle_cloudflare_and_wait()
        
        # Extract properties
        properties = scraper.extract_properties_smart()
        
        if properties:
            print(f"\n🎉 SUCCESS! Extracted {len(properties)} properties")
            
            # Show sample
            print("\n📋 Sample Properties:")
            for i, prop in enumerate(properties[:5], 1):
                print(f"   {i}. {prop.get('name', 'Property')}")
                print(f"      Price: S$ {prop.get('price', 'N/A'):,}" if isinstance(prop.get('price'), int) else f"      Price: {prop.get('price', 'N/A')}")
                print(f"      Bedrooms: {prop.get('bedrooms', 'N/A')}")
                print(f"      Area: {prop.get('area', 'N/A')} sqft")
            
            # Save data
            filename = scraper.save_properties(properties)
            print(f"\n✅ Extraction complete! Data saved to: {filename}")
            
        else:
            print("❌ No properties extracted")
    
    except Exception as e:
        print(f"❌ Error: {e}")
    
    finally:
        scraper.close()

if __name__ == "__main__":
    main()
