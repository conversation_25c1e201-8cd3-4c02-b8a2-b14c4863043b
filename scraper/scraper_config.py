"""
Configuration settings for PropertyGuru scraper
"""
from dataclasses import dataclass
from typing import List, Tuple, Optional


@dataclass
class ScrapingConfig:
    """Configuration for PropertyGuru scraping"""

    # Browser settings
    headless: bool = True  # Run browser in background
    window_size: Tuple[int, int] = (1920, 1080)

    # Anti-detection settings
    delay_range: Tuple[float, float] = (2.0, 5.0)  # Random delay between requests
    user_agent_rotation: bool = True
    proxy_rotation: bool = False  # Enable if you have proxies

    # Scraping limits
    max_pages_per_type: Optional[int] = None  # None for unlimited
    max_properties_total: Optional[int] = None  # None for unlimited
    max_concurrent_requests: int = 1  # Keep at 1 to be respectful

    # Property types to scrape
    listing_types: List[str] = None  # ["sale", "rent"]
    property_types: List[str] = None  # ["HDB", "Condo", "Landed", "Commercial"]
    districts: List[int] = None  # [1, 2, 3, ...] or None for all

    # Data settings
    save_to_database: bool = True
    save_to_json: bool = True
    save_images: bool = False  # Download property images

    # Error handling
    max_retries: int = 3
    retry_delay: float = 10.0
    continue_on_error: bool = True

    # Output settings
    output_directory: str = "scraped_data"
    log_level: str = "INFO"  # DEBUG, INFO, WARNING, ERROR

    def __post_init__(self):
        """Set default values"""
        if self.listing_types is None:
            self.listing_types = ["sale", "rent"]

        if self.property_types is None:
            self.property_types = ["HDB", "Condo", "Landed", "Commercial"]


# Predefined configurations
CONFIGS = {
    "test": ScrapingConfig(
        headless=False,
        max_pages_per_type=2,
        max_properties_total=50,
        delay_range=(1.0, 2.0),
        listing_types=["sale"],
        log_level="DEBUG"
    ),

    "development": ScrapingConfig(
        headless=True,
        max_pages_per_type=10,
        max_properties_total=500,
        delay_range=(2.0, 4.0),
        listing_types=["sale", "rent"]
    ),

    "production": ScrapingConfig(
        headless=True,
        max_pages_per_type=None,  # Unlimited
        max_properties_total=None,  # Unlimited
        delay_range=(5.0, 12.0),  # Very conservative delays
        listing_types=["sale", "rent"],
        user_agent_rotation=True,
        max_retries=5,
        retry_delay=60.0  # Longer retry delays
    ),

    "quick_sample": ScrapingConfig(
        headless=True,
        max_pages_per_type=1,
        max_properties_total=20,
        delay_range=(1.0, 2.0),
        listing_types=["sale"],
        save_to_database=False
    )
}


def get_config(config_name: str = "development") -> ScrapingConfig:
    """Get predefined configuration"""
    if config_name not in CONFIGS:
        raise ValueError(f"Unknown config: {config_name}. Available: {list(CONFIGS.keys())}")

    return CONFIGS[config_name]


# Search filters for targeted scraping
SEARCH_FILTERS = {
    "luxury_properties": {
        "min_price": 2000000,  # S$2M+
        "property_types": ["Condo", "Landed"],
        "districts": [9, 10, 11]  # Prime districts
    },

    "hdb_flats": {
        "property_types": ["HDB"],
        "max_price": 1000000
    },

    "rental_properties": {
        "listing_types": ["rent"],
        "min_bedrooms": 2
    },

    "investment_properties": {
        "property_types": ["Condo", "HDB"],
        "districts": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15]
    }
}


# URL patterns for different search types
URL_PATTERNS = {
    "sale": "https://www.propertyguru.com.sg/property-for-sale",
    "rent": "https://www.propertyguru.com.sg/property-for-rent",
    "new_launch": "https://www.propertyguru.com.sg/new-launch",
    "commercial": "https://www.propertyguru.com.sg/commercial-property-for-sale"
}


# User agents for rotation
USER_AGENTS = [
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
    "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Safari/605.1.15",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:120.0) Gecko/20100101 Firefox/120.0"
]
