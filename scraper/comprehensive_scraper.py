"""
Comprehensive PropertyGuru Scraper for Singapore Property Market
Targets all 52,383 properties with advanced anti-detection and VPN integration
"""

import os
import sys
import time
import json
import random
import logging
import subprocess
from datetime import datetime
from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass, asdict
from pathlib import Path

# Selenium imports
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.common.action_chains import ActionChains
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.common.exceptions import (
    TimeoutException, NoSuchElementException,
    WebDriverException, StaleElementReferenceException
)

# Web scraping imports
import requests
from bs4 import BeautifulSoup
from webdriver_manager.chrome import ChromeDriverManager

# Data processing
import pandas as pd
from urllib.parse import urljoin, urlparse, parse_qs

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('comprehensive_scraper.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


@dataclass
class PropertyData:
    """Comprehensive property data structure"""
    # Basic Information
    property_id: str = ""
    title: str = ""
    description: str = ""
    property_type: str = ""
    property_subtype: str = ""
    listing_type: str = ""
    status: str = ""
    listing_date: str = ""

    # Pricing Information
    price: int = 0
    price_psf: float = 0.0
    original_price: int = 0
    price_change_percentage: float = 0.0
    maintenance_fee: int = 0
    rental_yield: float = 0.0
    days_on_market: int = 0

    # Property Specifications
    bedrooms: int = 0
    bathrooms: int = 0
    floor_area_sqft: int = 0
    land_area_sqft: int = 0
    balcony_area_sqft: int = 0
    unit_number: str = ""
    floor_level: str = ""
    total_floors: int = 0
    facing_direction: str = ""
    furnishing: str = ""
    parking_lots: int = 0
    built_year: int = 0

    # Building & Development
    building_name: str = ""
    developer_name: str = ""
    architect: str = ""
    total_units: int = 0
    tenure: str = ""
    top_date: str = ""
    facilities: List[str] = None
    security_features: List[str] = None

    # Location Intelligence
    address: str = ""
    postal_code: str = ""
    district: str = ""
    subzone: str = ""
    planning_area: str = ""
    latitude: float = 0.0
    longitude: float = 0.0
    nearest_mrt: str = ""
    mrt_distance_m: int = 0
    mrt_walking_time: int = 0

    # Market Context
    nearby_schools: List[str] = None
    nearby_malls: List[str] = None
    nearby_hospitals: List[str] = None
    bus_stops: List[str] = None
    comparable_sales: List[Dict] = None
    market_trends: Dict = None

    # Agent & Media
    agent_id: str = ""
    agent_name: str = ""
    agent_phone: str = ""
    agent_cea_number: str = ""
    agent_agency: str = ""
    images: List[str] = None
    virtual_tour_url: str = ""
    floor_plan_images: List[str] = None

    # Metadata
    scraped_at: str = ""
    data_quality_score: float = 0.0
    source_url: str = ""

    def __post_init__(self):
        """Initialize list fields if None"""
        if self.facilities is None:
            self.facilities = []
        if self.security_features is None:
            self.security_features = []
        if self.nearby_schools is None:
            self.nearby_schools = []
        if self.nearby_malls is None:
            self.nearby_malls = []
        if self.nearby_hospitals is None:
            self.nearby_hospitals = []
        if self.bus_stops is None:
            self.bus_stops = []
        if self.comparable_sales is None:
            self.comparable_sales = []
        if self.market_trends is None:
            self.market_trends = {}
        if self.images is None:
            self.images = []
        if self.floor_plan_images is None:
            self.floor_plan_images = []


class VPNManager:
    """Manage SurfShark VPN CLI for IP rotation"""

    def __init__(self):
        self.vpn_active = False
        self.current_server = None
        self.available_servers = [
            "sg-sng.prod.surfshark.com",  # Singapore
            "my-kul.prod.surfshark.com",  # Malaysia
            "th-bkk.prod.surfshark.com",  # Thailand
            "id-jkt.prod.surfshark.com",  # Indonesia
            "ph-mnl.prod.surfshark.com",  # Philippines
        ]

    def connect_vpn(self, server: str = None) -> bool:
        """Connect to VPN server"""
        try:
            if server is None:
                server = random.choice(self.available_servers)

            logger.info(f"Connecting to VPN server: {server}")

            # Disconnect any existing connection
            self.disconnect_vpn()

            # Connect to new server
            result = subprocess.run([
                "surfshark-vpn", "connect", server
            ], capture_output=True, text=True, timeout=30)

            if result.returncode == 0:
                self.vpn_active = True
                self.current_server = server
                logger.info(f"Successfully connected to VPN: {server}")

                # Wait for connection to stabilize
                time.sleep(5)
                return True
            else:
                logger.error(f"Failed to connect to VPN: {result.stderr}")
                return False

        except subprocess.TimeoutExpired:
            logger.error("VPN connection timeout")
            return False
        except FileNotFoundError:
            logger.warning("SurfShark VPN CLI not found, continuing without VPN")
            return False
        except Exception as e:
            logger.error(f"VPN connection error: {e}")
            return False

    def disconnect_vpn(self) -> bool:
        """Disconnect from VPN"""
        try:
            if not self.vpn_active:
                return True

            logger.info("Disconnecting from VPN")
            result = subprocess.run([
                "surfshark-vpn", "disconnect"
            ], capture_output=True, text=True, timeout=15)

            self.vpn_active = False
            self.current_server = None

            if result.returncode == 0:
                logger.info("Successfully disconnected from VPN")
                time.sleep(3)  # Wait for disconnection
                return True
            else:
                logger.warning(f"VPN disconnect warning: {result.stderr}")
                return True  # Continue anyway

        except Exception as e:
            logger.error(f"VPN disconnect error: {e}")
            return False

    def rotate_server(self) -> bool:
        """Rotate to a different VPN server"""
        try:
            # Get a different server
            available = [s for s in self.available_servers if s != self.current_server]
            if not available:
                available = self.available_servers

            new_server = random.choice(available)
            return self.connect_vpn(new_server)

        except Exception as e:
            logger.error(f"VPN rotation error: {e}")
            return False

    def get_current_ip(self) -> str:
        """Get current public IP address"""
        try:
            response = requests.get("https://httpbin.org/ip", timeout=10)
            return response.json().get("origin", "Unknown")
        except Exception as e:
            logger.error(f"Failed to get IP: {e}")
            return "Unknown"


class StealthWebDriver:
    """Advanced stealth WebDriver with anti-detection measures"""

    def __init__(self, headless: bool = True, proxy: str = None, use_google_auth: bool = True):
        self.driver = None
        self.headless = headless
        self.proxy = proxy
        self.use_google_auth = use_google_auth
        self.user_agents = [
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Safari/605.1.15",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/121.0"
        ]

    def create_driver(self) -> webdriver.Chrome:
        """Create stealth Chrome WebDriver with optional Google authentication"""
        try:
            # Use Google-authenticated driver if enabled
            if self.use_google_auth:
                try:
                    from .google_auth_driver import GoogleAuthenticatedDriver
                    google_driver = GoogleAuthenticatedDriver(headless=self.headless)
                    driver = google_driver.create_driver()
                    self.driver = driver
                    self.google_driver_manager = google_driver
                    logger.info("Google-authenticated WebDriver created successfully")
                    return driver
                except Exception as e:
                    logger.warning(f"Google auth driver failed, falling back to stealth: {e}")

            # Fallback to regular stealth driver
            options = Options()

            # Basic stealth options
            if self.headless:
                options.add_argument("--headless=new")

            # Anti-detection arguments
            options.add_argument("--no-sandbox")
            options.add_argument("--disable-dev-shm-usage")
            options.add_argument("--disable-blink-features=AutomationControlled")
            options.add_experimental_option("excludeSwitches", ["enable-automation"])
            options.add_experimental_option('useAutomationExtension', False)

            # Realistic browser settings
            options.add_argument("--window-size=1920,1080")
            options.add_argument("--start-maximized")
            options.add_argument(f"--user-agent={random.choice(self.user_agents)}")

            # Performance optimizations
            options.add_argument("--disable-extensions")
            options.add_argument("--disable-plugins")
            # Remove image/JS disabling for Google auth compatibility

            # Memory optimizations
            options.add_argument("--memory-pressure-off")
            options.add_argument("--max_old_space_size=4096")

            # Proxy configuration
            if self.proxy:
                options.add_argument(f"--proxy-server={self.proxy}")

            # Create service
            service = Service(ChromeDriverManager().install())

            # Create driver
            driver = webdriver.Chrome(service=service, options=options)

            # Execute stealth script
            driver.execute_script("""
                Object.defineProperty(navigator, 'webdriver', {
                    get: () => undefined,
                });
            """)

            # Set realistic timeouts
            driver.implicitly_wait(10)
            driver.set_page_load_timeout(60)  # Longer for authenticated sessions

            self.driver = driver
            logger.info("Stealth WebDriver created successfully")
            return driver

        except Exception as e:
            logger.error(f"Failed to create WebDriver: {e}")
            raise

    def human_like_delay(self, min_seconds: float = 2.0, max_seconds: float = 8.0):
        """Human-like random delay"""
        delay = random.uniform(min_seconds, max_seconds)
        logger.debug(f"Human delay: {delay:.2f}s")
        time.sleep(delay)

    def simulate_human_behavior(self):
        """Simulate human browsing behavior"""
        try:
            # Random mouse movements
            actions = ActionChains(self.driver)

            # Move to random positions
            for _ in range(random.randint(2, 5)):
                x = random.randint(100, 800)
                y = random.randint(100, 600)
                actions.move_by_offset(x, y)
                actions.pause(random.uniform(0.5, 1.5))

            actions.perform()

            # Random scrolling
            scroll_amount = random.randint(200, 800)
            self.driver.execute_script(f"window.scrollBy(0, {scroll_amount});")

            self.human_like_delay(1, 3)

        except Exception as e:
            logger.debug(f"Human behavior simulation error: {e}")

    def close(self):
        """Close the WebDriver"""
        if self.driver:
            try:
                self.driver.quit()
                logger.info("WebDriver closed")
            except Exception as e:
                logger.error(f"Error closing WebDriver: {e}")


class ComprehensivePropertyGuruScraper:
    """
    Comprehensive PropertyGuru scraper with advanced anti-detection measures
    Targets all 52,383 properties across Singapore districts
    """

    def __init__(self,
                 headless: bool = True,
                 use_vpn: bool = True,
                 max_pages: int = None,
                 delay_range: Tuple[float, float] = (5.0, 15.0),
                 session_duration_minutes: int = 60):

        self.headless = headless
        self.use_vpn = use_vpn
        self.max_pages = max_pages
        self.delay_range = delay_range
        self.session_duration_minutes = session_duration_minutes

        # Initialize components
        self.vpn_manager = VPNManager() if use_vpn else None
        self.stealth_driver = None
        self.driver = None

        # Target URL with all Singapore districts
        self.target_url = (
            "https://www.propertyguru.com.sg/property-for-sale?"
            "freetext=D01+Boat+Quay+%2F+Raffles+Place+%2F+Marina%2C+"
            "D02+Chinatown+%2F+Tanjong+Pagar%2C+"
            "D03+Alexandra+%2F+Commonwealth%2C+"
            "D04+Harbourfront+%2F+Telok+Blangah%2C+"
            "D05+Buona+Vista+%2F+West+Coast+%2F+Clementi+New+Town%2C+"
            "D06+City+Hall+%2F+Clarke+Quay%2C+"
            "D07+Beach+Road+%2F+Bugis+%2F+Rochor%2C+"
            "D08+Farrer+Park+%2F+Serangoon+Rd%2C+"
            "D09+Orchard+%2F+River+Valley%2C+"
            "D10+Tanglin+%2F+Holland+%2F+Bukit+Timah%2C+"
            "D11+Newton+%2F+Novena%2C+"
            "D21+Clementi+Park+%2F+Upper+Bukit+Timah%2C+"
            "D12+Balestier+%2F+Toa+Payoh%2C+"
            "D13+Macpherson+%2F+Potong+Pasir%2C+"
            "D14+Eunos+%2F+Geylang+%2F+Paya+Lebar%2C+"
            "D15+East+Coast+%2F+Marine+Parade%2C+"
            "D16+Bedok+%2F+Upper+East+Coast%2C+"
            "D17+Changi+Airport+%2F+Changi+Village%2C+"
            "D18+Pasir+Ris+%2F+Tampines%2C+"
            "D19+Hougang+%2F+Punggol+%2F+Sengkang%2C+"
            "D20+Ang+Mo+Kio+%2F+Bishan+%2F+Thomson%2C+"
            "D22+Boon+Lay+%2F+Jurong+%2F+Tuas%2C+"
            "D23+Dairy+Farm+%2F+Bukit+Panjang+%2F+Choa+Chu+Kang%2C+"
            "D24+Lim+Chu+Kang+%2F+Tengah%2C+"
            "D25+Admiralty+%2F+Woodlands%2C+"
            "D26+Mandai+%2F+Upper+Thomson%2C+"
            "D27+Sembawang+%2F+Yishun%2C+"
            "D28+Seletar+%2F+Yio+Chu+Kang&"
            "&".join([f"districtCode=D{i:02d}" for i in range(1, 29) if i != 21] + ["districtCode=D21"]) +
            "&isCommercial=false"
        )

        # Statistics tracking
        self.stats = {
            "session_start": None,
            "pages_scraped": 0,
            "properties_found": 0,
            "properties_detailed": 0,
            "errors": 0,
            "vpn_rotations": 0,
            "current_ip": "Unknown"
        }

        # Results storage
        self.scraped_properties = []
        self.failed_urls = []

    def initialize_session(self) -> bool:
        """Initialize scraping session with VPN and WebDriver"""
        try:
            logger.info("🚀 Initializing comprehensive scraping session")
            self.stats["session_start"] = datetime.now()

            # Connect to VPN if enabled
            if self.use_vpn and self.vpn_manager:
                logger.info("🔒 Connecting to VPN...")
                if self.vpn_manager.connect_vpn():
                    self.stats["current_ip"] = self.vpn_manager.get_current_ip()
                    logger.info(f"✅ VPN connected. Current IP: {self.stats['current_ip']}")
                else:
                    logger.warning("⚠️ VPN connection failed, continuing without VPN")

            # Initialize stealth WebDriver with Google authentication
            logger.info("🕷️ Initializing Google-authenticated WebDriver...")
            self.stealth_driver = StealthWebDriver(headless=self.headless, use_google_auth=True)
            self.driver = self.stealth_driver.create_driver()

            logger.info("✅ Session initialized successfully")
            return True

        except Exception as e:
            logger.error(f"❌ Failed to initialize session: {e}")
            return False

    def scrape_all_properties(self) -> Dict[str, Any]:
        """Main method to scrape all properties"""
        try:
            if not self.initialize_session():
                return {"error": "Failed to initialize session"}

            logger.info("🎯 Starting comprehensive PropertyGuru scraping")
            logger.info(f"📊 Target: All Singapore districts (D01-D28)")
            logger.info(f"🔗 URL: {self.target_url[:100]}...")

            # Navigate to target URL with Google authentication
            logger.info("🌐 Navigating to PropertyGuru with Google session...")

            # Use Google-authenticated navigation if available
            if hasattr(self.stealth_driver, 'google_driver_manager'):
                success = self.stealth_driver.google_driver_manager.navigate_to_propertyguru(self.target_url)
                if not success:
                    logger.warning("Google navigation failed, trying direct navigation")
                    self.driver.get(self.target_url)
            else:
                self.driver.get(self.target_url)

            self.stealth_driver.human_like_delay(3, 8)

            # Check for Cloudflare or bot detection
            if self.check_for_blocking():
                logger.error("🚫 Bot detection or Cloudflare blocking detected")
                return self.handle_blocking_scenario()

            # Start pagination scraping
            page_num = 1
            while True:
                logger.info(f"📄 Scraping page {page_num}")

                # Check session limits
                if self.should_rotate_session():
                    if not self.rotate_session():
                        logger.warning("Session rotation failed, continuing...")

                # Scrape current page
                page_properties = self.scrape_current_page()

                if not page_properties:
                    logger.info("No more properties found, ending scraping")
                    break

                # Process each property for detailed information
                for prop_summary in page_properties:
                    detailed_prop = self.scrape_property_details(prop_summary)
                    if detailed_prop:
                        self.scraped_properties.append(detailed_prop)
                        self.stats["properties_detailed"] += 1

                self.stats["pages_scraped"] += 1
                self.stats["properties_found"] += len(page_properties)

                logger.info(f"✅ Page {page_num}: {len(page_properties)} properties")
                logger.info(f"📊 Total: {len(self.scraped_properties)} detailed properties")

                # Check limits
                if self.max_pages and page_num >= self.max_pages:
                    logger.info(f"Reached page limit: {self.max_pages}")
                    break

                # Navigate to next page
                if not self.go_to_next_page():
                    logger.info("No more pages available")
                    break

                page_num += 1
                self.stealth_driver.human_like_delay(*self.delay_range)

            # Compile results
            results = self.compile_results()

            # Save results
            self.save_results(results)

            return results

        except Exception as e:
            logger.error(f"❌ Scraping failed: {e}")
            return {"error": str(e), "partial_results": self.scraped_properties}

        finally:
            self.cleanup_session()

    def check_for_blocking(self) -> bool:
        """Check if we're being blocked by Cloudflare or bot detection"""
        try:
            # Check page title and content for blocking indicators
            page_title = self.driver.title.lower()
            page_source = self.driver.page_source.lower()

            blocking_indicators = [
                "attention required",
                "cloudflare",
                "access denied",
                "blocked",
                "captcha",
                "security check",
                "bot detection"
            ]

            for indicator in blocking_indicators:
                if indicator in page_title or indicator in page_source:
                    logger.warning(f"🚫 Blocking detected: {indicator}")
                    return True

            # Check if we can find property listings
            try:
                WebDriverWait(self.driver, 10).until(
                    EC.presence_of_element_located((By.CLASS_NAME, "listing-card"))
                )
                return False
            except TimeoutException:
                logger.warning("🚫 No property listings found - possible blocking")
                return True

        except Exception as e:
            logger.error(f"Error checking for blocking: {e}")
            return True

    def handle_blocking_scenario(self) -> Dict[str, Any]:
        """Handle scenario where we're being blocked"""
        logger.info("🔄 Attempting to bypass blocking...")

        strategies = [
            self.try_vpn_rotation,
            self.try_user_agent_rotation,
            self.try_longer_delays,
            self.try_alternative_endpoints
        ]

        for strategy in strategies:
            logger.info(f"🔧 Trying strategy: {strategy.__name__}")
            if strategy():
                logger.info("✅ Blocking bypass successful")
                return {"status": "bypass_successful"}

        logger.error("❌ All bypass strategies failed")
        return {
            "error": "Cloudflare/Bot detection blocking access",
            "status": "blocked",
            "attempted_strategies": [s.__name__ for s in strategies],
            "recommendation": "Consider using residential proxies or manual intervention"
        }

    def try_vpn_rotation(self) -> bool:
        """Try rotating VPN server"""
        try:
            if self.vpn_manager and self.vpn_manager.rotate_server():
                self.stats["vpn_rotations"] += 1
                self.stats["current_ip"] = self.vpn_manager.get_current_ip()
                logger.info(f"🔄 VPN rotated. New IP: {self.stats['current_ip']}")

                # Refresh page with new IP
                self.driver.refresh()
                self.stealth_driver.human_like_delay(5, 10)

                return not self.check_for_blocking()
            return False
        except Exception as e:
            logger.error(f"VPN rotation failed: {e}")
            return False

    def try_user_agent_rotation(self) -> bool:
        """Try rotating user agent"""
        try:
            # Create new driver with different user agent
            self.stealth_driver.close()
            self.stealth_driver = StealthWebDriver(headless=self.headless)
            self.driver = self.stealth_driver.create_driver()

            # Navigate to target URL
            self.driver.get(self.target_url)
            self.stealth_driver.human_like_delay(5, 10)

            return not self.check_for_blocking()
        except Exception as e:
            logger.error(f"User agent rotation failed: {e}")
            return False

    def try_longer_delays(self) -> bool:
        """Try using longer delays"""
        try:
            logger.info("🐌 Using extended delays...")
            self.stealth_driver.human_like_delay(15, 30)

            # Simulate more human behavior
            self.stealth_driver.simulate_human_behavior()

            # Refresh page
            self.driver.refresh()
            self.stealth_driver.human_like_delay(10, 20)

            return not self.check_for_blocking()
        except Exception as e:
            logger.error(f"Extended delays failed: {e}")
            return False

    def try_alternative_endpoints(self) -> bool:
        """Try alternative PropertyGuru endpoints"""
        try:
            alternative_urls = [
                "https://www.propertyguru.com.sg/property-for-sale",
                "https://www.propertyguru.com.sg/singapore-property-listing",
                "https://www.propertyguru.com.sg/property-search"
            ]

            for url in alternative_urls:
                logger.info(f"🔗 Trying alternative URL: {url}")
                self.driver.get(url)
                self.stealth_driver.human_like_delay(5, 10)

                if not self.check_for_blocking():
                    return True

            return False
        except Exception as e:
            logger.error(f"Alternative endpoints failed: {e}")
            return False

    def scrape_current_page(self) -> List[Dict[str, Any]]:
        """Scrape properties from current page"""
        try:
            # Wait for page to load
            WebDriverWait(self.driver, 15).until(
                EC.presence_of_element_located((By.TAG_NAME, "body"))
            )

            # Scroll to load dynamic content
            self.scroll_page_gradually()

            # Find property cards
            property_cards = self.driver.find_elements(By.CSS_SELECTOR,
                "[data-testid='listing-card'], .listing-card, .property-card")

            if not property_cards:
                logger.warning("No property cards found on current page")
                return []

            properties = []
            for card in property_cards:
                try:
                    prop_data = self.extract_property_summary(card)
                    if prop_data:
                        properties.append(prop_data)
                except Exception as e:
                    logger.debug(f"Error extracting property card: {e}")
                    continue

            logger.info(f"📋 Extracted {len(properties)} properties from current page")
            return properties

        except Exception as e:
            logger.error(f"Error scraping current page: {e}")
            return []

    def extract_property_summary(self, card_element) -> Dict[str, Any]:
        """Extract basic property information from listing card"""
        try:
            prop_data = {}

            # Property title and link
            title_elem = card_element.find_element(By.CSS_SELECTOR,
                "h3 a, .listing-title a, .property-title a")
            prop_data["title"] = title_elem.text.strip()
            prop_data["detail_url"] = title_elem.get_attribute("href")

            # Price
            price_elem = card_element.find_element(By.CSS_SELECTOR,
                ".price, .listing-price, .property-price")
            prop_data["price_text"] = price_elem.text.strip()
            prop_data["price"] = self.parse_price(prop_data["price_text"])

            # Property type
            try:
                type_elem = card_element.find_element(By.CSS_SELECTOR,
                    ".property-type, .listing-type")
                prop_data["property_type"] = type_elem.text.strip()
            except NoSuchElementException:
                prop_data["property_type"] = "Unknown"

            # Bedrooms and bathrooms
            try:
                specs_elem = card_element.find_element(By.CSS_SELECTOR,
                    ".property-specs, .listing-specs")
                specs_text = specs_elem.text
                prop_data["bedrooms"] = self.extract_bedrooms(specs_text)
                prop_data["bathrooms"] = self.extract_bathrooms(specs_text)
                prop_data["floor_area_sqft"] = self.extract_floor_area(specs_text)
            except NoSuchElementException:
                prop_data["bedrooms"] = 0
                prop_data["bathrooms"] = 0
                prop_data["floor_area_sqft"] = 0

            # Location
            try:
                location_elem = card_element.find_element(By.CSS_SELECTOR,
                    ".property-location, .listing-location")
                prop_data["address"] = location_elem.text.strip()
                prop_data["district"] = self.extract_district(prop_data["address"])
            except NoSuchElementException:
                prop_data["address"] = "Unknown"
                prop_data["district"] = "Unknown"

            # Image
            try:
                img_elem = card_element.find_element(By.CSS_SELECTOR, "img")
                prop_data["main_image"] = img_elem.get_attribute("src")
            except NoSuchElementException:
                prop_data["main_image"] = ""

            return prop_data

        except Exception as e:
            logger.debug(f"Error extracting property summary: {e}")
            return None

    def scrape_property_details(self, property_summary: Dict[str, Any]) -> PropertyData:
        """Scrape detailed information from individual property page"""
        try:
            detail_url = property_summary.get("detail_url")
            if not detail_url:
                return None

            logger.debug(f"🔍 Scraping details for: {property_summary.get('title', 'Unknown')[:50]}...")

            # Navigate to property detail page
            self.driver.get(detail_url)
            self.stealth_driver.human_like_delay(3, 7)

            # Wait for page to load
            WebDriverWait(self.driver, 15).until(
                EC.presence_of_element_located((By.TAG_NAME, "body"))
            )

            # Create PropertyData object
            prop_data = PropertyData()

            # Fill basic information from summary
            prop_data.title = property_summary.get("title", "")
            prop_data.price = property_summary.get("price", 0)
            prop_data.property_type = property_summary.get("property_type", "")
            prop_data.bedrooms = property_summary.get("bedrooms", 0)
            prop_data.bathrooms = property_summary.get("bathrooms", 0)
            prop_data.floor_area_sqft = property_summary.get("floor_area_sqft", 0)
            prop_data.address = property_summary.get("address", "")
            prop_data.district = property_summary.get("district", "")
            prop_data.source_url = detail_url
            prop_data.scraped_at = datetime.now().isoformat()

            # Extract detailed information
            self.extract_detailed_specs(prop_data)
            self.extract_agent_information(prop_data)
            self.extract_building_information(prop_data)
            self.extract_location_intelligence(prop_data)
            self.extract_media_content(prop_data)

            # Calculate data quality score
            prop_data.data_quality_score = self.calculate_quality_score(prop_data)

            return prop_data

        except Exception as e:
            logger.error(f"Error scraping property details: {e}")
            self.failed_urls.append(property_summary.get("detail_url", ""))
            return None

    def scroll_page_gradually(self):
        """Gradually scroll page to load dynamic content"""
        try:
            # Get page height
            last_height = self.driver.execute_script("return document.body.scrollHeight")

            # Scroll in increments
            scroll_increment = 500
            current_position = 0

            while current_position < last_height:
                # Scroll down
                self.driver.execute_script(f"window.scrollTo(0, {current_position});")
                current_position += scroll_increment

                # Random delay
                time.sleep(random.uniform(0.5, 1.5))

                # Check if new content loaded
                new_height = self.driver.execute_script("return document.body.scrollHeight")
                if new_height > last_height:
                    last_height = new_height

            # Scroll back to top
            self.driver.execute_script("window.scrollTo(0, 0);")
            time.sleep(1)

        except Exception as e:
            logger.debug(f"Error scrolling page: {e}")

    def go_to_next_page(self) -> bool:
        """Navigate to next page of results"""
        try:
            # Look for next page button
            next_selectors = [
                "a[aria-label='Next page']",
                ".pagination-next",
                ".next-page",
                "a:contains('Next')",
                ".pagination a:last-child"
            ]

            for selector in next_selectors:
                try:
                    next_button = self.driver.find_element(By.CSS_SELECTOR, selector)
                    if next_button.is_enabled() and next_button.is_displayed():
                        # Scroll to button
                        self.driver.execute_script("arguments[0].scrollIntoView(true);", next_button)
                        self.stealth_driver.human_like_delay(1, 3)

                        # Click next button
                        next_button.click()

                        # Wait for page to load
                        self.stealth_driver.human_like_delay(3, 8)

                        return True
                except NoSuchElementException:
                    continue

            logger.info("No next page button found")
            return False

        except Exception as e:
            logger.error(f"Error navigating to next page: {e}")
            return False

    def parse_price(self, price_text: str) -> int:
        """Parse price from text"""
        try:
            # Remove currency symbols and formatting
            price_clean = price_text.replace("S$", "").replace("$", "").replace(",", "").strip()

            # Handle million and k multipliers
            import re

            # Check for million
            if "million" in price_clean.lower():
                numbers = re.findall(r'(\d+\.?\d*)', price_clean)
                if numbers:
                    return int(float(numbers[0]) * 1000000)

            # Check for k (thousands)
            elif "k" in price_clean.lower():
                numbers = re.findall(r'(\d+\.?\d*)', price_clean)
                if numbers:
                    return int(float(numbers[0]) * 1000)

            # Regular number
            else:
                numbers = re.findall(r'\d+\.?\d*', price_clean)
                if numbers:
                    return int(float(numbers[0]))

            return 0
        except Exception:
            return 0

    def extract_bedrooms(self, specs_text: str) -> int:
        """Extract number of bedrooms"""
        try:
            import re
            bedroom_match = re.search(r'(\d+)\s*(?:bed|br|bedroom)', specs_text.lower())
            if bedroom_match:
                bedrooms = int(bedroom_match.group(1))
                return min(bedrooms, 10)  # Cap at reasonable number
            return 0
        except Exception:
            return 0

    def extract_bathrooms(self, specs_text: str) -> int:
        """Extract number of bathrooms"""
        try:
            import re
            bathroom_match = re.search(r'(\d+)\s*(?:bath|bathroom)', specs_text.lower())
            if bathroom_match:
                bathrooms = int(bathroom_match.group(1))
                return min(bathrooms, 10)  # Cap at reasonable number
            return 0
        except Exception:
            return 0

    def extract_floor_area(self, specs_text: str) -> int:
        """Extract floor area in sqft"""
        try:
            import re
            area_match = re.search(r'(\d+(?:,\d+)?)\s*(?:sqft|sq ft|square feet)', specs_text.lower())
            if area_match:
                area_str = area_match.group(1).replace(",", "")
                return int(area_str)
            return 0
        except Exception:
            return 0

    def extract_district(self, address: str) -> str:
        """Extract district from address"""
        try:
            import re
            district_match = re.search(r'D(\d{2})', address.upper())
            if district_match:
                return f"D{district_match.group(1)}"

            # District mapping based on area names
            district_mapping = {
                "boat quay": "D01", "raffles place": "D01", "marina": "D01",
                "chinatown": "D02", "tanjong pagar": "D02",
                "alexandra": "D03", "commonwealth": "D03",
                "harbourfront": "D04", "telok blangah": "D04",
                "buona vista": "D05", "west coast": "D05", "clementi": "D05",
                "city hall": "D06", "clarke quay": "D06",
                "beach road": "D07", "bugis": "D07", "rochor": "D07",
                "farrer park": "D08", "serangoon": "D08",
                "orchard": "D09", "river valley": "D09",
                "tanglin": "D10", "holland": "D10", "bukit timah": "D10",
                "newton": "D11", "novena": "D11",
                "balestier": "D12", "toa payoh": "D12",
                "macpherson": "D13", "potong pasir": "D13",
                "eunos": "D14", "geylang": "D14", "paya lebar": "D14",
                "east coast": "D15", "marine parade": "D15",
                "bedok": "D16", "upper east coast": "D16",
                "changi": "D17", "pasir ris": "D18", "tampines": "D18",
                "hougang": "D19", "punggol": "D19", "sengkang": "D19",
                "ang mo kio": "D20", "bishan": "D20", "thomson": "D20",
                "clementi park": "D21", "upper bukit timah": "D21",
                "boon lay": "D22", "jurong": "D22", "tuas": "D22",
                "dairy farm": "D23", "bukit panjang": "D23", "choa chu kang": "D23",
                "lim chu kang": "D24", "tengah": "D24",
                "admiralty": "D25", "woodlands": "D25",
                "mandai": "D26", "upper thomson": "D26",
                "sembawang": "D27", "yishun": "D27",
                "seletar": "D28", "yio chu kang": "D28"
            }

            address_lower = address.lower()
            for area, district in district_mapping.items():
                if area in address_lower:
                    return district

            return "Unknown"
        except Exception:
            return "Unknown"

    def extract_detailed_specs(self, prop_data: PropertyData):
        """Extract detailed property specifications"""
        try:
            # Look for detailed specs section
            specs_selectors = [
                ".property-details-specs",
                ".listing-details",
                ".property-specifications",
                ".details-section"
            ]

            for selector in specs_selectors:
                try:
                    specs_section = self.driver.find_element(By.CSS_SELECTOR, selector)
                    specs_text = specs_section.text

                    # Extract various specifications
                    prop_data.built_year = self.extract_built_year(specs_text)
                    prop_data.tenure = self.extract_tenure(specs_text)
                    prop_data.furnishing = self.extract_furnishing(specs_text)
                    prop_data.floor_level = self.extract_floor_level(specs_text)
                    prop_data.facing_direction = self.extract_facing_direction(specs_text)
                    prop_data.parking_lots = self.extract_parking_lots(specs_text)

                    break
                except NoSuchElementException:
                    continue

        except Exception as e:
            logger.debug(f"Error extracting detailed specs: {e}")

    def extract_agent_information(self, prop_data: PropertyData):
        """Extract agent contact information"""
        try:
            # Look for agent section
            agent_selectors = [
                ".agent-details",
                ".listing-agent",
                ".contact-agent",
                ".agent-info"
            ]

            for selector in agent_selectors:
                try:
                    agent_section = self.driver.find_element(By.CSS_SELECTOR, selector)

                    # Agent name
                    try:
                        name_elem = agent_section.find_element(By.CSS_SELECTOR,
                            ".agent-name, .contact-name, h3, h4")
                        prop_data.agent_name = name_elem.text.strip()
                    except NoSuchElementException:
                        pass

                    # Agent phone
                    try:
                        phone_elem = agent_section.find_element(By.CSS_SELECTOR,
                            ".agent-phone, .contact-phone, [href^='tel:']")
                        prop_data.agent_phone = phone_elem.text.strip()
                    except NoSuchElementException:
                        pass

                    # Agent agency
                    try:
                        agency_elem = agent_section.find_element(By.CSS_SELECTOR,
                            ".agent-agency, .agency-name, .company-name")
                        prop_data.agent_agency = agency_elem.text.strip()
                    except NoSuchElementException:
                        pass

                    # CEA number
                    try:
                        cea_elem = agent_section.find_element(By.CSS_SELECTOR,
                            ".cea-number, .license-number")
                        prop_data.agent_cea_number = cea_elem.text.strip()
                    except NoSuchElementException:
                        pass

                    break
                except NoSuchElementException:
                    continue

        except Exception as e:
            logger.debug(f"Error extracting agent information: {e}")

    def extract_building_information(self, prop_data: PropertyData):
        """Extract building and development information"""
        try:
            # Look for building details
            building_selectors = [
                ".building-details",
                ".development-info",
                ".project-details"
            ]

            for selector in building_selectors:
                try:
                    building_section = self.driver.find_element(By.CSS_SELECTOR, selector)
                    building_text = building_section.text

                    # Extract building information
                    prop_data.building_name = self.extract_building_name(building_text)
                    prop_data.developer_name = self.extract_developer_name(building_text)
                    prop_data.total_units = self.extract_total_units(building_text)
                    prop_data.facilities = self.extract_facilities(building_text)

                    break
                except NoSuchElementException:
                    continue

        except Exception as e:
            logger.debug(f"Error extracting building information: {e}")

    def extract_location_intelligence(self, prop_data: PropertyData):
        """Extract location intelligence data"""
        try:
            # Look for location details
            location_selectors = [
                ".location-details",
                ".nearby-amenities",
                ".location-info"
            ]

            for selector in location_selectors:
                try:
                    location_section = self.driver.find_element(By.CSS_SELECTOR, selector)
                    location_text = location_section.text

                    # Extract location data
                    prop_data.nearest_mrt = self.extract_nearest_mrt(location_text)
                    prop_data.mrt_distance_m = self.extract_mrt_distance(location_text)
                    prop_data.nearby_schools = self.extract_nearby_schools(location_text)
                    prop_data.nearby_malls = self.extract_nearby_malls(location_text)

                    break
                except NoSuchElementException:
                    continue

        except Exception as e:
            logger.debug(f"Error extracting location intelligence: {e}")

    def extract_media_content(self, prop_data: PropertyData):
        """Extract images and media content"""
        try:
            # Extract all images
            img_elements = self.driver.find_elements(By.CSS_SELECTOR,
                ".property-images img, .gallery img, .listing-images img")

            images = []
            for img in img_elements:
                src = img.get_attribute("src")
                if src and "placeholder" not in src.lower():
                    images.append(src)

            prop_data.images = list(set(images))  # Remove duplicates

            # Look for virtual tour
            try:
                tour_elem = self.driver.find_element(By.CSS_SELECTOR,
                    "[href*='virtual'], [href*='tour'], .virtual-tour")
                prop_data.virtual_tour_url = tour_elem.get_attribute("href")
            except NoSuchElementException:
                pass

        except Exception as e:
            logger.debug(f"Error extracting media content: {e}")

    # Utility extraction methods
    def extract_built_year(self, text: str) -> int:
        """Extract built year from text"""
        try:
            import re
            # More flexible pattern to match various formats
            year_patterns = [
                r'(?:built|completed|top)\s*:?\s*(?:in\s*)?(\d{4})',
                r'(\d{4})\s*(?:built|completed|top)',
                r'year\s*:?\s*(\d{4})',
                r'(\d{4})'  # Fallback: any 4-digit number that looks like a year
            ]

            text_lower = text.lower()
            for pattern in year_patterns:
                year_match = re.search(pattern, text_lower)
                if year_match:
                    year = int(year_match.group(1))
                    if 1900 <= year <= 2030:
                        return year
            return 0
        except Exception:
            return 0

    def extract_tenure(self, text: str) -> str:
        """Extract tenure information"""
        try:
            text_lower = text.lower()
            if "freehold" in text_lower:
                return "Freehold"
            elif "99" in text_lower and "year" in text_lower:
                return "99-year Leasehold"
            elif "103" in text_lower and "year" in text_lower:
                return "103-year Leasehold"
            elif "leasehold" in text_lower:
                return "Leasehold"
            return "Unknown"
        except Exception:
            return "Unknown"

    def extract_furnishing(self, text: str) -> str:
        """Extract furnishing status"""
        try:
            text_lower = text.lower()
            if "fully furnished" in text_lower:
                return "Fully Furnished"
            elif "partially furnished" in text_lower:
                return "Partially Furnished"
            elif "unfurnished" in text_lower:
                return "Unfurnished"
            return "Unknown"
        except Exception:
            return "Unknown"

    def extract_floor_level(self, text: str) -> str:
        """Extract floor level"""
        try:
            import re
            floor_match = re.search(r'(?:floor|level)\s*:?\s*(\d+(?:-\d+)?)', text.lower())
            if floor_match:
                return floor_match.group(1)
            return "Unknown"
        except Exception:
            return "Unknown"

    def extract_facing_direction(self, text: str) -> str:
        """Extract facing direction"""
        try:
            directions = ["north", "south", "east", "west", "northeast", "northwest", "southeast", "southwest"]
            text_lower = text.lower()
            for direction in directions:
                if f"facing {direction}" in text_lower or f"{direction} facing" in text_lower:
                    return direction.title()
            return "Unknown"
        except Exception:
            return "Unknown"

    def extract_parking_lots(self, text: str) -> int:
        """Extract number of parking lots"""
        try:
            import re
            parking_match = re.search(r'(\d+)\s*(?:parking|car\s*park)', text.lower())
            if parking_match:
                return int(parking_match.group(1))
            return 0
        except Exception:
            return 0

    def extract_building_name(self, text: str) -> str:
        """Extract building name"""
        try:
            lines = text.split('\n')
            for line in lines:
                if any(keyword in line.lower() for keyword in ["building", "tower", "residence", "court", "park"]):
                    return line.strip()
            return "Unknown"
        except Exception:
            return "Unknown"

    def extract_developer_name(self, text: str) -> str:
        """Extract developer name"""
        try:
            import re
            dev_match = re.search(r'(?:developer|developed by)\s*:?\s*([^\n]+)', text.lower())
            if dev_match:
                return dev_match.group(1).strip().title()
            return "Unknown"
        except Exception:
            return "Unknown"

    def extract_total_units(self, text: str) -> int:
        """Extract total number of units"""
        try:
            import re
            units_match = re.search(r'(\d+)\s*(?:units|apartments)', text.lower())
            if units_match:
                return int(units_match.group(1))
            return 0
        except Exception:
            return 0

    def extract_facilities(self, text: str) -> List[str]:
        """Extract building facilities"""
        try:
            facilities = []
            facility_keywords = [
                "swimming pool", "gym", "playground", "tennis court", "basketball court",
                "bbq pit", "function room", "security", "covered parking", "lift",
                "garden", "clubhouse", "sauna", "jacuzzi", "putting green"
            ]

            text_lower = text.lower()
            for facility in facility_keywords:
                if facility in text_lower:
                    facilities.append(facility.title())

            return facilities
        except Exception:
            return []

    def extract_nearest_mrt(self, text: str) -> str:
        """Extract nearest MRT station"""
        try:
            import re
            mrt_match = re.search(r'([A-Za-z\s]+)\s*(?:mrt|station)', text.lower())
            if mrt_match:
                return mrt_match.group(1).strip().title()
            return "Unknown"
        except Exception:
            return "Unknown"

    def extract_mrt_distance(self, text: str) -> int:
        """Extract MRT distance in meters"""
        try:
            import re
            # Look for distance patterns
            dist_match = re.search(r'(\d+(?:\.\d+)?)\s*(?:m|km|meters|kilometres)', text.lower())
            if dist_match:
                distance = float(dist_match.group(1))
                if "km" in text.lower():
                    distance *= 1000
                return int(distance)
            return 0
        except Exception:
            return 0

    def extract_nearby_schools(self, text: str) -> List[str]:
        """Extract nearby schools"""
        try:
            schools = []
            school_keywords = ["primary school", "secondary school", "junior college", "university", "polytechnic"]

            lines = text.split('\n')
            for line in lines:
                line_lower = line.lower()
                if any(keyword in line_lower for keyword in school_keywords):
                    schools.append(line.strip())

            return schools[:5]  # Limit to 5 schools
        except Exception:
            return []

    def extract_nearby_malls(self, text: str) -> List[str]:
        """Extract nearby shopping malls"""
        try:
            malls = []
            mall_keywords = ["mall", "shopping centre", "plaza", "market", "supermarket"]

            lines = text.split('\n')
            for line in lines:
                line_lower = line.lower()
                if any(keyword in line_lower for keyword in mall_keywords):
                    malls.append(line.strip())

            return malls[:5]  # Limit to 5 malls
        except Exception:
            return []

    def calculate_quality_score(self, prop_data: PropertyData) -> float:
        """Calculate data quality score (0-100)"""
        try:
            total_fields = 0
            filled_fields = 0

            # Check all fields
            for field_name, field_value in asdict(prop_data).items():
                total_fields += 1

                if field_value:
                    if isinstance(field_value, str) and field_value not in ["", "Unknown"]:
                        filled_fields += 1
                    elif isinstance(field_value, (int, float)) and field_value > 0:
                        filled_fields += 1
                    elif isinstance(field_value, list) and len(field_value) > 0:
                        filled_fields += 1
                    elif isinstance(field_value, dict) and len(field_value) > 0:
                        filled_fields += 1

            return (filled_fields / total_fields) * 100 if total_fields > 0 else 0

        except Exception:
            return 0.0

    def should_rotate_session(self) -> bool:
        """Check if session should be rotated"""
        try:
            if not self.stats["session_start"]:
                return False

            # Check session duration
            session_duration = (datetime.now() - self.stats["session_start"]).total_seconds() / 60
            if session_duration >= self.session_duration_minutes:
                logger.info(f"🔄 Session duration limit reached: {session_duration:.1f} minutes")
                return True

            # Check error rate
            if self.stats["errors"] > 10:
                logger.info("🔄 High error rate detected, rotating session")
                return True

            # Random rotation (5% chance per page)
            if random.random() < 0.05:
                logger.info("🔄 Random session rotation")
                return True

            return False

        except Exception:
            return False

    def rotate_session(self) -> bool:
        """Rotate session (VPN + WebDriver)"""
        try:
            logger.info("🔄 Rotating session...")

            # Rotate VPN if enabled
            if self.use_vpn and self.vpn_manager:
                if not self.vpn_manager.rotate_server():
                    logger.warning("VPN rotation failed")

            # Create new WebDriver
            self.stealth_driver.close()
            self.stealth_driver = StealthWebDriver(headless=self.headless)
            self.driver = self.stealth_driver.create_driver()

            # Reset session stats
            self.stats["session_start"] = datetime.now()
            self.stats["errors"] = 0

            logger.info("✅ Session rotated successfully")
            return True

        except Exception as e:
            logger.error(f"Session rotation failed: {e}")
            return False

    def compile_results(self) -> Dict[str, Any]:
        """Compile final scraping results"""
        try:
            end_time = datetime.now()
            duration = (end_time - self.stats["session_start"]).total_seconds()

            results = {
                "scraping_summary": {
                    "start_time": self.stats["session_start"].isoformat(),
                    "end_time": end_time.isoformat(),
                    "duration_seconds": duration,
                    "duration_formatted": f"{duration//3600:.0f}h {(duration%3600)//60:.0f}m {duration%60:.0f}s"
                },
                "statistics": {
                    "pages_scraped": self.stats["pages_scraped"],
                    "properties_found": self.stats["properties_found"],
                    "properties_detailed": self.stats["properties_detailed"],
                    "success_rate": (self.stats["properties_detailed"] / max(self.stats["properties_found"], 1)) * 100,
                    "errors": self.stats["errors"],
                    "vpn_rotations": self.stats["vpn_rotations"],
                    "failed_urls": len(self.failed_urls)
                },
                "data_quality": {
                    "average_quality_score": sum(p.data_quality_score for p in self.scraped_properties) / max(len(self.scraped_properties), 1),
                    "properties_with_agent_info": sum(1 for p in self.scraped_properties if p.agent_name),
                    "properties_with_images": sum(1 for p in self.scraped_properties if p.images),
                    "properties_with_facilities": sum(1 for p in self.scraped_properties if p.facilities)
                },
                "properties": [asdict(prop) for prop in self.scraped_properties],
                "failed_urls": self.failed_urls,
                "target_url": self.target_url,
                "configuration": {
                    "headless": self.headless,
                    "use_vpn": self.use_vpn,
                    "max_pages": self.max_pages,
                    "delay_range": self.delay_range,
                    "session_duration_minutes": self.session_duration_minutes
                }
            }

            return results

        except Exception as e:
            logger.error(f"Error compiling results: {e}")
            return {"error": str(e), "partial_data": [asdict(prop) for prop in self.scraped_properties]}

    def save_results(self, results: Dict[str, Any]):
        """Save results to JSON file"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"comprehensive_scraping_results_{timestamp}.json"

            # Create results directory if it doesn't exist
            results_dir = Path("scraper/comprehensive_results")
            results_dir.mkdir(exist_ok=True)

            filepath = results_dir / filename

            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(results, f, indent=2, ensure_ascii=False, default=str)

            logger.info(f"💾 Results saved to: {filepath}")
            logger.info(f"📊 Total properties: {len(results.get('properties', []))}")

        except Exception as e:
            logger.error(f"Error saving results: {e}")

    def cleanup_session(self):
        """Clean up session resources"""
        try:
            logger.info("🧹 Cleaning up session...")

            # Close WebDriver
            if self.stealth_driver:
                self.stealth_driver.close()

            # Disconnect VPN
            if self.use_vpn and self.vpn_manager:
                self.vpn_manager.disconnect_vpn()

            logger.info("✅ Session cleanup completed")

        except Exception as e:
            logger.error(f"Error during cleanup: {e}")


# Main execution function
def main():
    """Main function to run comprehensive scraping"""
    print("🚀 Comprehensive PropertyGuru Scraper")
    print("=" * 50)
    print("🎯 Target: All 52,383 Singapore properties")
    print("🔒 Features: VPN rotation, stealth mode, comprehensive data extraction")
    print("=" * 50)

    # Configuration options
    configs = {
        "test": {
            "headless": False,
            "use_vpn": False,
            "max_pages": 2,
            "delay_range": (2.0, 5.0),
            "session_duration_minutes": 10
        },
        "development": {
            "headless": True,
            "use_vpn": True,
            "max_pages": 10,
            "delay_range": (5.0, 12.0),
            "session_duration_minutes": 30
        },
        "production": {
            "headless": True,
            "use_vpn": True,
            "max_pages": None,  # Unlimited
            "delay_range": (8.0, 20.0),
            "session_duration_minutes": 60
        }
    }

    # Get configuration choice
    print("\nAvailable configurations:")
    for name, config in configs.items():
        print(f"  {name}: {config['max_pages'] or 'unlimited'} pages, "
              f"VPN: {'Yes' if config['use_vpn'] else 'No'}, "
              f"Headless: {'Yes' if config['headless'] else 'No'}")

    config_choice = input("\nChoose configuration (test/development/production) [test]: ").strip().lower()
    if config_choice not in configs:
        config_choice = "test"

    config = configs[config_choice]
    print(f"\n✅ Using {config_choice} configuration")

    # Initialize scraper
    scraper = ComprehensivePropertyGuruScraper(**config)

    try:
        # Run scraping
        results = scraper.scrape_all_properties()

        # Display results summary
        if "error" not in results:
            print(f"\n🎉 Scraping completed successfully!")
            print(f"📊 Statistics:")
            print(f"   Pages scraped: {results['statistics']['pages_scraped']}")
            print(f"   Properties found: {results['statistics']['properties_found']}")
            print(f"   Properties detailed: {results['statistics']['properties_detailed']}")
            print(f"   Success rate: {results['statistics']['success_rate']:.1f}%")
            print(f"   Average quality score: {results['data_quality']['average_quality_score']:.1f}%")
            print(f"   Duration: {results['scraping_summary']['duration_formatted']}")
        else:
            print(f"\n❌ Scraping failed: {results['error']}")
            if "partial_data" in results:
                print(f"📊 Partial results: {len(results['partial_data'])} properties")

    except KeyboardInterrupt:
        print("\n⏹️ Scraping interrupted by user")
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
    finally:
        scraper.cleanup_session()


if __name__ == "__main__":
    main()

    # Utility extraction methods
    def extract_built_year(self, text: str) -> int:
        """Extract built year from text"""
        try:
            import re
            year_match = re.search(r'(?:built|completed|top)\s*:?\s*(\d{4})', text.lower())
            if year_match:
                year = int(year_match.group(1))
                if 1900 <= year <= 2030:
                    return year
            return 0
        except Exception:
            return 0

    def extract_tenure(self, text: str) -> str:
        """Extract tenure information"""
        try:
            text_lower = text.lower()
            if "freehold" in text_lower:
                return "Freehold"
            elif "99" in text_lower and "year" in text_lower:
                return "99-year Leasehold"
            elif "103" in text_lower and "year" in text_lower:
                return "103-year Leasehold"
            elif "leasehold" in text_lower:
                return "Leasehold"
            return "Unknown"
        except Exception:
            return "Unknown"

    def extract_furnishing(self, text: str) -> str:
        """Extract furnishing status"""
        try:
            text_lower = text.lower()
            if "fully furnished" in text_lower:
                return "Fully Furnished"
            elif "partially furnished" in text_lower:
                return "Partially Furnished"
            elif "unfurnished" in text_lower:
                return "Unfurnished"
            return "Unknown"
        except Exception:
            return "Unknown"

    def extract_floor_level(self, text: str) -> str:
        """Extract floor level"""
        try:
            import re
            floor_match = re.search(r'(?:floor|level)\s*:?\s*(\d+(?:-\d+)?)', text.lower())
            if floor_match:
                return floor_match.group(1)
            return "Unknown"
        except Exception:
            return "Unknown"

    def extract_facing_direction(self, text: str) -> str:
        """Extract facing direction"""
        try:
            directions = ["north", "south", "east", "west", "northeast", "northwest", "southeast", "southwest"]
            text_lower = text.lower()
            for direction in directions:
                if f"facing {direction}" in text_lower or f"{direction} facing" in text_lower:
                    return direction.title()
            return "Unknown"
        except Exception:
            return "Unknown"

    def extract_parking_lots(self, text: str) -> int:
        """Extract number of parking lots"""
        try:
            import re
            parking_match = re.search(r'(\d+)\s*(?:parking|car\s*park)', text.lower())
            if parking_match:
                return int(parking_match.group(1))
            return 0
        except Exception:
            return 0

    def extract_building_name(self, text: str) -> str:
        """Extract building name"""
        try:
            lines = text.split('\n')
            for line in lines:
                if any(keyword in line.lower() for keyword in ["building", "tower", "residence", "court", "park"]):
                    return line.strip()
            return "Unknown"
        except Exception:
            return "Unknown"

    def extract_developer_name(self, text: str) -> str:
        """Extract developer name"""
        try:
            import re
            dev_match = re.search(r'(?:developer|developed by)\s*:?\s*([^\n]+)', text.lower())
            if dev_match:
                return dev_match.group(1).strip().title()
            return "Unknown"
        except Exception:
            return "Unknown"

    def extract_total_units(self, text: str) -> int:
        """Extract total number of units"""
        try:
            import re
            units_match = re.search(r'(\d+)\s*(?:units|apartments)', text.lower())
            if units_match:
                return int(units_match.group(1))
            return 0
        except Exception:
            return 0

    def extract_facilities(self, text: str) -> List[str]:
        """Extract building facilities"""
        try:
            facilities = []
            facility_keywords = [
                "swimming pool", "gym", "playground", "tennis court", "basketball court",
                "bbq pit", "function room", "security", "covered parking", "lift",
                "garden", "clubhouse", "sauna", "jacuzzi", "putting green"
            ]

            text_lower = text.lower()
            for facility in facility_keywords:
                if facility in text_lower:
                    facilities.append(facility.title())

            return facilities
        except Exception:
            return []

    def extract_nearest_mrt(self, text: str) -> str:
        """Extract nearest MRT station"""
        try:
            import re
            mrt_match = re.search(r'([A-Za-z\s]+)\s*(?:mrt|station)', text.lower())
            if mrt_match:
                return mrt_match.group(1).strip().title()
            return "Unknown"
        except Exception:
            return "Unknown"

    def extract_mrt_distance(self, text: str) -> int:
        """Extract MRT distance in meters"""
        try:
            import re
            # Look for distance patterns
            dist_match = re.search(r'(\d+(?:\.\d+)?)\s*(?:m|km|meters|kilometres)', text.lower())
            if dist_match:
                distance = float(dist_match.group(1))
                if "km" in text.lower():
                    distance *= 1000
                return int(distance)
            return 0
        except Exception:
            return 0

    def extract_nearby_schools(self, text: str) -> List[str]:
        """Extract nearby schools"""
        try:
            schools = []
            school_keywords = ["primary school", "secondary school", "junior college", "university", "polytechnic"]

            lines = text.split('\n')
            for line in lines:
                line_lower = line.lower()
                if any(keyword in line_lower for keyword in school_keywords):
                    schools.append(line.strip())

            return schools[:5]  # Limit to 5 schools
        except Exception:
            return []

    def extract_nearby_malls(self, text: str) -> List[str]:
        """Extract nearby shopping malls"""
        try:
            malls = []
            mall_keywords = ["mall", "shopping centre", "plaza", "market", "supermarket"]

            lines = text.split('\n')
            for line in lines:
                line_lower = line.lower()
                if any(keyword in line_lower for keyword in mall_keywords):
                    malls.append(line.strip())

            return malls[:5]  # Limit to 5 malls
        except Exception:
            return []

    def calculate_quality_score(self, prop_data: PropertyData) -> float:
        """Calculate data quality score (0-100)"""
        try:
            total_fields = 0
            filled_fields = 0

            # Check all fields
            for field_name, field_value in asdict(prop_data).items():
                total_fields += 1

                if field_value:
                    if isinstance(field_value, str) and field_value not in ["", "Unknown"]:
                        filled_fields += 1
                    elif isinstance(field_value, (int, float)) and field_value > 0:
                        filled_fields += 1
                    elif isinstance(field_value, list) and len(field_value) > 0:
                        filled_fields += 1
                    elif isinstance(field_value, dict) and len(field_value) > 0:
                        filled_fields += 1

            return (filled_fields / total_fields) * 100 if total_fields > 0 else 0

        except Exception:
            return 0.0

    def should_rotate_session(self) -> bool:
        """Check if session should be rotated"""
        try:
            if not self.stats["session_start"]:
                return False

            # Check session duration
            session_duration = (datetime.now() - self.stats["session_start"]).total_seconds() / 60
            if session_duration >= self.session_duration_minutes:
                logger.info(f"🔄 Session duration limit reached: {session_duration:.1f} minutes")
                return True

            # Check error rate
            if self.stats["errors"] > 10:
                logger.info("🔄 High error rate detected, rotating session")
                return True

            # Random rotation (5% chance per page)
            if random.random() < 0.05:
                logger.info("🔄 Random session rotation")
                return True

            return False

        except Exception:
            return False

    def rotate_session(self) -> bool:
        """Rotate session (VPN + WebDriver)"""
        try:
            logger.info("🔄 Rotating session...")

            # Rotate VPN if enabled
            if self.use_vpn and self.vpn_manager:
                if not self.vpn_manager.rotate_server():
                    logger.warning("VPN rotation failed")

            # Create new WebDriver
            self.stealth_driver.close()
            self.stealth_driver = StealthWebDriver(headless=self.headless)
            self.driver = self.stealth_driver.create_driver()

            # Reset session stats
            self.stats["session_start"] = datetime.now()
            self.stats["errors"] = 0

            logger.info("✅ Session rotated successfully")
            return True

        except Exception as e:
            logger.error(f"Session rotation failed: {e}")
            return False

    def compile_results(self) -> Dict[str, Any]:
        """Compile final scraping results"""
        try:
            end_time = datetime.now()
            duration = (end_time - self.stats["session_start"]).total_seconds()

            results = {
                "scraping_summary": {
                    "start_time": self.stats["session_start"].isoformat(),
                    "end_time": end_time.isoformat(),
                    "duration_seconds": duration,
                    "duration_formatted": f"{duration//3600:.0f}h {(duration%3600)//60:.0f}m {duration%60:.0f}s"
                },
                "statistics": {
                    "pages_scraped": self.stats["pages_scraped"],
                    "properties_found": self.stats["properties_found"],
                    "properties_detailed": self.stats["properties_detailed"],
                    "success_rate": (self.stats["properties_detailed"] / max(self.stats["properties_found"], 1)) * 100,
                    "errors": self.stats["errors"],
                    "vpn_rotations": self.stats["vpn_rotations"],
                    "failed_urls": len(self.failed_urls)
                },
                "data_quality": {
                    "average_quality_score": sum(p.data_quality_score for p in self.scraped_properties) / max(len(self.scraped_properties), 1),
                    "properties_with_agent_info": sum(1 for p in self.scraped_properties if p.agent_name),
                    "properties_with_images": sum(1 for p in self.scraped_properties if p.images),
                    "properties_with_facilities": sum(1 for p in self.scraped_properties if p.facilities)
                },
                "properties": [asdict(prop) for prop in self.scraped_properties],
                "failed_urls": self.failed_urls,
                "target_url": self.target_url,
                "configuration": {
                    "headless": self.headless,
                    "use_vpn": self.use_vpn,
                    "max_pages": self.max_pages,
                    "delay_range": self.delay_range,
                    "session_duration_minutes": self.session_duration_minutes
                }
            }

            return results

        except Exception as e:
            logger.error(f"Error compiling results: {e}")
            return {"error": str(e), "partial_data": [asdict(prop) for prop in self.scraped_properties]}

    def save_results(self, results: Dict[str, Any]):
        """Save results to JSON file"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"comprehensive_scraping_results_{timestamp}.json"

            # Create results directory if it doesn't exist
            results_dir = Path("scraper/comprehensive_results")
            results_dir.mkdir(exist_ok=True)

            filepath = results_dir / filename

            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(results, f, indent=2, ensure_ascii=False, default=str)

            logger.info(f"💾 Results saved to: {filepath}")
            logger.info(f"📊 Total properties: {len(results.get('properties', []))}")

        except Exception as e:
            logger.error(f"Error saving results: {e}")

    def cleanup_session(self):
        """Clean up session resources"""
        try:
            logger.info("🧹 Cleaning up session...")

            # Close WebDriver
            if self.stealth_driver:
                self.stealth_driver.close()

            # Disconnect VPN
            if self.use_vpn and self.vpn_manager:
                self.vpn_manager.disconnect_vpn()

            logger.info("✅ Session cleanup completed")

        except Exception as e:
            logger.error(f"Error during cleanup: {e}")
