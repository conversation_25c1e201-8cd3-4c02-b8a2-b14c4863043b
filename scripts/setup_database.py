#!/usr/bin/env python3
"""
Database setup script for PropertyGuru Scraper
"""
import sys
import os
import subprocess
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

from config.settings import settings
from database.connection import DatabaseManager
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def check_postgresql_running():
    """Check if PostgreSQL is running"""
    try:
        result = subprocess.run(['pg_isready'], capture_output=True, text=True)
        return result.returncode == 0
    except FileNotFoundError:
        logger.error("PostgreSQL is not installed or not in PATH")
        return False


def create_database():
    """Create the database if it doesn't exist"""
    try:
        # Parse database URL to get connection details
        db_url = settings.database_url
        
        # Extract database name from URL
        # Format: postgresql://user:password@host:port/database
        if "postgresql://" in db_url:
            parts = db_url.replace("postgresql://", "").split("/")
            if len(parts) > 1:
                db_name = parts[-1]
                base_url = "postgresql://" + "/".join(parts[:-1])
            else:
                db_name = "propertyguru_db"
                base_url = db_url
        else:
            db_name = "propertyguru_db"
            base_url = "postgresql://localhost:5432"
        
        logger.info(f"Creating database: {db_name}")
        
        # Try to create database using psql
        create_cmd = f'psql -c "CREATE DATABASE {db_name};" {base_url}/postgres'
        result = subprocess.run(create_cmd, shell=True, capture_output=True, text=True)
        
        if result.returncode == 0:
            logger.info(f"Database {db_name} created successfully")
            return True
        elif "already exists" in result.stderr:
            logger.info(f"Database {db_name} already exists")
            return True
        else:
            logger.error(f"Failed to create database: {result.stderr}")
            return False
            
    except Exception as e:
        logger.error(f"Error creating database: {e}")
        return False


def run_migrations():
    """Run database migrations"""
    try:
        logger.info("Running database migrations...")
        
        # First, create initial migration if it doesn't exist
        migrations_dir = project_root / "database" / "migrations" / "versions"
        if not any(migrations_dir.glob("*.py")):
            logger.info("Creating initial migration...")
            result = subprocess.run([
                "alembic", "revision", "--autogenerate", "-m", "Initial database schema"
            ], cwd=project_root, capture_output=True, text=True)
            
            if result.returncode != 0:
                logger.error(f"Failed to create migration: {result.stderr}")
                return False
        
        # Run migrations
        result = subprocess.run([
            "alembic", "upgrade", "head"
        ], cwd=project_root, capture_output=True, text=True)
        
        if result.returncode == 0:
            logger.info("Database migrations completed successfully")
            return True
        else:
            logger.error(f"Failed to run migrations: {result.stderr}")
            return False
            
    except Exception as e:
        logger.error(f"Error running migrations: {e}")
        return False


def setup_database():
    """Complete database setup"""
    logger.info("Starting database setup...")
    
    # Check if PostgreSQL is running
    if not check_postgresql_running():
        logger.error("PostgreSQL is not running. Please start PostgreSQL first.")
        logger.info("On macOS with Homebrew: brew services start postgresql")
        logger.info("On Ubuntu: sudo systemctl start postgresql")
        return False
    
    # Create database
    if not create_database():
        logger.error("Failed to create database")
        return False
    
    # Initialize database with tables
    try:
        logger.info("Initializing database tables...")
        DatabaseManager.init_db()
        logger.info("Database tables created successfully")
    except Exception as e:
        logger.error(f"Failed to initialize database: {e}")
        return False
    
    # Test database connection
    try:
        health = DatabaseManager.health_check()
        if health["status"] == "healthy":
            logger.info("Database setup completed successfully!")
            logger.info(f"Database URL: {settings.database_url}")
            return True
        else:
            logger.error(f"Database health check failed: {health}")
            return False
    except Exception as e:
        logger.error(f"Database health check failed: {e}")
        return False


def main():
    """Main setup function"""
    print("PropertyGuru Scraper Database Setup")
    print("=" * 40)
    
    if setup_database():
        print("\n✅ Database setup completed successfully!")
        print("\nNext steps:")
        print("1. Start the API server: python -m uvicorn api.main:app --reload")
        print("2. Visit http://localhost:8000/docs for API documentation")
        print("3. Test the health endpoint: curl http://localhost:8000/health")
    else:
        print("\n❌ Database setup failed!")
        print("\nTroubleshooting:")
        print("1. Make sure PostgreSQL is installed and running")
        print("2. Check your DATABASE_URL in .env file")
        print("3. Ensure you have proper database permissions")
        sys.exit(1)


if __name__ == "__main__":
    main()
