#!/usr/bin/env python3
"""
🚀 PropertyGuru Smart Scraper Runner
Main entry point for running the smart scraper with Cloudflare bypass
"""

import sys
import os
import logging
from datetime import datetime

# Add project root to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('smart_scraper.log'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

def main():
    """Main function to run the smart scraper"""
    
    print("🚀 PropertyGuru Smart Scraper")
    print("=" * 50)
    print("🛡️ Features: Cloudflare bypass, intelligent extraction, market analysis")
    print("🎯 Target: PropertyGuru Singapore property listings")
    print()
    
    try:
        # Import and run the smart scraper
        sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'scraper'))
        from main_scraper import SmartPropertyScraper
        
        print("🔧 Initializing smart scraper...")
        scraper = SmartPropertyScraper()
        
        # Connect and navigate
        if not scraper.connect_and_navigate():
            print("❌ Failed to connect to browser or navigate to PropertyGuru")
            return 1
        
        # Handle Cloudflare and wait for page load
        print("🛡️ Handling Cloudflare protection...")
        scraper.handle_cloudflare_and_wait()
        
        # Extract properties
        print("🔍 Extracting property data...")
        properties = scraper.extract_properties_smart()
        
        if properties:
            print(f"\n🎉 SUCCESS! Extracted {len(properties)} properties")
            
            # Calculate statistics
            prices = [p['price'] for p in properties if 'price' in p]
            if prices:
                avg_price = sum(prices) / len(prices)
                min_price = min(prices)
                max_price = max(prices)
                
                print(f"💰 Price Statistics:")
                print(f"   Average: S$ {avg_price:,.0f}")
                print(f"   Range: S$ {min_price:,.0f} - S$ {max_price:,.0f}")
            
            # Show sample properties
            print(f"\n📋 Sample Properties:")
            for i, prop in enumerate(properties[:5], 1):
                name = prop.get('name', 'Property')
                price = prop.get('price', 'N/A')
                bedrooms = prop.get('bedrooms', 'N/A')
                area = prop.get('area', 'N/A')
                
                price_str = f"S$ {price:,}" if isinstance(price, int) else str(price)
                print(f"   {i}. {name}")
                print(f"      Price: {price_str} | {bedrooms}BR | {area} sqft")
            
            # Save data
            filename = scraper.save_properties(properties)
            print(f"\n💾 Data saved to: {filename}")
            
            # Run analysis
            print(f"\n📊 Running market analysis...")
            try:
                sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'scraper'))
                from analyzer import PropertyAnalyzer
                analyzer = PropertyAnalyzer(filename)
                analyzer.analyze_all()
                analyzer.find_best_deals()
            except Exception as e:
                print(f"⚠️ Analysis failed: {e}")

            # Run tests
            print(f"\n🧪 Running data validation tests...")
            try:
                sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'tests'))
                from validate_data import run_comprehensive_tests
                success = run_comprehensive_tests()
                if success:
                    print("✅ All validation tests passed!")
                else:
                    print("⚠️ Some validation tests failed")
            except Exception as e:
                print(f"⚠️ Testing failed: {e}")
            
        else:
            print("❌ No properties extracted")
            return 1
        
    except KeyboardInterrupt:
        print("\n⏹️ Scraping interrupted by user")
        return 1
    except Exception as e:
        logger.error(f"Scraping failed: {e}")
        print(f"❌ Scraping failed: {e}")
        return 1
    
    finally:
        # Cleanup
        try:
            if 'scraper' in locals():
                scraper.close()
        except:
            pass
    
    print(f"\n🎯 Scraping completed successfully!")
    print(f"📄 Check logs in: smart_scraper.log")
    return 0

if __name__ == "__main__":
    exit(main())
