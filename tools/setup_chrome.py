#!/usr/bin/env python3
"""
🔧 Chrome Debug Setup Helper
Helps you enable Chrome remote debugging for PropertyGuru scraping
"""

import subprocess
import time
import requests
import json
import os

def kill_chrome():
    """Kill all Chrome processes"""
    try:
        subprocess.run(['pkill', '-f', 'Google Chrome'], check=False)
        print("🔄 Closing existing Chrome instances...")
        time.sleep(3)
    except Exception as e:
        print(f"ℹ️ Chrome processes: {e}")

def start_chrome_with_debug():
    """Start Chrome with remote debugging enabled"""
    chrome_path = "/Applications/Google Chrome.app/Contents/MacOS/Google Chrome"
    
    if not os.path.exists(chrome_path):
        print("❌ Chrome not found at expected location")
        return False
    
    cmd = [
        chrome_path,
        "--remote-debugging-port=9222",
        "--user-data-dir=/tmp/chrome_debug_profile",
        "--disable-web-security",
        "--disable-features=VizDisplayCompositor",
        "--no-first-run",
        "--no-default-browser-check"
    ]
    
    try:
        print("🚀 Starting Chrome with debugging enabled...")
        subprocess.Popen(cmd)
        time.sleep(5)
        return True
    except Exception as e:
        print(f"❌ Failed to start Chrome: {e}")
        return False

def test_debug_connection():
    """Test if Chrome debugging is working"""
    try:
        response = requests.get('http://localhost:9222/json', timeout=5)
        tabs = response.json()
        print(f"✅ Chrome debugging active! Found {len(tabs)} tabs")
        return True
    except Exception as e:
        print(f"❌ Debug connection failed: {e}")
        return False

def open_propertyguru():
    """Open PropertyGuru in a new tab"""
    try:
        url = "https://www.propertyguru.com.sg/property-for-sale"
        response = requests.post(f'http://localhost:9222/json/new?{url}')
        print("🏠 Opening PropertyGuru...")
        time.sleep(3)
        return True
    except Exception as e:
        print(f"❌ Failed to open PropertyGuru: {e}")
        return False

def main():
    print("🔧 Chrome Debug Setup Helper")
    print("=" * 50)
    
    # Step 1: Kill existing Chrome
    kill_chrome()
    
    # Step 2: Start Chrome with debugging
    if not start_chrome_with_debug():
        print("\n❌ Failed to start Chrome")
        print("💡 Try manually running:")
        print("/Applications/Google\\ Chrome.app/Contents/MacOS/Google\\ Chrome --remote-debugging-port=9222")
        return
    
    # Step 3: Test connection
    print("\n🔍 Testing debug connection...")
    for i in range(10):
        if test_debug_connection():
            break
        print(f"⏳ Waiting for Chrome... ({i+1}/10)")
        time.sleep(2)
    else:
        print("❌ Chrome debugging not responding")
        return
    
    # Step 4: Open PropertyGuru
    print("\n🏠 Opening PropertyGuru...")
    if open_propertyguru():
        print("\n✅ Setup complete!")
        print("🎯 Chrome is ready for scraping")
        print("🔗 Debug URL: http://localhost:9222")
    else:
        print("❌ Failed to open PropertyGuru")

if __name__ == "__main__":
    main()
