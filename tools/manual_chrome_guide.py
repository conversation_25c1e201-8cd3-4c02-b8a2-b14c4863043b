#!/usr/bin/env python3
"""
🔧 Manual Chrome Setup Guide
Use your existing Chrome session to avoid detection
"""

import subprocess
import time
import requests
import json

def check_existing_chrome():
    """Check if Chrome is already running with debugging"""
    try:
        response = requests.get('http://localhost:9222/json', timeout=3)
        tabs = response.json()
        print(f"✅ Found existing Chrome with debugging! {len(tabs)} tabs")
        return True, tabs
    except:
        return False, []

def find_chrome_processes():
    """Find running Chrome processes"""
    try:
        result = subprocess.run(['ps', 'aux'], capture_output=True, text=True)
        chrome_lines = [line for line in result.stdout.split('\n') if 'Google Chrome' in line and 'Helper' not in line]
        return chrome_lines
    except:
        return []

def enable_debugging_existing_chrome():
    """Guide user to enable debugging on existing Chrome"""
    print("\n🔧 Manual Chrome Setup Guide")
    print("=" * 50)
    
    # Check if debugging already enabled
    has_debug, tabs = check_existing_chrome()
    if has_debug:
        print("✅ Chrome debugging already enabled!")
        print(f"📊 Found {len(tabs)} tabs")
        return True
    
    # Check for running Chrome
    chrome_procs = find_chrome_processes()
    if chrome_procs:
        print("🔍 Found running Chrome processes:")
        for proc in chrome_procs[:3]:  # Show first 3
            print(f"   {proc[:80]}...")
        
        print("\n📋 To enable debugging on your existing Chrome:")
        print("1. 🚪 Close Chrome completely (Cmd+Q)")
        print("2. ⏳ Wait 3 seconds")
        print("3. 🖥️  Open Terminal and run:")
        print("   /Applications/Google\\ Chrome.app/Contents/MacOS/Google\\ Chrome --remote-debugging-port=9222 &")
        print("4. 🌐 Chrome will restart with debugging enabled")
        print("5. 🔑 Log back into your Google account if needed")
        
    else:
        print("❌ No Chrome processes found")
        print("\n📋 To start Chrome with debugging:")
        print("1. 🖥️  Open Terminal and run:")
        print("   /Applications/Google\\ Chrome.app/Contents/MacOS/Google\\ Chrome --remote-debugging-port=9222 &")
        print("2. 🌐 Chrome will start with debugging enabled")
        print("3. 🔑 Log into your Google account")
    
    print("\n⚠️  IMPORTANT:")
    print("   • Don't use --disable-web-security (causes detection)")
    print("   • Keep your existing user profile")
    print("   • Stay logged into Google account")
    
    return False

def wait_for_debugging():
    """Wait for user to enable debugging"""
    print("\n⏳ Waiting for Chrome debugging to be enabled...")
    print("   Press Ctrl+C to cancel")
    
    for i in range(60):  # Wait up to 60 seconds
        try:
            has_debug, tabs = check_existing_chrome()
            if has_debug:
                print(f"\n✅ Chrome debugging enabled! Found {len(tabs)} tabs")
                return True
            
            print(f"   Checking... ({i+1}/60)", end='\r')
            time.sleep(1)
            
        except KeyboardInterrupt:
            print("\n❌ Cancelled by user")
            return False
    
    print("\n❌ Timeout waiting for Chrome debugging")
    return False

def list_available_tabs():
    """List available Chrome tabs"""
    try:
        response = requests.get('http://localhost:9222/json', timeout=5)
        tabs = response.json()
        
        print(f"\n📋 Available Chrome tabs ({len(tabs)}):")
        print("-" * 50)
        
        for i, tab in enumerate(tabs):
            title = tab.get('title', 'No title')[:50]
            url = tab.get('url', 'No URL')[:60]
            tab_id = tab.get('id', 'No ID')
            
            print(f"{i+1:2d}. {title}")
            print(f"    URL: {url}")
            print(f"    ID:  {tab_id}")
            print()
        
        return tabs
    except Exception as e:
        print(f"❌ Failed to list tabs: {e}")
        return []

def open_propertyguru_tab():
    """Open PropertyGuru in a new tab"""
    try:
        url = "https://www.propertyguru.com.sg/property-for-sale"
        response = requests.post(f'http://localhost:9222/json/new?{url}')
        if response.status_code == 200:
            print("🏠 Opened PropertyGuru in new tab")
            time.sleep(2)
            return True
        else:
            print(f"❌ Failed to open tab: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Failed to open PropertyGuru: {e}")
        return False

def main():
    print("🔧 Manual Chrome Setup Guide")
    print("=" * 50)
    print("This tool helps you use your existing Chrome session safely")
    
    # Step 1: Check current state
    if not enable_debugging_existing_chrome():
        # Step 2: Wait for user to enable debugging
        if not wait_for_debugging():
            print("\n❌ Setup failed")
            return
    
    # Step 3: List available tabs
    tabs = list_available_tabs()
    if not tabs:
        print("❌ No tabs found")
        return
    
    # Step 4: Offer to open PropertyGuru
    print("🏠 Would you like to open PropertyGuru? (y/n): ", end='')
    try:
        choice = input().lower().strip()
        if choice in ['y', 'yes']:
            open_propertyguru_tab()
            # Refresh tab list
            list_available_tabs()
    except KeyboardInterrupt:
        print("\n")
    
    print("\n✅ Setup complete!")
    print("🎯 Chrome is ready for scraping")
    print("🔗 Debug URL: http://localhost:9222")
    print("📋 Use manual_chrome_selector.py to choose a tab")

if __name__ == "__main__":
    main()
