#!/usr/bin/env python3
"""
🧪 Test 2-Page Scraping
Quick test to scrape exactly 2 pages
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'scraper'))

from main_scraper import SmartPropertyScraper

def test_2_pages():
    """Test scraping exactly 2 pages"""
    print("🧪 Testing 2-Page Scraping")
    print("=" * 50)
    
    scraper = SmartPropertyScraper()
    
    try:
        # Connect to existing Chrome
        print("🔗 Connecting to Chrome...")
        if not scraper.connect_and_navigate():
            print("❌ Failed to connect")
            return
        
        # Handle Cloudflare
        print("🛡️ Handling Cloudflare...")
        scraper.handle_cloudflare_and_wait()
        
        # Scrape 2 pages
        print("\n🔄 Starting 2-page scraping...")
        properties = scraper.scrape_multiple_pages(max_pages=2)
        
        if properties:
            print(f"\n🎉 SUCCESS! Extracted {len(properties)} properties from 2 pages")
            
            # Show detailed sample
            print("\n📋 Sample Properties:")
            for i, prop in enumerate(properties[:5], 1):
                print(f"   {i}. Price: S$ {prop.get('price', 'N/A'):,}" if isinstance(prop.get('price'), int) else f"   {i}. Price: {prop.get('price', 'N/A')}")
                print(f"      Bedrooms: {prop.get('bedrooms', 'N/A')}")
                print(f"      Area: {prop.get('area', 'N/A')} sqft")
                print(f"      ID: {prop.get('id', 'N/A')}")
                print()
            
            # Save data
            filename = scraper.save_properties(properties)
            print(f"✅ 2-page test complete! Data saved to: {filename}")
            
            # Show statistics
            print(f"\n📊 Statistics:")
            print(f"   Total properties: {len(properties)}")
            print(f"   Average per page: {len(properties) / 2:.1f}")
            
            # Price analysis
            prices = [p.get('price') for p in properties if isinstance(p.get('price'), int)]
            if prices:
                print(f"   Price range: S$ {min(prices):,} - S$ {max(prices):,}")
                print(f"   Average price: S$ {sum(prices) // len(prices):,}")
            
        else:
            print("❌ No properties extracted")
    
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        print("\n🔧 Keeping Chrome open for inspection...")
        # Don't close Chrome so you can inspect the results
        # scraper.close()

if __name__ == "__main__":
    test_2_pages()
