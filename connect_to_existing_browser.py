#!/usr/bin/env python3
"""
Connect to Your Existing Browser for PropertyGuru Scraping
Takes control of your current Chrome session to avoid detection
"""

import sys
import time
import subprocess
import requests
from pathlib import Path

# Add scraper to path
sys.path.append(str(Path(__file__).parent))

from scraper.existing_browser_controller import ExistingBrowserController


def check_chrome_debug_port():
    """Check if Chrome is running with debug port enabled"""
    try:
        response = requests.get("http://localhost:9222/json", timeout=3)
        if response.status_code == 200:
            tabs = response.json()
            print(f"✅ Chrome debug port active with {len(tabs)} tabs")
            return True, tabs
        return False, []
    except:
        return False, []


def enable_chrome_debugging():
    """Help user enable Chrome debugging"""
    print("\n🔧 Setting up Chrome Remote Debugging")
    print("=" * 50)
    print("To connect to your existing browser, we need to enable remote debugging.")
    print()
    print("Option 1: Restart Chrome with debugging (Recommended)")
    print("1. Close all Chrome windows completely")
    print("2. Run this command in Terminal:")
    print("   /Applications/Google\\ Chrome.app/Contents/MacOS/Google\\ Chrome --remote-debugging-port=9222")
    print("3. Chrome will restart with debugging enabled")
    print("4. Navigate to PropertyGuru in the new Chrome window")
    print()
    print("Option 2: Use existing Chrome (if already on PropertyGuru)")
    print("1. Keep your current Chrome window open")
    print("2. We'll try to connect directly")
    print()
    
    choice = input("Choose option (1 or 2): ").strip()
    
    if choice == "1":
        print("\n🚀 Starting Chrome with debugging...")
        try:
            # Start Chrome with debugging
            subprocess.Popen([
                "/Applications/Google Chrome.app/Contents/MacOS/Google Chrome",
                "--remote-debugging-port=9222",
                "--user-data-dir=/tmp/chrome_debug"
            ])
            print("✅ Chrome started with debugging enabled")
            print("📝 Please navigate to PropertyGuru in the new Chrome window")
            input("⏳ Press Enter when you're on PropertyGuru...")
            return True
        except Exception as e:
            print(f"❌ Failed to start Chrome: {e}")
            return False
    
    elif choice == "2":
        print("\n🔍 Attempting direct connection...")
        return True
    
    else:
        print("❌ Invalid choice")
        return False


def main():
    """Main function to connect and scrape"""
    print("🎮 PropertyGuru Existing Browser Controller")
    print("=" * 60)
    print("🎯 Target: Take control of your existing browser")
    print("🔐 Method: Chrome DevTools Protocol connection")
    print("🚀 Benefit: No new browser = No detection")
    print("=" * 60)
    
    # Check if Chrome debug port is already active
    debug_active, tabs = check_chrome_debug_port()
    
    if not debug_active:
        print("ℹ️ Chrome debugging not detected")
        if not enable_chrome_debugging():
            print("❌ Could not enable Chrome debugging")
            return
        
        # Wait and check again
        print("\n⏳ Waiting for Chrome to start...")
        time.sleep(5)
        debug_active, tabs = check_chrome_debug_port()
    
    if debug_active:
        print(f"✅ Chrome debugging active with {len(tabs)} tabs")
        
        # Show available tabs
        print("\n📄 Available tabs:")
        for i, tab in enumerate(tabs[:5], 1):
            title = tab.get('title', 'Unknown')[:50]
            url = tab.get('url', 'Unknown')[:50]
            print(f"   {i}. {title}")
            print(f"      URL: {url}")
        
        # Connect and start scraping
        controller = ExistingBrowserController()
        
        try:
            print("\n🔗 Connecting to existing browser...")
            if controller.connect_to_existing_browser():
                print("✅ Successfully connected!")
                
                # Navigate to PropertyGuru if not already there
                current_url = controller.driver.current_url
                if "propertyguru" not in current_url.lower():
                    print("🌐 Navigating to PropertyGuru...")
                    target_url = (
                        "https://www.propertyguru.com.sg/property-for-sale?"
                        "districtCode=D01&districtCode=D02&districtCode=D09&districtCode=D10&"
                        "isCommercial=false"
                    )
                    controller.navigate_to_propertyguru(target_url)
                
                # Check for blocking
                print("🛡️ Checking for Cloudflare protection...")
                if controller.check_for_blocking():
                    print("❌ Cloudflare protection detected")
                    print("💡 Try manually solving any CAPTCHA in the browser")
                    input("⏳ Press Enter after solving CAPTCHA...")
                
                # Start scraping
                print("🚀 Starting property extraction...")
                print("📜 The browser will scroll automatically to load all properties")
                print("👀 You can watch the process in your browser window")
                
                properties = controller.scroll_and_extract_properties()
                
                if properties:
                    print(f"\n🎉 SUCCESS: Extracted {len(properties)} properties!")
                    
                    # Save results
                    timestamp = time.strftime("%Y%m%d_%H%M%S")
                    filename = f"existing_browser_results_{timestamp}.json"
                    
                    import json
                    with open(filename, 'w') as f:
                        json.dump(properties, f, indent=2)
                    
                    print(f"💾 Results saved to: {filename}")
                    
                    # Show sample results
                    print("\n📋 Sample Properties:")
                    for i, prop in enumerate(properties[:5], 1):
                        print(f"   {i}. {prop.get('title', 'Unknown')[:60]}")
                        print(f"      💰 Price: {prop.get('price', 'Unknown')}")
                        print(f"      📍 Location: {prop.get('location', 'Unknown')}")
                        print(f"      🔗 URL: {prop.get('url', 'Unknown')[:50]}...")
                        print()
                    
                    print("🎯 Next steps:")
                    print("   1. Review the extracted data")
                    print("   2. Refine selectors if needed")
                    print("   3. Scale up to full scraping")
                    
                else:
                    print("❌ No properties extracted")
                    print("💡 May need to adjust selectors or solve CAPTCHA")
                
                input("\n⏳ Press Enter to finish (browser will remain open)...")
                
            else:
                print("❌ Could not connect to browser")
                
        except KeyboardInterrupt:
            print("\n⏹️ Scraping interrupted by user")
        except Exception as e:
            print(f"\n💥 Error: {e}")
        finally:
            controller.close()
    
    else:
        print("❌ Could not establish Chrome debugging connection")
        print("💡 Try restarting Chrome with debugging enabled")


if __name__ == "__main__":
    main()
