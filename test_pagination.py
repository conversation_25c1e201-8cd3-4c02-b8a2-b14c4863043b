#!/usr/bin/env python3
"""
🧪 Test Pagination Functionality
Quick test to verify pagination works before full scraping
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'scraper'))

from main_scraper import SmartPropertyScraper

def test_pagination():
    """Test pagination with 2 pages"""
    print("🧪 Testing Pagination Functionality")
    print("=" * 50)
    
    scraper = SmartPropertyScraper()
    
    try:
        # Connect and navigate
        print("🔗 Connecting to Chrome...")
        if not scraper.connect_and_navigate():
            print("❌ Failed to connect")
            return
        
        # Handle Cloudflare
        print("🛡️ Handling Cloudflare...")
        scraper.handle_cloudflare_and_wait()
        
        # Test pagination info
        print("\n📊 Testing page info detection...")
        page_num, total_pages = scraper.get_current_page_info()
        print(f"Current page: {page_num}")
        print(f"Total pages: {total_pages}")
        
        # Test next button detection
        print("\n🔍 Testing next button detection...")
        next_button = scraper.find_next_button()
        if next_button:
            print(f"✅ Found next button: {next_button.tag_name}")
            print(f"   Text: {next_button.text}")
            print(f"   Enabled: {next_button.is_enabled()}")
            print(f"   Displayed: {next_button.is_displayed()}")
        else:
            print("❌ Next button not found")
        
        # Test 2-page scraping
        print("\n🔄 Testing 2-page scraping...")
        properties = scraper.scrape_multiple_pages(max_pages=2)
        
        if properties:
            print(f"\n🎉 SUCCESS! Extracted {len(properties)} properties from 2 pages")
            
            # Show sample
            print("\n📋 Sample Properties:")
            for i, prop in enumerate(properties[:3], 1):
                print(f"   {i}. Price: S$ {prop.get('price', 'N/A'):,}" if isinstance(prop.get('price'), int) else f"   {i}. Price: {prop.get('price', 'N/A')}")
                print(f"      Bedrooms: {prop.get('bedrooms', 'N/A')}")
                print(f"      Area: {prop.get('area', 'N/A')} sqft")
            
            # Save test data
            filename = scraper.save_properties(properties)
            print(f"\n✅ Test complete! Data saved to: {filename}")
        else:
            print("❌ No properties extracted")
    
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        scraper.close()

if __name__ == "__main__":
    test_pagination()
