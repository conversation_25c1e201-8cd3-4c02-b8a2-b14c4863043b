"""
Comprehensive test suite for the PropertyGuru comprehensive scraper
Tests all components including VPN integration, stealth measures, and data extraction
"""

import pytest
import json
import time
from datetime import datetime
from pathlib import Path
from unittest.mock import Mock, patch, MagicMock

# Import the comprehensive scraper
import sys
sys.path.append(str(Path(__file__).parent.parent))

from scraper.comprehensive_scraper import (
    ComprehensivePropertyGuruScraper,
    VPNManager,
    StealthWebDriver,
    PropertyData
)


class TestVPNManager:
    """Test VPN management functionality"""
    
    def test_vpn_manager_initialization(self):
        """Test VPN manager initialization"""
        vpn_manager = VPNManager()
        
        assert vpn_manager.vpn_active == False
        assert vpn_manager.current_server is None
        assert len(vpn_manager.available_servers) == 5
        assert "sg-sng.prod.surfshark.com" in vpn_manager.available_servers
    
    @patch('subprocess.run')
    def test_vpn_connect_success(self, mock_subprocess):
        """Test successful VPN connection"""
        # Mock successful subprocess call
        mock_subprocess.return_value.returncode = 0
        mock_subprocess.return_value.stderr = ""
        
        vpn_manager = VPNManager()
        result = vpn_manager.connect_vpn("sg-sng.prod.surfshark.com")
        
        assert result == True
        assert vpn_manager.vpn_active == True
        assert vpn_manager.current_server == "sg-sng.prod.surfshark.com"
    
    @patch('subprocess.run')
    def test_vpn_connect_failure(self, mock_subprocess):
        """Test VPN connection failure"""
        # Mock failed subprocess call
        mock_subprocess.return_value.returncode = 1
        mock_subprocess.return_value.stderr = "Connection failed"
        
        vpn_manager = VPNManager()
        result = vpn_manager.connect_vpn("invalid-server")
        
        assert result == False
        assert vpn_manager.vpn_active == False
    
    @patch('subprocess.run')
    def test_vpn_disconnect(self, mock_subprocess):
        """Test VPN disconnection"""
        # Mock successful disconnect
        mock_subprocess.return_value.returncode = 0
        
        vpn_manager = VPNManager()
        vpn_manager.vpn_active = True
        vpn_manager.current_server = "test-server"
        
        result = vpn_manager.disconnect_vpn()
        
        assert result == True
        assert vpn_manager.vpn_active == False
        assert vpn_manager.current_server is None
    
    @patch('requests.get')
    def test_get_current_ip(self, mock_requests):
        """Test getting current IP address"""
        # Mock IP response
        mock_response = Mock()
        mock_response.json.return_value = {"origin": "***********"}
        mock_requests.return_value = mock_response
        
        vpn_manager = VPNManager()
        ip = vpn_manager.get_current_ip()
        
        assert ip == "***********"


class TestStealthWebDriver:
    """Test stealth WebDriver functionality"""
    
    def test_stealth_driver_initialization(self):
        """Test stealth driver initialization"""
        stealth_driver = StealthWebDriver(headless=True)
        
        assert stealth_driver.headless == True
        assert stealth_driver.driver is None
        assert len(stealth_driver.user_agents) == 5
    
    @patch('selenium.webdriver.Chrome')
    @patch('webdriver_manager.chrome.ChromeDriverManager')
    def test_create_driver(self, mock_driver_manager, mock_chrome):
        """Test WebDriver creation"""
        # Mock ChromeDriverManager
        mock_driver_manager.return_value.install.return_value = "/path/to/chromedriver"
        
        # Mock Chrome WebDriver
        mock_driver = Mock()
        mock_chrome.return_value = mock_driver
        
        stealth_driver = StealthWebDriver(headless=True)
        driver = stealth_driver.create_driver()
        
        assert driver == mock_driver
        assert stealth_driver.driver == mock_driver
        
        # Verify stealth script was executed
        mock_driver.execute_script.assert_called()
    
    def test_human_like_delay(self):
        """Test human-like delay functionality"""
        stealth_driver = StealthWebDriver()
        
        start_time = time.time()
        stealth_driver.human_like_delay(0.1, 0.2)
        end_time = time.time()
        
        delay = end_time - start_time
        assert 0.1 <= delay <= 0.3  # Allow some tolerance


class TestPropertyData:
    """Test PropertyData dataclass"""
    
    def test_property_data_initialization(self):
        """Test PropertyData initialization"""
        prop_data = PropertyData()
        
        # Check default values
        assert prop_data.property_id == ""
        assert prop_data.title == ""
        assert prop_data.price == 0
        assert prop_data.bedrooms == 0
        assert prop_data.facilities == []
        assert prop_data.images == []
        assert prop_data.data_quality_score == 0.0
    
    def test_property_data_with_values(self):
        """Test PropertyData with actual values"""
        prop_data = PropertyData(
            property_id="PG123456",
            title="Beautiful Condo in Orchard",
            price=1500000,
            bedrooms=3,
            bathrooms=2,
            property_type="Condo",
            district="D09"
        )
        
        assert prop_data.property_id == "PG123456"
        assert prop_data.title == "Beautiful Condo in Orchard"
        assert prop_data.price == 1500000
        assert prop_data.bedrooms == 3
        assert prop_data.bathrooms == 2
        assert prop_data.property_type == "Condo"
        assert prop_data.district == "D09"


class TestComprehensivePropertyGuruScraper:
    """Test main scraper functionality"""
    
    def test_scraper_initialization(self):
        """Test scraper initialization"""
        scraper = ComprehensivePropertyGuruScraper(
            headless=True,
            use_vpn=False,
            max_pages=5,
            delay_range=(2.0, 5.0)
        )
        
        assert scraper.headless == True
        assert scraper.use_vpn == False
        assert scraper.max_pages == 5
        assert scraper.delay_range == (2.0, 5.0)
        assert scraper.vpn_manager is None  # VPN disabled
        assert len(scraper.scraped_properties) == 0
        assert scraper.stats["pages_scraped"] == 0
    
    def test_scraper_with_vpn(self):
        """Test scraper initialization with VPN"""
        scraper = ComprehensivePropertyGuruScraper(use_vpn=True)
        
        assert scraper.use_vpn == True
        assert scraper.vpn_manager is not None
        assert isinstance(scraper.vpn_manager, VPNManager)
    
    def test_target_url_generation(self):
        """Test target URL contains all districts"""
        scraper = ComprehensivePropertyGuruScraper()
        
        # Check that URL contains all districts D01-D28
        for i in range(1, 29):
            district_code = f"D{i:02d}"
            assert district_code in scraper.target_url
        
        # Check that URL contains key parameters
        assert "property-for-sale" in scraper.target_url
        assert "isCommercial=false" in scraper.target_url
    
    def test_parse_price(self):
        """Test price parsing functionality"""
        scraper = ComprehensivePropertyGuruScraper()
        
        # Test various price formats
        assert scraper.parse_price("S$1,500,000") == 1500000
        assert scraper.parse_price("$2.5 million") == 2500000
        assert scraper.parse_price("800k") == 800000
        assert scraper.parse_price("S$950,000") == 950000
        assert scraper.parse_price("Invalid price") == 0
    
    def test_extract_bedrooms(self):
        """Test bedroom extraction"""
        scraper = ComprehensivePropertyGuruScraper()
        
        assert scraper.extract_bedrooms("3 bedrooms, 2 bathrooms") == 3
        assert scraper.extract_bedrooms("5 bed, 4 bath") == 5
        assert scraper.extract_bedrooms("Studio apartment") == 0
        assert scraper.extract_bedrooms("2 BR, 1 BA") == 2
    
    def test_extract_bathrooms(self):
        """Test bathroom extraction"""
        scraper = ComprehensivePropertyGuruScraper()
        
        assert scraper.extract_bathrooms("3 bedrooms, 2 bathrooms") == 2
        assert scraper.extract_bathrooms("5 bed, 4 bath") == 4
        assert scraper.extract_bathrooms("1 bathroom") == 1
        assert scraper.extract_bathrooms("No bathroom info") == 0
    
    def test_extract_floor_area(self):
        """Test floor area extraction"""
        scraper = ComprehensivePropertyGuruScraper()
        
        assert scraper.extract_floor_area("1,200 sqft") == 1200
        assert scraper.extract_floor_area("850 sq ft") == 850
        assert scraper.extract_floor_area("2,500 square feet") == 2500
        assert scraper.extract_floor_area("No area info") == 0
    
    def test_extract_district(self):
        """Test district extraction"""
        scraper = ComprehensivePropertyGuruScraper()
        
        # Test direct district codes
        assert scraper.extract_district("Property in D09 Orchard") == "D09"
        assert scraper.extract_district("D15 East Coast location") == "D15"
        
        # Test area name mapping
        assert scraper.extract_district("Orchard Road location") == "D09"
        assert scraper.extract_district("Chinatown heritage area") == "D02"
        assert scraper.extract_district("Jurong West") == "D22"
        assert scraper.extract_district("Unknown location") == "Unknown"
    
    def test_extract_built_year(self):
        """Test built year extraction"""
        scraper = ComprehensivePropertyGuruScraper()
        
        assert scraper.extract_built_year("Built: 2015") == 2015
        assert scraper.extract_built_year("Completed in 2020") == 2020
        assert scraper.extract_built_year("TOP: 2018") == 2018
        assert scraper.extract_built_year("Old building") == 0
        assert scraper.extract_built_year("Built: 1850") == 0  # Too old
    
    def test_extract_tenure(self):
        """Test tenure extraction"""
        scraper = ComprehensivePropertyGuruScraper()
        
        assert scraper.extract_tenure("Freehold property") == "Freehold"
        assert scraper.extract_tenure("99-year leasehold") == "99-year Leasehold"
        assert scraper.extract_tenure("103-year lease") == "103-year Leasehold"
        assert scraper.extract_tenure("Leasehold property") == "Leasehold"
        assert scraper.extract_tenure("No tenure info") == "Unknown"
    
    def test_extract_facilities(self):
        """Test facilities extraction"""
        scraper = ComprehensivePropertyGuruScraper()
        
        text = "Facilities include swimming pool, gym, playground, and security"
        facilities = scraper.extract_facilities(text)
        
        assert "Swimming Pool" in facilities
        assert "Gym" in facilities
        assert "Playground" in facilities
        assert "Security" in facilities
    
    def test_calculate_quality_score(self):
        """Test data quality score calculation"""
        scraper = ComprehensivePropertyGuruScraper()
        
        # Create property with some filled fields
        prop_data = PropertyData(
            property_id="PG123",
            title="Test Property",
            price=1000000,
            bedrooms=3,
            bathrooms=2,
            property_type="Condo",
            district="D09"
        )
        
        score = scraper.calculate_quality_score(prop_data)
        
        # Score should be > 0 since some fields are filled
        assert score > 0
        assert score <= 100
    
    def test_should_rotate_session(self):
        """Test session rotation logic"""
        scraper = ComprehensivePropertyGuruScraper(session_duration_minutes=1)
        scraper.stats["session_start"] = datetime.now()
        
        # Should not rotate immediately
        assert scraper.should_rotate_session() == False
        
        # Test error threshold
        scraper.stats["errors"] = 15
        assert scraper.should_rotate_session() == True


def test_comprehensive_scraper_integration():
    """Integration test for comprehensive scraper"""
    print("\n🧪 Running comprehensive scraper integration test...")
    
    # Test configuration
    config = {
        "headless": True,
        "use_vpn": False,  # Disable VPN for testing
        "max_pages": 1,    # Limit to 1 page for testing
        "delay_range": (1.0, 2.0),  # Shorter delays for testing
        "session_duration_minutes": 5
    }
    
    scraper = ComprehensivePropertyGuruScraper(**config)
    
    # Test initialization
    assert scraper.headless == True
    assert scraper.use_vpn == False
    assert scraper.max_pages == 1
    
    # Test utility methods
    assert scraper.parse_price("S$1,500,000") == 1500000
    assert scraper.extract_bedrooms("3 bed, 2 bath") == 3
    assert scraper.extract_district("Orchard Road") == "D09"
    
    print("✅ Integration test passed!")


if __name__ == "__main__":
    # Run basic tests
    test_comprehensive_scraper_integration()
    
    print("\n🚀 Running pytest suite...")
    pytest.main([__file__, "-v"])
