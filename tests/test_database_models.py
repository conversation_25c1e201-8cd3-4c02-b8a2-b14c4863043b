"""
Test database models and schemas
"""
import pytest
from datetime import datetime, date
from decimal import Decimal
from database.models import Property, Agent, PropertyHistory, ScrapingJob, SearchQuery
from database.schemas import PropertyCreate, PropertyResponse, AgentCreate


def test_property_model_creation():
    """Test Property model can be created"""
    property_data = {
        "property_guru_id": "PG123456",
        "title": "Beautiful 3-bedroom condo",
        "description": "Spacious and well-maintained",
        "property_type": "Condo",
        "listing_type": "sale",
        "price": Decimal("800000.00"),
        "bedrooms": 3,
        "bathrooms": 2,
        "address": "123 Orchard Road, Singapore",
        "district": 9,
        "status": "active"
    }
    
    property_obj = Property(**property_data)
    
    assert property_obj.property_guru_id == "PG123456"
    assert property_obj.title == "Beautiful 3-bedroom condo"
    assert property_obj.property_type == "Condo"
    assert property_obj.listing_type == "sale"
    assert property_obj.price == Decimal("800000.00")
    assert property_obj.bedrooms == 3
    assert property_obj.status == "active"


def test_agent_model_creation():
    """Test Agent model can be created"""
    agent_data = {
        "name": "<PERSON>e",
        "agency": "ABC Realty",
        "phone": "+65 9123 4567",
        "email": "<EMAIL>",
        "cea_number": "R123456A"
    }
    
    agent_obj = Agent(**agent_data)
    
    assert agent_obj.name == "John Doe"
    assert agent_obj.agency == "ABC Realty"
    assert agent_obj.phone == "+65 9123 4567"
    assert agent_obj.cea_number == "R123456A"


def test_property_create_schema():
    """Test PropertyCreate schema validation"""
    property_data = {
        "property_guru_id": "PG123456",
        "title": "Beautiful 3-bedroom condo",
        "property_type": "Condo",
        "listing_type": "sale",
        "price": 800000.00,
        "bedrooms": 3,
        "address": "123 Orchard Road, Singapore"
    }
    
    schema = PropertyCreate(**property_data)
    
    assert schema.property_guru_id == "PG123456"
    assert schema.title == "Beautiful 3-bedroom condo"
    assert schema.property_type == "Condo"
    assert schema.listing_type == "sale"
    assert schema.price == 800000.00
    assert schema.bedrooms == 3


def test_property_history_model():
    """Test PropertyHistory model"""
    history_data = {
        "property_id": 1,
        "field_name": "price",
        "old_value": "750000.00",
        "new_value": "800000.00",
        "change_type": "price_change",
        "change_percentage": Decimal("6.67")
    }
    
    history_obj = PropertyHistory(**history_data)
    
    assert history_obj.property_id == 1
    assert history_obj.field_name == "price"
    assert history_obj.change_type == "price_change"
    assert history_obj.change_percentage == Decimal("6.67")


def test_scraping_job_model():
    """Test ScrapingJob model"""
    job_data = {
        "job_id": "job-123-456",
        "job_type": "full_scrape",
        "status": "pending",
        "filters": {"property_type": "Condo", "district": 9}
    }
    
    job_obj = ScrapingJob(**job_data)
    
    assert job_obj.job_id == "job-123-456"
    assert job_obj.job_type == "full_scrape"
    assert job_obj.status == "pending"
    assert job_obj.filters == {"property_type": "Condo", "district": 9}


def test_search_query_model():
    """Test SearchQuery model"""
    query_data = {
        "query_text": "3 bedroom condo in district 9",
        "filters_applied": {"bedrooms": 3, "property_type": "Condo"},
        "results_count": 25,
        "response_time_ms": 150
    }
    
    query_obj = SearchQuery(**query_data)
    
    assert query_obj.query_text == "3 bedroom condo in district 9"
    assert query_obj.results_count == 25
    assert query_obj.response_time_ms == 150


def test_agent_create_schema():
    """Test AgentCreate schema"""
    agent_data = {
        "name": "Jane Smith",
        "agency": "XYZ Properties",
        "phone": "+65 8765 4321",
        "email": "<EMAIL>"
    }
    
    schema = AgentCreate(**agent_data)
    
    assert schema.name == "Jane Smith"
    assert schema.agency == "XYZ Properties"
    assert schema.phone == "+65 8765 4321"
    assert schema.email == "<EMAIL>"
