"""
Test cases for the API endpoints
"""
import pytest
from fastapi.testclient import TestClient
from api.main import app

client = TestClient(app)


def test_root_endpoint():
    """Test the root endpoint"""
    response = client.get("/")
    assert response.status_code == 200
    data = response.json()
    assert data["message"] == "PropertyGuru Scraper API"
    assert data["version"] == "0.1.0"
    assert data["status"] == "running"


def test_health_check():
    """Test the health check endpoint"""
    response = client.get("/health")
    assert response.status_code == 200
    data = response.json()
    # Status will be "degraded" when database is not available
    assert data["status"] in ["healthy", "degraded"]
    assert "database" in data
    assert "redis" in data


def test_invalid_endpoint():
    """Test accessing an invalid endpoint"""
    response = client.get("/invalid")
    assert response.status_code == 404
