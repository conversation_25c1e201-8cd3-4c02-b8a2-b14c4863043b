#!/usr/bin/env python3
"""
🏠 Comprehensive PropertyGuru Data Schema
Based on actual PropertyGuru listing structure
"""

from datetime import datetime
from typing import Optional, List, Dict, Any

class PropertySchema:
    """Comprehensive property data schema for PropertyGuru listings"""
    
    @staticmethod
    def create_property_record() -> Dict[str, Any]:
        """Create a comprehensive property record template"""
        return {
            # Basic Identification
            "id": "",
            "extraction_timestamp": datetime.now().isoformat(),
            "source": "PropertyGuru",
            "listing_url": "",
            
            # Property Name & Address
            "property_name": "",           # e.g., "102A Bidadari Park Drive"
            "full_address": "",            # Complete address
            "street_address": "",          # Street portion
            "postal_code": "",             # Singapore postal code
            "district": "",                # e.g., "D19"
            "district_name": "",           # e.g., "Hougang / Punggol / Sengkang"
            
            # Pricing Information
            "price": None,                 # Main price (int)
            "price_formatted": "",         # e.g., "S$ 1,180,000"
            "price_per_sqft": None,        # Price per sqft (float)
            "price_per_sqft_formatted": "",# e.g., "S$ 1,178.82 psf"
            "currency": "SGD",
            
            # Property Details
            "bedrooms": None,              # Number of bedrooms (int)
            "bathrooms": None,             # Number of bathrooms (int)
            "floor_area_sqft": None,       # Floor area in sqft (int)
            "floor_area_formatted": "",    # e.g., "1,001 sqft"
            "land_area_sqft": None,        # Land area for landed properties
            "land_area_formatted": "",     # e.g., "2,650 sqft (land)"
            
            # Property Type & Tenure
            "property_type": "",           # e.g., "HDB Flat", "Condominium", "Landed"
            "property_subtype": "",        # e.g., "Apartment", "Terraced House"
            "tenure": "",                  # e.g., "99-year Leasehold", "Freehold"
            "built_year": None,            # Year built (int)
            "completion_year": None,       # For new projects
            
            # Location & Transportation
            "nearest_mrt": "",             # e.g., "NE11 Woodleigh MRT Station"
            "mrt_distance": "",            # e.g., "5 min (410 m)"
            "mrt_line": "",                # e.g., "NE11"
            "mrt_station": "",             # e.g., "Woodleigh"
            
            # Listing Information
            "listed_date": "",             # e.g., "Jul 13, 2025"
            "listed_time_ago": "",         # e.g., "23m ago"
            "listing_type": "sale",        # "sale" or "rent"
            
            # Agent Information
            "agent_name": "",              # e.g., "Priscilla Tan"
            "agent_rating": None,          # Agent rating (float)
            "agent_company": "",           # Agency name
            "agent_description": "",       # Agent's listing description
            "agent_photo_url": "",         # Agent photo URL
            
            # Images & Media
            "main_image_url": "",          # Primary listing image
            "image_urls": [],              # List of all image URLs
            "image_count": 0,              # Number of images
            "has_virtual_tour": False,     # Virtual tour available
            "has_video": False,            # Video available
            
            # Property Features & Amenities
            "floor_level": "",             # e.g., "High Floor", "Mid Floor"
            "facing": "",                  # e.g., "North", "South-East"
            "view": "",                    # e.g., "City View", "Sea View"
            "furnishing": "",              # e.g., "Fully Furnished", "Unfurnished"
            "condition": "",               # e.g., "Renovated", "Original"
            
            # Additional Details
            "description": "",             # Full property description
            "highlights": [],              # Key selling points
            "amenities": [],               # Building/development amenities
            "nearby_schools": [],          # Nearby schools
            "nearby_shopping": [],         # Nearby shopping centers
            
            # Market Information
            "market_segment": "",          # e.g., "Mass Market", "Luxury"
            "development_name": "",        # For condos/developments
            "total_units": None,           # Total units in development
            "available_units": None,       # Available units
            
            # Investment Information
            "rental_yield": None,          # Estimated rental yield
            "capital_appreciation": None,  # Historical appreciation
            "maintenance_fee": None,       # Monthly maintenance
            
            # Verification & Quality
            "verified_listing": False,     # PropertyGuru verified
            "featured_listing": False,     # Featured/premium listing
            "exclusive_listing": False,    # Exclusive to agent
            
            # Technical Metadata
            "page_number": None,           # Which page this was found on
            "position_on_page": None,      # Position in search results
            "extraction_method": "",       # How data was extracted
            "data_quality_score": 0.0,    # Completeness score (0-1)
            
            # Raw Data (for debugging)
            "raw_html": "",                # Raw HTML snippet
            "raw_text": "",                # Raw text content
        }
    
    @staticmethod
    def validate_property(property_data: Dict[str, Any]) -> Dict[str, Any]:
        """Validate and clean property data"""
        # Ensure required fields
        if not property_data.get("price") and not property_data.get("price_formatted"):
            property_data["data_quality_score"] = 0.1
        
        # Calculate data quality score
        required_fields = ["property_name", "price", "bedrooms", "floor_area_sqft", "property_type"]
        filled_fields = sum(1 for field in required_fields if property_data.get(field))
        property_data["data_quality_score"] = filled_fields / len(required_fields)
        
        return property_data
    
    @staticmethod
    def get_schema_fields() -> List[str]:
        """Get list of all schema fields"""
        return list(PropertySchema.create_property_record().keys())

# Example usage and field descriptions
FIELD_DESCRIPTIONS = {
    "property_name": "Main property title/name",
    "price": "Numeric price value",
    "bedrooms": "Number of bedrooms",
    "bathrooms": "Number of bathrooms", 
    "floor_area_sqft": "Floor area in square feet",
    "property_type": "HDB Flat, Condominium, Landed, etc.",
    "tenure": "Freehold, 99-year Leasehold, etc.",
    "nearest_mrt": "Closest MRT station with line code",
    "agent_name": "Listing agent name",
    "main_image_url": "Primary property image URL",
    "listed_date": "When property was listed",
    "district": "Singapore district code (D01-D28)"
}

if __name__ == "__main__":
    # Demo the schema
    sample_property = PropertySchema.create_property_record()
    print("🏠 PropertyGuru Data Schema")
    print(f"📊 Total fields: {len(sample_property)}")
    print("\n📋 Key fields:")
    for field, desc in FIELD_DESCRIPTIONS.items():
        print(f"   {field}: {desc}")
