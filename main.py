#!/usr/bin/env python3
"""
🚀 PropertyGuru Intelligent Scraper
Main entry point for the intelligent property scraping system
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from scrapers.intelligent_scraper import FullScaleIntelligentScraper

def main():
    """Main execution function"""
    
    print("🏠 PROPERTYGURU INTELLIGENT SCRAPER")
    print("=" * 60)
    print("🧠 Intelligent sales schema with 100% field completion")
    print("🎯 Complete Singapore property market coverage")
    print("📊 Ready for property agents and market analysis")
    
    # Get scraping parameters
    try:
        print("\n⚙️ SCRAPING CONFIGURATION:")
        max_pages = int(input("📄 How many pages to scrape? (default 2600): ") or "2600")
        start_page = int(input("🔢 Start from which page? (default 1): ") or "1")
        
        print(f"\n🎯 Configuration:")
        print(f"   Pages to scrape: {max_pages}")
        print(f"   Starting page: {start_page}")
        print(f"   Expected properties: ~{max_pages * 20}")
        print(f"   Schema: Intelligent sales format")
        
        confirm = input("\n🚀 Start intelligent scraping? (y/N): ").lower()
        if confirm != 'y':
            print("❌ Scraping cancelled")
            return
        
        # Start scraping
        scraper = FullScaleIntelligentScraper()
        success = scraper.start_full_scale_scraping(max_pages, start_page)
        
        if success:
            print("\n🎉 Intelligent scraping completed successfully!")
        else:
            print("\n❌ Scraping failed")
            
    except KeyboardInterrupt:
        print("\n⏹️ Scraping interrupted by user")
    except Exception as e:
        print(f"\n❌ Error: {e}")

if __name__ == "__main__":
    main()
