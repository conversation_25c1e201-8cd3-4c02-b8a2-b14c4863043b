#!/usr/bin/env python3
"""
🚀 PropertyGuru Smart Scraper - Quick Launcher
Simple launcher for the PropertyGuru smart scraper
"""

import os
import sys
import subprocess

def print_banner():
    """Print welcome banner"""
    print("🏠 PropertyGuru Smart Scraper")
    print("=" * 40)
    print("Choose an option:")
    print()
    print("1. 🔧 Setup Chrome (automatic)")
    print("2. 🎯 Select Chrome Tab (manual)")
    print("3. 🚀 Run Main Scraper")
    print("4. 📊 Analyze Data")
    print("5. 🧪 Validate Data")
    print("6. 🔍 Debug Browser")
    print("7. 🎯 Run All (Scrape + Analyze + Test)")
    print("8. 📚 View Documentation")
    print("9. ❌ Exit")
    print()

def run_script(script_path):
    """Run a Python script"""
    try:
        result = subprocess.run([sys.executable, script_path], 
                              cwd=os.path.dirname(os.path.abspath(__file__)))
        return result.returncode == 0
    except Exception as e:
        print(f"❌ Error running {script_path}: {e}")
        return False

def main():
    """Main launcher function"""
    while True:
        print_banner()
        
        try:
            choice = input("Enter your choice (1-9): ").strip()
            print()

            if choice == '1':
                print("🔧 Setting up Chrome debugging (automatic)...")
                run_script("tools/setup_chrome.py")

            elif choice == '2':
                print("🎯 Selecting Chrome tab (manual)...")
                run_script("tools/manual_chrome_selector.py")

            elif choice == '3':
                print("🚀 Running main scraper...")
                run_script("scraper/main_scraper.py")

            elif choice == '4':
                print("📊 Running data analysis...")
                run_script("scraper/analyzer.py")

            elif choice == '5':
                print("🧪 Running data validation...")
                run_script("tests/validate_data.py")

            elif choice == '6':
                print("🔍 Running browser debugger...")
                run_script("tools/browser_debug.py")

            elif choice == '7':
                print("🎯 Running complete workflow...")
                run_script("tools/run_all.py")

            elif choice == '8':
                print("📚 Documentation available in docs/ folder:")
                print("   - docs/setup_guide.md - Setup instructions")
                print("   - docs/update_summary.md - Technical details")
                print("   - README.md - Main documentation")

            elif choice == '9':
                print("👋 Goodbye!")
                break

            else:
                print("❌ Invalid choice. Please enter 1-9.")
                
        except KeyboardInterrupt:
            print("\n👋 Goodbye!")
            break
        except Exception as e:
            print(f"❌ Error: {e}")
        
        print("\n" + "="*40)
        input("Press Enter to continue...")
        print()

if __name__ == "__main__":
    main()
