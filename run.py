#!/usr/bin/env python3
"""
🚀 PropertyGuru Smart Scraper - Quick Launcher
Simple launcher for the PropertyGuru smart scraper
"""

import os
import sys
import subprocess

def print_banner():
    """Print welcome banner"""
    print("🏠 PropertyGuru Smart Scraper")
    print("=" * 40)
    print("Choose an option:")
    print()
    print("1. 🔧 Setup Chrome (first time only)")
    print("2. 🚀 Run Main Scraper")
    print("3. 📊 Analyze Data")
    print("4. 🧪 Validate Data")
    print("5. 🔍 Debug Browser")
    print("6. 🎯 Run All (Scrape + Analyze + Test)")
    print("7. 📚 View Documentation")
    print("8. ❌ Exit")
    print()

def run_script(script_path):
    """Run a Python script"""
    try:
        result = subprocess.run([sys.executable, script_path], 
                              cwd=os.path.dirname(os.path.abspath(__file__)))
        return result.returncode == 0
    except Exception as e:
        print(f"❌ Error running {script_path}: {e}")
        return False

def main():
    """Main launcher function"""
    while True:
        print_banner()
        
        try:
            choice = input("Enter your choice (1-8): ").strip()
            print()
            
            if choice == '1':
                print("🔧 Setting up Chrome debugging...")
                run_script("tools/setup_chrome.py")
                
            elif choice == '2':
                print("🚀 Running main scraper...")
                run_script("scraper/main_scraper.py")
                
            elif choice == '3':
                print("📊 Running data analysis...")
                run_script("scraper/analyzer.py")
                
            elif choice == '4':
                print("🧪 Running data validation...")
                run_script("tests/validate_data.py")
                
            elif choice == '5':
                print("🔍 Running browser debugger...")
                run_script("tools/browser_debug.py")
                
            elif choice == '6':
                print("🎯 Running complete workflow...")
                run_script("tools/run_all.py")
                
            elif choice == '7':
                print("📚 Documentation available in docs/ folder:")
                print("   - docs/setup_guide.md - Setup instructions")
                print("   - docs/update_summary.md - Technical details")
                print("   - README.md - Main documentation")
                
            elif choice == '8':
                print("👋 Goodbye!")
                break
                
            else:
                print("❌ Invalid choice. Please enter 1-8.")
                
        except KeyboardInterrupt:
            print("\n👋 Goodbye!")
            break
        except Exception as e:
            print(f"❌ Error: {e}")
        
        print("\n" + "="*40)
        input("Press Enter to continue...")
        print()

if __name__ == "__main__":
    main()
