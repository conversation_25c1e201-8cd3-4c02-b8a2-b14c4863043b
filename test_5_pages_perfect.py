#!/usr/bin/env python3
"""
🧪 Perfect 5-Page Test with JavaScript Loading
Test pagination with proper waiting and comprehensive analysis
"""

import sys
import os
import json
import time
from collections import Counter, defaultdict
sys.path.append(os.path.join(os.path.dirname(__file__), 'scraper'))

from main_scraper import SmartPropertyScraper
from property_schema import PropertySchema

def analyze_data_quality(properties):
    """Comprehensive data quality analysis"""
    print("\n📊 COMPREHENSIVE DATA QUALITY ANALYSIS")
    print("=" * 80)
    
    total_properties = len(properties)
    total_fields = len(PropertySchema.get_schema_fields())
    
    # 1. Overall Statistics
    print(f"\n📈 OVERALL STATISTICS:")
    print(f"   Total properties extracted: {total_properties}")
    print(f"   Total schema fields: {total_fields}")
    
    # 2. Duplicate Analysis
    print(f"\n🔍 DUPLICATE ANALYSIS:")
    seen_properties = {}
    duplicates = []
    
    for i, prop in enumerate(properties):
        key = (prop.get('property_name', ''), prop.get('price', 0))
        if key in seen_properties:
            duplicates.append((i, seen_properties[key], prop.get('property_name', 'Unknown')))
        else:
            seen_properties[key] = i
    
    print(f"   Duplicates found: {len(duplicates)} ({len(duplicates)/total_properties*100:.1f}%)")
    if duplicates:
        print("   Duplicate properties:")
        for dup in duplicates[:5]:
            print(f"     - Property {dup[0]} duplicates {dup[1]}: {dup[2]}")
    
    # 3. Field Completion Analysis
    print(f"\n📋 FIELD COMPLETION ANALYSIS:")
    field_stats = defaultdict(int)
    
    for prop in properties:
        for field, value in prop.items():
            if value not in [None, '', [], 0] or field in ['has_virtual_tour', 'verified_listing', 'featured_listing']:
                field_stats[field] += 1
    
    # Group fields by category
    categories = {
        "🏠 Basic Info": ["property_name", "full_address", "postal_code", "district"],
        "💰 Pricing": ["price", "price_formatted", "price_per_sqft", "price_per_sqft_formatted"],
        "📐 Specifications": ["bedrooms", "bathrooms", "floor_area_sqft", "property_type", "tenure"],
        "📍 Location": ["nearest_mrt", "mrt_distance", "mrt_line", "mrt_station"],
        "👤 Agent Info": ["agent_name", "agent_rating", "agent_company", "agent_description"],
        "📅 Listing Info": ["listed_date", "listed_time_ago", "listing_url"],
        "🖼️ Media": ["main_image_url", "image_count", "has_virtual_tour"],
        "✨ Features": ["verified_listing", "featured_listing", "built_year"]
    }
    
    for category, fields in categories.items():
        print(f"\n   {category}:")
        for field in fields:
            count = field_stats.get(field, 0)
            percentage = count / total_properties * 100
            status = "✅" if percentage > 80 else "⚠️" if percentage > 50 else "❌"
            print(f"     {status} {field}: {count}/{total_properties} ({percentage:.1f}%)")
    
    # 4. Data Quality Scores
    print(f"\n🎯 DATA QUALITY SCORES:")
    quality_scores = []
    for prop in properties:
        filled_fields = sum(1 for key, value in prop.items() 
                          if value not in [None, "", [], 0] or key in ["has_virtual_tour", "verified_listing"])
        quality_score = filled_fields / total_fields
        quality_scores.append(quality_score)
    
    avg_quality = sum(quality_scores) / len(quality_scores)
    min_quality = min(quality_scores)
    max_quality = max(quality_scores)
    
    print(f"   Average quality score: {avg_quality:.1%}")
    print(f"   Quality range: {min_quality:.1%} - {max_quality:.1%}")
    
    # 5. Market Data Analysis
    print(f"\n🏢 MARKET DATA ANALYSIS:")
    
    # Price analysis
    prices = [p.get('price') for p in properties if isinstance(p.get('price'), (int, float))]
    if prices:
        print(f"   Price range: S$ {min(prices):,} - S$ {max(prices):,}")
        print(f"   Average price: S$ {sum(prices) // len(prices):,}")
        print(f"   Median price: S$ {sorted(prices)[len(prices)//2]:,}")
    
    # Property types
    types = [p.get('property_type') for p in properties if p.get('property_type')]
    if types:
        type_counts = Counter(types)
        print(f"   Property types:")
        for prop_type, count in type_counts.most_common():
            print(f"     - {prop_type}: {count} ({count/len(types)*100:.1f}%)")
    
    # MRT coverage
    mrt_stations = [p.get('mrt_station') for p in properties if p.get('mrt_station')]
    if mrt_stations:
        print(f"   MRT coverage: {len(mrt_stations)}/{total_properties} ({len(mrt_stations)/total_properties*100:.1f}%)")
        unique_stations = len(set(mrt_stations))
        print(f"   Unique MRT stations: {unique_stations}")
    
    # Agent analysis
    agents = [p.get('agent_name') for p in properties if p.get('agent_name')]
    if agents:
        agent_counts = Counter(agents)
        print(f"   Unique agents: {len(agent_counts)}")
        if len(agent_counts) > 0:
            top_agent = agent_counts.most_common(1)[0]
            print(f"   Most active agent: {top_agent[0]} ({top_agent[1]} listings)")
    
    # 6. Issues and Recommendations
    print(f"\n⚠️ ISSUES AND RECOMMENDATIONS:")
    issues = []
    
    if len(duplicates) > 0:
        issues.append(f"🔴 {len(duplicates)} duplicate properties found - need better deduplication")
    
    missing_critical = []
    critical_fields = ["property_name", "price", "bedrooms", "property_type"]
    for field in critical_fields:
        count = field_stats.get(field, 0)
        if count < total_properties * 0.9:  # Less than 90%
            missing_critical.append(f"{field} ({count}/{total_properties})")
    
    if missing_critical:
        issues.append(f"🔴 Critical fields missing: {', '.join(missing_critical)}")
    
    if field_stats.get('nearest_mrt', 0) < total_properties * 0.7:
        issues.append(f"🟡 MRT information missing for many properties")
    
    if field_stats.get('agent_rating', 0) < total_properties * 0.3:
        issues.append(f"🟡 Agent ratings rarely available")
    
    if not issues:
        issues.append("✅ No major issues found - data quality is excellent!")
    
    for issue in issues:
        print(f"   {issue}")
    
    return {
        'total_properties': total_properties,
        'duplicates': len(duplicates),
        'avg_quality_score': avg_quality,
        'field_completion': dict(field_stats),
        'issues': issues
    }

def test_5_pages_perfect():
    """Perfect 5-page test with comprehensive analysis"""
    print("🧪 PERFECT 5-PAGE TEST WITH JAVASCRIPT LOADING")
    print("=" * 80)
    
    scraper = SmartPropertyScraper()
    all_properties = []
    
    try:
        # Connect to existing Chrome
        print("🔗 Connecting to Chrome...")
        if not scraper.connect_and_navigate():
            print("❌ Failed to connect")
            return
        
        # Handle Cloudflare with extra patience
        print("🛡️ Handling Cloudflare with extra patience...")
        scraper.handle_cloudflare_and_wait()
        
        # Extra wait for JavaScript to fully load
        print("⏳ Waiting for JavaScript to fully load...")
        time.sleep(10)  # Give JavaScript time to initialize
        
        # Scrape 5 pages with detailed monitoring
        print("\n🔄 Starting 5-page scraping with detailed monitoring...")
        
        for page in range(1, 6):
            print(f"\n📄 SCRAPING PAGE {page}/5")
            print("-" * 40)
            
            # Get current page info
            current_page, _ = scraper.get_current_page_info()
            print(f"📍 Current page number: {current_page}")
            
            # Wait for page to fully load
            print("⏳ Waiting for page content to stabilize...")
            time.sleep(5)  # Let content load
            
            # Extract properties
            print("🔍 Extracting properties...")
            properties = scraper.extract_properties_smart()
            
            if properties:
                print(f"✅ Extracted {len(properties)} properties from page {page}")
                
                # Add page number to each property
                for prop in properties:
                    prop['page_number'] = page
                
                all_properties.extend(properties)
                
                # Show sample from this page
                if properties:
                    sample = properties[0]
                    print(f"📋 Sample: {sample.get('property_name', 'Unknown')} - S$ {sample.get('price', 'N/A'):,}" if isinstance(sample.get('price'), int) else f"📋 Sample: {sample.get('property_name', 'Unknown')} - {sample.get('price_formatted', 'N/A')}")
            else:
                print("❌ No properties extracted from this page")
            
            # Navigate to next page (except for last page)
            if page < 5:
                print(f"🔄 Navigating to page {page + 1}...")
                
                # Find and click next button
                next_button = scraper.find_next_button()
                if next_button:
                    print("✅ Found Next button")
                    
                    # Human-like click
                    success = scraper.click_next_page()
                    if success:
                        print("✅ Clicked Next button successfully")
                        
                        # Wait for navigation with verification
                        print("⏳ Waiting for page navigation...")
                        time.sleep(8)  # Wait for navigation
                        
                        # Verify page changed
                        new_page, _ = scraper.get_current_page_info()
                        if new_page > current_page:
                            print(f"✅ Successfully navigated to page {new_page}")
                        else:
                            print(f"⚠️ Page number didn't change (still {new_page})")
                    else:
                        print("❌ Failed to click Next button")
                        break
                else:
                    print("❌ Next button not found")
                    break
        
        # Comprehensive analysis
        if all_properties:
            print(f"\n🎉 SCRAPING COMPLETE!")
            print(f"📊 Total properties collected: {len(all_properties)} from 5 pages")
            
            # Save data
            timestamp = all_properties[0].get('extraction_timestamp', '').replace(':', '-').replace('.', '-')[:19]
            filename = f"data/perfect_5_pages_{timestamp}.json"
            
            os.makedirs('data', exist_ok=True)
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(all_properties, f, indent=2, ensure_ascii=False)
            
            print(f"💾 Data saved to: {filename}")
            
            # Comprehensive analysis
            analysis_results = analyze_data_quality(all_properties)
            
            # Final summary
            print(f"\n🏆 FINAL SUMMARY:")
            print(f"   ✅ Pages scraped: 5/5")
            print(f"   ✅ Properties collected: {len(all_properties)}")
            print(f"   ✅ Average per page: {len(all_properties)/5:.1f}")
            print(f"   ✅ Data quality score: {analysis_results['avg_quality_score']:.1%}")
            print(f"   ✅ Duplicate rate: {analysis_results['duplicates']/len(all_properties)*100:.1f}%")
            
            if analysis_results['avg_quality_score'] > 0.5 and analysis_results['duplicates'] < len(all_properties) * 0.1:
                print(f"\n🎉 EXCELLENT! Ready for full-scale scraping of 2400+ pages!")
            else:
                print(f"\n⚠️ Needs improvement before full-scale scraping")
        
        else:
            print("❌ No properties extracted from any page")
    
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        print("\n🔧 Keeping Chrome open for inspection...")

if __name__ == "__main__":
    test_5_pages_perfect()
