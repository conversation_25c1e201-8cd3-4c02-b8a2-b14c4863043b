"""
Application configuration settings
"""
import os
from typing import Optional
from pydantic_settings import BaseSettings
from pydantic import Field


class Settings(BaseSettings):
    """Application settings loaded from environment variables"""
    
    # Database Configuration
    database_url: str = Field(default="postgresql://localhost:5432/propertyguru_db")
    redis_url: str = Field(default="redis://localhost:6379/0")
    
    # API Keys
    gemini_api_key: Optional[str] = Field(default=None)
    openai_api_key: Optional[str] = Field(default=None)
    
    # Scraping Configuration
    scraping_delay_min: int = Field(default=1, ge=1)
    scraping_delay_max: int = Field(default=3, ge=1)
    max_concurrent_scrapers: int = Field(default=3, ge=1, le=10)
    user_agent_rotation: bool = Field(default=True)
    
    # Proxy Configuration
    proxy_enabled: bool = Field(default=False)
    proxy_list_url: Optional[str] = Field(default=None)
    proxy_username: Optional[str] = Field(default=None)
    proxy_password: Optional[str] = Field(default=None)
    
    # Security
    secret_key: str = Field(default="dev-secret-key-change-in-production")
    jwt_algorithm: str = Field(default="HS256")
    access_token_expire_minutes: int = Field(default=30)
    
    # Application Settings
    debug: bool = Field(default=True)
    log_level: str = Field(default="INFO")
    api_host: str = Field(default="0.0.0.0")
    api_port: int = Field(default=8000)
    
    # Monitoring
    prometheus_port: int = Field(default=8001)
    enable_metrics: bool = Field(default=True)
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = False


# Global settings instance
settings = Settings()
