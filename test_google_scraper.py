#!/usr/bin/env python3
"""
Test Google-Authenticated PropertyGuru Scraper
Uses your Google account to bypass Cloudflare protection
"""

import sys
import time
import logging
from pathlib import Path

# Add scraper to path
sys.path.append(str(Path(__file__).parent))

from scraper.simple_google_auth import SimpleGoogleAuth

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def test_google_authenticated_scraping():
    """Test PropertyGuru scraping with Google authentication"""
    
    print("🚀 Google-Authenticated PropertyGuru Scraper Test")
    print("=" * 60)
    print("🎯 Target: PropertyGuru Singapore property listings")
    print("🔐 Method: Google account authentication bypass")
    print("📧 Account: <EMAIL>")
    print("=" * 60)
    
    # Target URL (simplified for testing)
    target_url = (
        "https://www.propertyguru.com.sg/property-for-sale?"
        "districtCode=D01&districtCode=D02&districtCode=D09&districtCode=D10&"
        "isCommercial=false"
    )
    
    auth = SimpleGoogleAuth(headless=False)  # Visible browser for testing
    
    try:
        # Step 1: Create driver
        print("\n🔧 Step 1: Creating Chrome WebDriver...")
        driver = auth.create_driver()
        print("✅ WebDriver created successfully")
        
        # Step 2: Google authentication
        print("\n🔐 Step 2: Google Authentication...")
        if auth.login_to_google():
            print("✅ Google authentication successful!")
        else:
            print("❌ Google authentication failed")
            return False
        
        # Step 3: Navigate to PropertyGuru
        print("\n🌐 Step 3: Navigating to PropertyGuru...")
        if auth.navigate_to_propertyguru(target_url):
            print("✅ PropertyGuru navigation successful!")
        else:
            print("❌ PropertyGuru navigation failed")
            return False
        
        # Step 4: Check for blocking
        print("\n🛡️ Step 4: Checking for Cloudflare blocking...")
        if auth.check_for_blocking():
            print("❌ Still being blocked by Cloudflare")
            print("💡 Recommendation: Try different timing or additional measures")
            return False
        else:
            print("🎉 SUCCESS: No blocking detected!")
            print("🚀 Ready for property scraping!")
        
        # Step 5: Quick property extraction test
        print("\n📊 Step 5: Testing property extraction...")
        properties = extract_sample_properties(driver)
        
        if properties:
            print(f"✅ Successfully extracted {len(properties)} properties!")
            print("\n📋 Sample Properties:")
            for i, prop in enumerate(properties[:3], 1):
                print(f"   {i}. {prop.get('title', 'Unknown')[:50]}...")
                print(f"      Price: {prop.get('price', 'Unknown')}")
                print(f"      Location: {prop.get('location', 'Unknown')}")
                print()
        else:
            print("⚠️ No properties extracted - may need refinement")
        
        # Keep browser open for inspection
        print("\n🔍 Browser will remain open for inspection...")
        input("Press Enter to close and finish test...")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Test failed: {e}")
        return False
    finally:
        auth.close()


def extract_sample_properties(driver):
    """Extract sample properties from current page"""
    try:
        properties = []
        
        # Wait a bit for page to load
        time.sleep(3)
        
        # Try different selectors for property cards
        selectors = [
            "[data-testid='listing-card']",
            ".listing-card",
            ".property-card",
            ".search-result",
            ".property-listing"
        ]
        
        property_elements = []
        for selector in selectors:
            try:
                elements = driver.find_elements("css selector", selector)
                if elements:
                    property_elements = elements
                    logger.info(f"Found {len(elements)} properties using selector: {selector}")
                    break
            except Exception:
                continue
        
        if not property_elements:
            logger.warning("No property elements found with any selector")
            return []
        
        # Extract basic info from each property
        for element in property_elements[:10]:  # Limit to first 10
            try:
                prop_data = {}
                
                # Try to extract title
                title_selectors = ["h3", ".title", ".property-title", "a"]
                for sel in title_selectors:
                    try:
                        title_elem = element.find_element("css selector", sel)
                        prop_data["title"] = title_elem.text.strip()
                        if prop_data["title"]:
                            break
                    except:
                        continue
                
                # Try to extract price
                price_selectors = [".price", ".listing-price", ".property-price"]
                for sel in price_selectors:
                    try:
                        price_elem = element.find_element("css selector", sel)
                        prop_data["price"] = price_elem.text.strip()
                        if prop_data["price"]:
                            break
                    except:
                        continue
                
                # Try to extract location
                location_selectors = [".location", ".address", ".property-location"]
                for sel in location_selectors:
                    try:
                        loc_elem = element.find_element("css selector", sel)
                        prop_data["location"] = loc_elem.text.strip()
                        if prop_data["location"]:
                            break
                    except:
                        continue
                
                # Only add if we got some data
                if prop_data.get("title") or prop_data.get("price"):
                    properties.append(prop_data)
                    
            except Exception as e:
                logger.debug(f"Error extracting property: {e}")
                continue
        
        return properties
        
    except Exception as e:
        logger.error(f"Error in property extraction: {e}")
        return []


def main():
    """Main test function"""
    try:
        success = test_google_authenticated_scraping()
        
        if success:
            print("\n🎉 GOOGLE AUTHENTICATION TEST SUCCESSFUL!")
            print("✅ Your Google account can bypass Cloudflare protection")
            print("🚀 Ready to implement full-scale scraping")
        else:
            print("\n❌ GOOGLE AUTHENTICATION TEST FAILED")
            print("💡 May need additional measures or different timing")
        
    except KeyboardInterrupt:
        print("\n⏹️ Test interrupted by user")
    except Exception as e:
        print(f"\n💥 Unexpected error: {e}")


if __name__ == "__main__":
    main()
