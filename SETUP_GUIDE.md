# PropertyGuru Scraper Setup Guide

This guide will help you set up the PropertyGuru scraper with database integration.

## Prerequisites

1. **Python 3.11+** (tested with Python 3.13)
2. **PostgreSQL** (for database)
3. **Redis** (optional, for caching)

## Setup Options

### Option 1: Docker Setup (Recommended)
```bash
# Start all services with Docker
docker-compose up -d

# The API will be available at http://localhost:8000
```

### Option 2: Manual Setup

#### Step 1: Install PostgreSQL

**macOS (with Homebrew):**
```bash
brew install postgresql
brew services start postgresql
```

**Ubuntu/Debian:**
```bash
sudo apt update
sudo apt install postgresql postgresql-contrib
sudo systemctl start postgresql
sudo systemctl enable postgresql
```

**Windows:**
Download and install from https://www.postgresql.org/download/windows/

#### Step 2: Create Database User (Optional)
```bash
# Connect to PostgreSQL
sudo -u postgres psql

# Create user and database
CREATE USER propertyguru WITH PASSWORD 'your_password';
CREATE DATABASE propertyguru_db OWNER propertyguru;
GRANT ALL PRIVILEGES ON DATABASE propertyguru_db TO propertyguru;
\q
```

#### Step 3: Setup Python Environment
```bash
# Clone/navigate to project directory
cd Propertyguru_scraper

# Create virtual environment
python3 -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate

# Install dependencies
pip install -r requirements.txt
```

#### Step 4: Configure Environment
```bash
# Copy environment template
cp .env.example .env

# Edit .env file with your settings
nano .env
```

**Key settings to update in .env:**
```env
# Update with your database credentials
DATABASE_URL=postgresql://propertyguru:your_password@localhost:5432/propertyguru_db

# Add your API keys (optional for basic setup)
GEMINI_API_KEY=your_gemini_key_here
OPENAI_API_KEY=your_openai_key_here
```

#### Step 5: Setup Database
```bash
# Run the database setup script
python scripts/setup_database.py
```

Or manually:
```bash
# Create database tables
python -c "from database.connection import DatabaseManager; DatabaseManager.init_db()"
```

#### Step 6: Start the Application
```bash
# Start the API server
python -m uvicorn api.main:app --reload

# The API will be available at http://localhost:8000
```

## Verification

### 1. Test API Health
```bash
curl http://localhost:8000/health
```

Expected response:
```json
{
  "status": "healthy",
  "database": {
    "status": "healthy",
    "connection": "ok",
    "property_count": 0
  },
  "redis": "not_implemented"
}
```

### 2. Test API Documentation
Visit http://localhost:8000/docs for interactive API documentation.

### 3. Run Tests
```bash
# Run all tests
pytest tests/ -v

# Run specific test files
pytest tests/test_api.py -v
pytest tests/test_database_models.py -v
```

## Database Schema

The database includes these main tables:
- **properties**: Property listings with details
- **agents**: Real estate agent information
- **property_history**: Track property changes over time
- **scraping_jobs**: Monitor scraping job status
- **search_queries**: Analytics for search patterns

## API Endpoints

### Core Endpoints
- `GET /` - Health check
- `GET /health` - Detailed health status
- `GET /docs` - API documentation

### Property Endpoints
- `GET /api/v1/properties/` - Search properties with filters
- `GET /api/v1/properties/{id}` - Get specific property
- `POST /api/v1/properties/` - Create new property
- `PUT /api/v1/properties/{id}` - Update property
- `DELETE /api/v1/properties/{id}` - Delete property
- `GET /api/v1/properties/{id}/history` - Get property change history
- `GET /api/v1/properties/stats/overview` - Property statistics
- `GET /api/v1/properties/recent/listings` - Recent properties

## Troubleshooting

### Database Connection Issues
```bash
# Check if PostgreSQL is running
pg_isready

# Check database exists
psql -l | grep propertyguru_db

# Test connection manually
psql postgresql://localhost:5432/propertyguru_db
```

### Common Errors

**1. "database does not exist"**
```bash
# Create database manually
createdb propertyguru_db
```

**2. "permission denied for database"**
```bash
# Grant permissions
sudo -u postgres psql -c "GRANT ALL PRIVILEGES ON DATABASE propertyguru_db TO $USER;"
```

**3. "connection refused"**
```bash
# Start PostgreSQL
# macOS: brew services start postgresql
# Ubuntu: sudo systemctl start postgresql
```

### Reset Database
```bash
# Drop and recreate database
dropdb propertyguru_db
createdb propertyguru_db
python scripts/setup_database.py
```

## Next Steps

1. **Add Sample Data**: Create some test properties via API
2. **Setup Scraping**: Implement PropertyGuru scraping logic
3. **Add AI Processing**: Integrate Gemini API for natural language queries
4. **Frontend**: Build React frontend for property search
5. **Monitoring**: Set up logging and monitoring

## Development Workflow

```bash
# Start development server
python -m uvicorn api.main:app --reload

# Run tests during development
pytest tests/ -v --watch

# Format code
black .

# Check code quality
flake8 .
```

## Production Deployment

For production deployment, consider:
1. Use environment-specific .env files
2. Set up proper database backups
3. Configure reverse proxy (nginx)
4. Set up monitoring and logging
5. Use Docker for consistent deployment
6. Implement proper security measures
