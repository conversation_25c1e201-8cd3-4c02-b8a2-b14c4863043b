#!/usr/bin/env python3
"""
🏠 Sales-Friendly Property Schema
Human-readable, intuitive schema designed for property agents, buyers, and market analysis
"""

from datetime import datetime
from typing import Dict, Any, Optional

class SalesFriendlyPropertySchema:
    """Sales-oriented property schema with human-friendly field names and organization"""
    
    @staticmethod
    def create_property_record() -> Dict[str, Any]:
        """Create a new property record with sales-friendly structure"""
        return {
            # 🏠 PROPERTY BASICS (What buyers see first)
            "property_title": "",                    # "Stunning 3BR HDB at Commonwealth"
            "address": "",                          # "82 Commonwealth Close"
            "asking_price": "",                     # "S$ 400,000"
            "price_per_sqft": "",                   # "S$ 663 psf"
            "property_type": "",                    # "HDB Flat", "Condo", "Landed"
            "bedrooms": None,                       # 2
            "bathrooms": None,                      # 2
            "size_sqft": None,                      # 603
            "size_description": "",                 # "603 sqft (cozy 2-bedder)"
            
            # 📍 LOCATION & TRANSPORT (Critical for buyers)
            "district": "",                         # "D03 Alexandra/Commonwealth"
            "nearest_mrt": "",                      # "Commonwealth MRT (5 min walk)"
            "mrt_lines": [],                        # ["East West Line"]
            "transport_score": "",                  # "Excellent" / "Good" / "Fair"
            "walkability": "",                      # "5 min walk to MRT"
            
            # 💰 PRICING & VALUE (What agents need)
            "price_analysis": {
                "asking_price_num": None,           # 400000
                "psf_price_num": None,              # 663.35
                "market_segment": "",               # "Affordable", "Mid-range", "Premium", "Luxury"
                "value_proposition": "",            # "Below market rate", "Fair pricing", "Premium location"
                "price_trend": "",                  # "Stable", "Rising", "Declining"
            },
            
            # 🏢 PROPERTY DETAILS (Technical but important)
            "property_info": {
                "age": None,                        # 61 years old
                "age_description": "",              # "Mature estate (61 years)"
                "tenure": "",                       # "99-year Leasehold"
                "remaining_lease": "",              # "38 years remaining"
                "condition": "",                    # "Well-maintained", "Renovated", "Original"
                "floor_level": "",                  # "High floor", "Mid floor", "Low floor"
                "facing": "",                       # "North-facing", "Pool-facing"
                "view": "",                         # "City view", "Garden view"
            },
            
            # 🎯 SELLING POINTS (What makes it special)
            "highlights": [],                       # ["Near MRT", "Renovated", "Corner unit"]
            "lifestyle": {
                "family_friendly": None,            # True/False
                "investment_potential": "",         # "High", "Medium", "Low"
                "rental_yield": "",                 # "4.5% estimated"
                "nearby_amenities": [],             # ["Shopping mall", "Schools", "Parks"]
                "target_buyer": "",                 # "Young couples", "Families", "Investors"
            },
            
            # 👤 AGENT & LISTING INFO (Sales support)
            "listing_details": {
                "agent_name": "",                   # "Ajay Nair"
                "agent_company": "",                # "PropNex Realty"
                "agent_rating": None,               # 4.8
                "agent_speciality": "",             # "HDB specialist", "Luxury condos"
                "listed_date": "",                  # "13 Jul 2025"
                "days_on_market": None,             # 5 days
                "listing_freshness": "",            # "Fresh listing", "Price reduced", "Long on market"
                "urgency": "",                      # "Motivated seller", "Quick sale needed"
            },
            
            # 📸 VISUAL CONTENT (Marketing materials)
            "media": {
                "photo_count": None,                # 15
                "has_virtual_tour": False,          # True/False
                "has_video": False,                 # True/False
                "main_photo": "",                   # URL
                "photo_quality": "",                # "Professional", "Good", "Basic"
                "marketing_appeal": "",             # "High", "Medium", "Low"
            },
            
            # 📊 MARKET INTELLIGENCE (For analysis)
            "market_data": {
                "comparable_properties": None,      # Number of similar properties
                "area_median_price": None,          # S$ 450,000
                "price_vs_market": "",              # "10% below market", "At market rate"
                "demand_level": "",                 # "High demand area", "Moderate demand"
                "supply_level": "",                 # "Limited supply", "Good selection"
                "market_outlook": "",               # "Stable", "Growing", "Cooling"
            },
            
            # 🔍 SEARCH & FILTER TAGS (For easy finding)
            "search_tags": [],                      # ["affordable", "near-mrt", "hdb", "2br", "commonwealth"]
            "buyer_segments": [],                   # ["first-time-buyers", "young-couples", "investors"]
            "investment_tags": [],                  # ["rental-potential", "capital-appreciation", "stable-area"]
            
            # 🎯 SALES INTELLIGENCE (Action items)
            "sales_notes": {
                "competitive_advantages": [],       # ["Lowest price in block", "Recently renovated"]
                "potential_objections": [],         # ["Old building", "High floor", "Facing main road"]
                "selling_strategy": "",             # "Emphasize location", "Highlight renovation"
                "target_timeline": "",              # "Quick sale possible", "Normal timeline"
                "negotiation_room": "",             # "Firm price", "Some flexibility", "Motivated seller"
            },
            
            # 📋 TECHNICAL DATA (For records)
            "technical": {
                "listing_id": "",                   # "property_0"
                "source_url": "",                   # PropertyGuru URL
                "last_updated": "",                 # Timestamp
                "data_quality": "",                 # "Complete", "Partial", "Basic"
                "extraction_method": "",            # "advanced_element"
                "verification_status": "",          # "Verified", "Unverified"
            }
        }
    
    @staticmethod
    def convert_technical_to_sales_friendly(technical_data: Dict[str, Any]) -> Dict[str, Any]:
        """Convert technical schema to sales-friendly format"""
        sales_record = SalesFriendlyPropertySchema.create_property_record()
        
        try:
            # 🏠 PROPERTY BASICS
            sales_record["property_title"] = technical_data.get("property_name", "")
            sales_record["address"] = technical_data.get("full_address", "")
            sales_record["asking_price"] = technical_data.get("price_formatted", "")
            sales_record["price_per_sqft"] = technical_data.get("price_per_sqft_formatted", "")
            sales_record["property_type"] = technical_data.get("property_type", "")
            sales_record["bedrooms"] = technical_data.get("bedrooms")
            sales_record["bathrooms"] = technical_data.get("bathrooms")
            sales_record["size_sqft"] = technical_data.get("floor_area_sqft")
            
            # Create human-friendly size description
            if sales_record["size_sqft"] and sales_record["bedrooms"]:
                size_desc = f"{sales_record['size_sqft']} sqft"
                if sales_record["bedrooms"] <= 2:
                    size_desc += " (cozy)"
                elif sales_record["bedrooms"] == 3:
                    size_desc += " (spacious)"
                elif sales_record["bedrooms"] >= 4:
                    size_desc += " (large family home)"
                sales_record["size_description"] = size_desc
            
            # 📍 LOCATION & TRANSPORT
            mrt_info = technical_data.get("mrt_distance", "")
            mrt_station = technical_data.get("mrt_station", "")
            if mrt_station and mrt_info:
                sales_record["nearest_mrt"] = f"{mrt_station} ({mrt_info})"
                # Determine transport score
                if "min" in mrt_info:
                    try:
                        minutes = int(mrt_info.split()[0])
                        if minutes <= 5:
                            sales_record["transport_score"] = "Excellent"
                            sales_record["walkability"] = f"{minutes} min walk to MRT (excellent)"
                        elif minutes <= 10:
                            sales_record["transport_score"] = "Good"
                            sales_record["walkability"] = f"{minutes} min walk to MRT (convenient)"
                        else:
                            sales_record["transport_score"] = "Fair"
                            sales_record["walkability"] = f"{minutes} min walk to MRT"
                    except:
                        sales_record["transport_score"] = "Good"
            
            # 💰 PRICING & VALUE
            price_num = technical_data.get("price")
            psf_num = technical_data.get("price_per_sqft")
            
            sales_record["price_analysis"]["asking_price_num"] = price_num
            sales_record["price_analysis"]["psf_price_num"] = psf_num
            
            # Determine market segment based on price
            if price_num:
                if price_num < 500000:
                    sales_record["price_analysis"]["market_segment"] = "Affordable"
                elif price_num < 1000000:
                    sales_record["price_analysis"]["market_segment"] = "Mid-range"
                elif price_num < 3000000:
                    sales_record["price_analysis"]["market_segment"] = "Premium"
                else:
                    sales_record["price_analysis"]["market_segment"] = "Luxury"
            
            # 🏢 PROPERTY DETAILS
            built_year = technical_data.get("built_year")
            if built_year:
                current_year = datetime.now().year
                age = current_year - built_year
                sales_record["property_info"]["age"] = age
                
                if age < 5:
                    sales_record["property_info"]["age_description"] = f"New development ({age} years)"
                elif age < 15:
                    sales_record["property_info"]["age_description"] = f"Modern building ({age} years)"
                elif age < 30:
                    sales_record["property_info"]["age_description"] = f"Established estate ({age} years)"
                else:
                    sales_record["property_info"]["age_description"] = f"Mature estate ({age} years)"
            
            sales_record["property_info"]["tenure"] = technical_data.get("tenure", "")
            
            # 🎯 SELLING POINTS
            highlights = []
            if sales_record["transport_score"] == "Excellent":
                highlights.append("Near MRT")
            if technical_data.get("built_year", 0) > 2010:
                highlights.append("Modern building")
            if technical_data.get("has_virtual_tour"):
                highlights.append("Virtual tour available")
            
            sales_record["highlights"] = highlights
            
            # Determine target buyer
            bedrooms = sales_record["bedrooms"]
            price_segment = sales_record["price_analysis"]["market_segment"]
            
            if bedrooms <= 2 and price_segment in ["Affordable", "Mid-range"]:
                sales_record["lifestyle"]["target_buyer"] = "Young couples, First-time buyers"
            elif bedrooms >= 3 and price_segment in ["Affordable", "Mid-range"]:
                sales_record["lifestyle"]["target_buyer"] = "Families, Upgraders"
            elif price_segment in ["Premium", "Luxury"]:
                sales_record["lifestyle"]["target_buyer"] = "Affluent buyers, Investors"
            
            # 👤 AGENT & LISTING INFO
            sales_record["listing_details"]["agent_name"] = technical_data.get("agent_name", "")
            sales_record["listing_details"]["listed_date"] = technical_data.get("listed_date", "")
            
            # Determine listing freshness
            time_ago = technical_data.get("listed_time_ago", "")
            if "min" in time_ago or "hour" in time_ago:
                sales_record["listing_details"]["listing_freshness"] = "Fresh listing"
            elif "day" in time_ago:
                sales_record["listing_details"]["listing_freshness"] = "Recent listing"
            else:
                sales_record["listing_details"]["listing_freshness"] = "Established listing"
            
            # 📸 VISUAL CONTENT
            sales_record["media"]["photo_count"] = technical_data.get("image_count")
            sales_record["media"]["has_virtual_tour"] = technical_data.get("has_virtual_tour", False)
            sales_record["media"]["has_video"] = technical_data.get("has_video", False)
            sales_record["media"]["main_photo"] = technical_data.get("main_image_url", "")
            
            # Determine photo quality
            photo_count = sales_record["media"]["photo_count"]
            if photo_count and photo_count >= 15:
                sales_record["media"]["photo_quality"] = "Professional"
                sales_record["media"]["marketing_appeal"] = "High"
            elif photo_count and photo_count >= 8:
                sales_record["media"]["photo_quality"] = "Good"
                sales_record["media"]["marketing_appeal"] = "Medium"
            else:
                sales_record["media"]["photo_quality"] = "Basic"
                sales_record["media"]["marketing_appeal"] = "Low"
            
            # 🔍 SEARCH TAGS
            tags = []
            if price_segment:
                tags.append(price_segment.lower())
            if sales_record["property_type"]:
                tags.append(sales_record["property_type"].lower().replace(" ", "-"))
            if bedrooms:
                tags.append(f"{bedrooms}br")
            if sales_record["transport_score"] == "Excellent":
                tags.append("near-mrt")
            
            sales_record["search_tags"] = tags
            
            # 📋 TECHNICAL DATA
            sales_record["technical"]["listing_id"] = technical_data.get("id", "")
            sales_record["technical"]["source_url"] = technical_data.get("listing_url", "")
            sales_record["technical"]["last_updated"] = technical_data.get("extraction_timestamp", "")
            sales_record["technical"]["extraction_method"] = technical_data.get("extraction_method", "")
            
            return sales_record
            
        except Exception as e:
            print(f"⚠️ Error converting to sales-friendly format: {e}")
            return sales_record
    
    @staticmethod
    def get_sales_summary(property_data: Dict[str, Any]) -> str:
        """Generate a human-readable sales summary"""
        try:
            title = property_data.get("property_title", "Property")
            price = property_data.get("asking_price", "Price on request")
            bedrooms = property_data.get("bedrooms", "")
            size = property_data.get("size_description", "")
            location = property_data.get("nearest_mrt", "")
            target = property_data.get("lifestyle", {}).get("target_buyer", "")
            
            summary = f"🏠 {title}\n"
            summary += f"💰 {price}"
            if bedrooms:
                summary += f" • {bedrooms}BR"
            if size:
                summary += f" • {size}"
            summary += f"\n📍 {location}"
            if target:
                summary += f"\n🎯 Perfect for: {target}"
            
            return summary
            
        except Exception as e:
            return f"Property summary unavailable: {e}"
