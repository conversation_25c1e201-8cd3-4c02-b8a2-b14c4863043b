#!/usr/bin/env python3
"""
🧠 Intelligent Sales Schema with Complete Reasoning
Every field is either filled with intelligent data or removed entirely
"""

import re
from datetime import datetime
from typing import Dict, Any, Optional

class IntelligentSalesSchema:
    """Intelligent property schema that fills every field with reasoned data"""
    
    # Singapore property market intelligence
    DISTRICT_INFO = {
        "D01": {"name": "Boat Quay/Raffles Place", "tier": "Prime", "investment": "Excellent"},
        "D02": {"name": "Chinatown/Tanjong Pagar", "tier": "Prime", "investment": "Excellent"},
        "D03": {"name": "Alexandra/Commonwealth", "tier": "Good", "investment": "Good"},
        "D04": {"name": "Harbourfront/Telok Blangah", "tier": "Good", "investment": "Good"},
        "D05": {"name": "Buona Vista/West Coast", "tier": "Prime", "investment": "Excellent"},
        "D09": {"name": "Orchard/River Valley", "tier": "Prime", "investment": "Excellent"},
        "D10": {"name": "Tanglin/Holland/Bukit Timah", "tier": "Prime", "investment": "Excellent"},
        "D11": {"name": "Newton/Novena", "tier": "Prime", "investment": "Excellent"},
        "D15": {"name": "East Coast/Marine Parade", "tier": "Good", "investment": "Good"},
        "D19": {"name": "Hougang/Punggol/Sengkang", "tier": "Emerging", "investment": "Good"},
        "D20": {"name": "Ang Mo Kio/Bishan", "tier": "Good", "investment": "Good"},
    }
    
    MRT_LINES = {
        "EW": "East West Line", "NS": "North South Line", "NE": "North East Line",
        "CC": "Circle Line", "DT": "Downtown Line", "TE": "Thomson-East Coast Line",
        "BP": "Bukit Panjang LRT", "SK": "Sengkang LRT", "PG": "Punggol LRT"
    }
    
    @staticmethod
    def create_intelligent_property(technical_data: Dict[str, Any]) -> Dict[str, Any]:
        """Create intelligent property record with complete reasoning"""
        
        # Extract basic data
        name = technical_data.get("property_name", "").strip()
        price_num = technical_data.get("price")
        price_formatted = technical_data.get("price_formatted", "")
        psf_num = technical_data.get("price_per_sqft")
        psf_formatted = technical_data.get("price_per_sqft_formatted", "")
        bedrooms = technical_data.get("bedrooms")
        bathrooms = technical_data.get("bathrooms")
        size_sqft = technical_data.get("floor_area_sqft")
        property_type = technical_data.get("property_type", "")
        tenure = technical_data.get("tenure", "")
        built_year = technical_data.get("built_year")
        mrt_station = technical_data.get("mrt_station", "")
        mrt_distance = technical_data.get("mrt_distance", "")
        mrt_line = technical_data.get("mrt_line", "")
        agent_name = technical_data.get("agent_name", "")
        listed_date = technical_data.get("listed_date", "")
        image_count = technical_data.get("image_count", 0)
        main_image = technical_data.get("main_image_url", "")
        
        # Skip properties with insufficient data
        if not name or not price_num or not bedrooms:
            return None
        
        # Start building intelligent record
        property_record = {}
        
        # 🏠 PROPERTY ESSENTIALS (Always filled)
        property_record["title"] = name
        property_record["price"] = price_formatted if price_formatted else f"S$ {price_num:,}"
        property_record["price_psf"] = psf_formatted if psf_formatted else (f"S$ {psf_num:,.0f} psf" if psf_num else "")
        property_record["bedrooms"] = bedrooms
        property_record["bathrooms"] = bathrooms if bathrooms else bedrooms  # Estimate if missing
        property_record["size"] = f"{size_sqft:,} sqft" if size_sqft else ""
        property_record["type"] = property_type
        
        # 📍 LOCATION INTELLIGENCE
        location_data = IntelligentSalesSchema._analyze_location(name, mrt_station, mrt_distance, mrt_line)
        if location_data:
            property_record.update(location_data)
        
        # 💰 PRICING INTELLIGENCE
        pricing_data = IntelligentSalesSchema._analyze_pricing(price_num, psf_num, property_type, bedrooms, size_sqft)
        if pricing_data:
            property_record.update(pricing_data)
        
        # 🏢 PROPERTY INTELLIGENCE
        property_data = IntelligentSalesSchema._analyze_property(property_type, built_year, tenure, bedrooms, size_sqft)
        if property_data:
            property_record.update(property_data)
        
        # 🎯 BUYER INTELLIGENCE
        buyer_data = IntelligentSalesSchema._analyze_target_buyers(price_num, bedrooms, property_type, location_data)
        if buyer_data:
            property_record.update(buyer_data)
        
        # 📈 INVESTMENT INTELLIGENCE
        investment_data = IntelligentSalesSchema._analyze_investment(price_num, psf_num, property_type, location_data, built_year)
        if investment_data:
            property_record.update(investment_data)
        
        # 👤 LISTING INTELLIGENCE
        listing_data = IntelligentSalesSchema._analyze_listing(agent_name, listed_date, image_count, main_image)
        if listing_data:
            property_record.update(listing_data)
        
        # 🎪 MARKETING INTELLIGENCE
        marketing_data = IntelligentSalesSchema._analyze_marketing(property_record, image_count, main_image)
        if marketing_data:
            property_record.update(marketing_data)
        
        return property_record
    
    @staticmethod
    def _analyze_location(name: str, mrt_station: str, mrt_distance: str, mrt_line: str) -> Dict[str, Any]:
        """Intelligent location analysis"""
        location = {}
        
        # Extract district from name or infer from MRT
        district = IntelligentSalesSchema._extract_district(name, mrt_station)
        if district:
            district_info = IntelligentSalesSchema.DISTRICT_INFO.get(district, {})
            location["district"] = f"{district} {district_info.get('name', '')}"
            location["area_tier"] = district_info.get('tier', 'Good')
        
        # MRT accessibility analysis
        if mrt_station and mrt_distance:
            # Extract walking time
            walk_time = None
            if "min" in mrt_distance:
                try:
                    walk_time = int(re.search(r'(\d+)', mrt_distance).group(1))
                except:
                    pass
            
            # Determine MRT line
            line_name = ""
            if mrt_line:
                line_code = mrt_line[:2]
                line_name = IntelligentSalesSchema.MRT_LINES.get(line_code, "MRT")
            
            # Simple transport category (no analysis)
            if walk_time:
                if walk_time <= 5:
                    transport_category = "0-5 min"
                elif walk_time <= 10:
                    transport_category = "6-10 min"
                elif walk_time <= 15:
                    transport_category = "11-15 min"
                else:
                    transport_category = "Above 15 min"

                location["mrt_distance_category"] = transport_category
                location["mrt_station"] = mrt_station
                location["mrt_walk_time"] = f"{walk_time} min"
                
                if line_name:
                    location["mrt_line"] = line_name
        
        return location if location else None
    
    @staticmethod
    def _analyze_pricing(price_num: int, psf_num: float, property_type: str, bedrooms: int, size_sqft: int) -> Dict[str, Any]:
        """Intelligent pricing analysis"""
        pricing = {}
        
        # Simple price category (no analysis)
        if price_num < 600000:
            segment = "Under 600K"
        elif price_num < 1200000:
            segment = "600K-1.2M"
        elif price_num < 2500000:
            segment = "1.2M-2.5M"
        elif price_num < 5000000:
            segment = "2.5M-5M"
        else:
            segment = "Above 5M"

        pricing["price_category"] = segment
        
        # Simple PSF category (no analysis)
        if psf_num:
            if psf_num < 800:
                psf_category = "Under 800 PSF"
            elif psf_num < 1200:
                psf_category = "800-1200 PSF"
            elif psf_num < 1800:
                psf_category = "1200-1800 PSF"
            else:
                psf_category = "Above 1800 PSF"

            pricing["psf_category"] = psf_category
        
        return pricing
    
    @staticmethod
    def _analyze_property(property_type: str, built_year: int, tenure: str, bedrooms: int, size_sqft: int) -> Dict[str, Any]:
        """Intelligent property analysis"""
        property_info = {}
        
        # Age and condition analysis
        if built_year:
            current_year = datetime.now().year
            age = current_year - built_year
            
            if age < 5:
                age_desc = f"Brand new ({age} years) - modern fittings"
                condition = "Excellent"
            elif age < 15:
                age_desc = f"Modern building ({age} years) - contemporary design"
                condition = "Very Good"
            elif age < 30:
                age_desc = f"Established development ({age} years) - proven track record"
                condition = "Good"
            else:
                age_desc = f"Mature estate ({age} years) - character building"
                condition = "Well-maintained"
            
            property_info["age_description"] = age_desc
            property_info["condition_assessment"] = condition
            
            # Remaining lease for leasehold
            if tenure and "99-year" in tenure:
                remaining = 99 - age
                if remaining > 80:
                    lease_desc = f"Excellent lease remaining ({remaining} years)"
                elif remaining > 60:
                    lease_desc = f"Good lease remaining ({remaining} years)"
                elif remaining > 40:
                    lease_desc = f"Moderate lease remaining ({remaining} years)"
                else:
                    lease_desc = f"Consider lease remaining ({remaining} years)"
                
                property_info["lease_status"] = lease_desc
        
        # Space efficiency analysis
        if size_sqft and bedrooms:
            sqft_per_bedroom = size_sqft / bedrooms
            
            if sqft_per_bedroom > 400:
                space_desc = "Spacious layout - generous room sizes"
            elif sqft_per_bedroom > 300:
                space_desc = "Well-proportioned - comfortable living"
            else:
                space_desc = "Efficient layout - cozy living"
            
            property_info["space_efficiency"] = space_desc
        
        # Property type advantages
        if property_type == "HDB Flat":
            property_info["ownership_benefits"] = "No ABSD, lower maintenance, established community"
        elif property_type == "Condominium":
            property_info["ownership_benefits"] = "Facilities, security, lifestyle amenities"
        elif "House" in property_type:
            property_info["ownership_benefits"] = "Privacy, land ownership, expansion potential"
        
        return property_info if property_info else None
    
    @staticmethod
    def _analyze_target_buyers(price_num: int, bedrooms: int, property_type: str, location_data: Dict) -> Dict[str, Any]:
        """Intelligent target buyer analysis"""
        buyer_info = {}
        
        # Primary target based on price and bedrooms
        if bedrooms <= 2 and price_num < 800000:
            primary_target = "Young professionals, first-time buyers"
            lifestyle_fit = "Urban starter home"
        elif bedrooms == 3 and price_num < 1500000:
            primary_target = "Growing families, upgraders"
            lifestyle_fit = "Family-oriented living"
        elif bedrooms >= 4 or price_num > 2000000:
            primary_target = "Established families, affluent buyers"
            lifestyle_fit = "Premium family living"
        else:
            primary_target = "Investors, downsizers"
            lifestyle_fit = "Investment opportunity"
        
        buyer_info["target_buyers"] = primary_target
        buyer_info["lifestyle_appeal"] = lifestyle_fit
        
        # Investment buyer appeal
        if property_type == "HDB Flat":
            investor_appeal = "High rental demand, stable returns"
        elif property_type == "Condominium" and price_num < 2000000:
            investor_appeal = "Good rental yield, capital appreciation"
        else:
            investor_appeal = "Capital appreciation, prestige investment"
        
        buyer_info["investor_appeal"] = investor_appeal
        
        # Urgency factors
        transport_grade = location_data.get("transport_grade", "") if location_data else ""
        if transport_grade == "Excellent":
            urgency = "High demand location - act fast"
        elif price_num < 600000:
            urgency = "Affordable pricing - good entry opportunity"
        else:
            urgency = "Stable market - good time to buy"
        
        buyer_info["market_urgency"] = urgency
        
        return buyer_info
    
    @staticmethod
    def _analyze_investment(price_num: int, psf_num: float, property_type: str, location_data: Dict, built_year: int) -> Dict[str, Any]:
        """Intelligent investment analysis"""
        investment = {}
        
        # Rental yield estimation
        if property_type == "HDB Flat":
            estimated_rent = price_num * 0.04 / 12  # 4% yield
            yield_desc = "4-5% rental yield expected"
        elif property_type == "Condominium":
            estimated_rent = price_num * 0.035 / 12  # 3.5% yield
            yield_desc = "3-4% rental yield expected"
        else:
            estimated_rent = price_num * 0.03 / 12  # 3% yield
            yield_desc = "3-3.5% rental yield expected"
        
        investment["rental_estimate"] = f"S$ {estimated_rent:,.0f}/month"
        investment["yield_expectation"] = yield_desc
        
        # Capital appreciation potential
        area_tier = location_data.get("area_tier", "") if location_data else ""
        if area_tier == "Prime":
            appreciation = "Strong capital appreciation potential"
        elif area_tier == "Good":
            appreciation = "Steady capital appreciation expected"
        else:
            appreciation = "Moderate capital appreciation potential"
        
        investment["appreciation_outlook"] = appreciation
        
        # Investment timeline recommendation
        if built_year and datetime.now().year - built_year < 10:
            timeline = "Medium to long-term hold (5-10 years)"
        elif price_num < 1000000:
            timeline = "Flexible hold period (3-7 years)"
        else:
            timeline = "Long-term investment (7+ years)"
        
        investment["investment_timeline"] = timeline
        
        return investment
    
    @staticmethod
    def _analyze_listing(agent_name: str, listed_date: str, image_count: int, main_image: str) -> Dict[str, Any]:
        """Intelligent listing analysis"""
        listing = {}
        
        if agent_name:
            listing["agent"] = agent_name
            
            # Agent specialization inference
            if any(word in agent_name.lower() for word in ["lim", "tan", "lee", "wong"]):
                specialization = "Local market specialist"
            else:
                specialization = "International market specialist"
            
            listing["agent_expertise"] = specialization
        
        # Listing freshness and urgency
        if listed_date:
            listing["listing_date"] = listed_date
            
            # Infer urgency from listing patterns
            if "jul" in listed_date.lower() and "2025" in listed_date:
                listing["listing_status"] = "Fresh listing - immediate viewing available"
            else:
                listing["listing_status"] = "Established listing - proven market interest"
        
        # Photo quality assessment
        if image_count:
            if image_count >= 20:
                photo_quality = "Professional photography - premium presentation"
            elif image_count >= 10:
                photo_quality = "Good photo coverage - well documented"
            else:
                photo_quality = "Basic photo coverage - viewing recommended"
            
            listing["photo_assessment"] = photo_quality
        
        return listing if listing else None
    
    @staticmethod
    def _analyze_marketing(property_record: Dict, image_count: int, main_image: str) -> Dict[str, Any]:
        """Intelligent marketing analysis"""
        marketing = {}
        
        # Key selling points
        selling_points = []
        
        if property_record.get("transport_grade") == "Excellent":
            selling_points.append("Prime transport connectivity")
        
        if "Premium" in property_record.get("market_segment", ""):
            selling_points.append("Premium market positioning")
        
        if "new" in property_record.get("age_description", "").lower():
            selling_points.append("Modern development")
        
        if property_record.get("area_tier") == "Prime":
            selling_points.append("Prime district location")
        
        if selling_points:
            marketing["key_selling_points"] = selling_points
        
        # Marketing strategy
        target = property_record.get("target_buyers", "")
        if "first-time" in target.lower():
            strategy = "Emphasize affordability and growth potential"
        elif "family" in target.lower():
            strategy = "Highlight space, amenities, and schools nearby"
        elif "affluent" in target.lower():
            strategy = "Focus on prestige, exclusivity, and investment value"
        else:
            strategy = "Balanced approach highlighting value and location"
        
        marketing["marketing_strategy"] = strategy
        
        # Competitive advantages
        advantages = []
        if property_record.get("psf_analysis") == "Below market":
            advantages.append("Competitive pricing vs comparable properties")
        if property_record.get("transport_grade") in ["Excellent", "Very Good"]:
            advantages.append("Superior transport connectivity")
        if "High" in property_record.get("investor_appeal", ""):
            advantages.append("Strong rental demand area")
        
        if advantages:
            marketing["competitive_advantages"] = advantages
        
        return marketing if marketing else None
    
    @staticmethod
    def _extract_district(name: str, mrt_station: str) -> str:
        """Extract district code from property name or MRT station"""
        # Common area to district mapping
        area_districts = {
            "commonwealth": "D03", "alexandra": "D03", "toa payoh": "D12",
            "choa chu kang": "D23", "hougang": "D19", "punggol": "D19",
            "sengkang": "D19", "bishan": "D20", "ang mo kio": "D20",
            "orchard": "D09", "newton": "D11", "novena": "D11",
            "marina": "D01", "raffles": "D01", "chinatown": "D02",
            "tanjong pagar": "D02", "harbourfront": "D04", "telok blangah": "D04",
            "buona vista": "D05", "west coast": "D05", "clementi": "D05",
            "tanglin": "D10", "holland": "D10", "bukit timah": "D10",
            "east coast": "D15", "marine parade": "D15", "bedok": "D16"
        }
        
        text_to_search = f"{name} {mrt_station}".lower()
        
        for area, district in area_districts.items():
            if area in text_to_search:
                return district
        
        return None
