#!/usr/bin/env python3
"""
🚀 Full-Scale Intelligent Property Scraper
Complete PropertyGuru scraping with intelligent sales schema
"""

import sys
import os
import json
import time
from datetime import datetime

# Add src directory to path for imports
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from scrapers.main_scraper import SmartPropertyScraper
from schemas.intelligent_sales_schema import IntelligentSalesSchema

class FullScaleIntelligentScraper:
    """Full-scale scraper with intelligent sales schema"""
    
    def __init__(self):
        self.scraper = SmartPropertyScraper()
        self.all_properties = []
        self.stats = {
            'pages_scraped': 0,
            'properties_extracted': 0,
            'properties_converted': 0,
            'start_time': None,
            'errors': 0
        }
    
    def start_full_scale_scraping(self, max_pages: int = 2600, start_page: int = 1):
        """Start full-scale intelligent scraping"""
        
        print("🚀 FULL-SCALE INTELLIGENT PROPERTY SCRAPING")
        print("=" * 80)
        print(f"🎯 Target: {max_pages} pages starting from page {start_page}")
        print(f"📊 Expected properties: ~{max_pages * 16} with intelligent sales data")
        print(f"🧠 Schema: Intelligent sales format (100% field completion)")
        
        self.stats['start_time'] = datetime.now()
        
        try:
            # Connect to Chrome
            print("\n🔗 Connecting to Chrome...")
            if not self.scraper.connect_and_navigate():
                print("❌ Failed to connect to Chrome")
                return False
            
            # Handle Cloudflare
            print("🛡️ Handling Cloudflare protection...")
            self.scraper.handle_cloudflare_and_wait()
            
            # Navigate to start page if not page 1
            if start_page > 1:
                print(f"📄 Navigating to start page {start_page}...")
                self._navigate_to_page(start_page)
            
            # Start scraping loop
            print(f"\n🔄 Starting intelligent scraping from page {start_page}...")
            
            current_page = start_page
            consecutive_failures = 0
            
            while current_page <= max_pages and consecutive_failures < 5:
                try:
                    success = self._scrape_page_intelligently(current_page)
                    
                    if success:
                        consecutive_failures = 0
                        self.stats['pages_scraped'] += 1
                        
                        # Progress reporting
                        if self.stats['pages_scraped'] % 10 == 0:
                            self._report_progress()
                        
                        # Save checkpoint every 50 pages
                        if self.stats['pages_scraped'] % 50 == 0:
                            self._save_checkpoint()
                        
                        # Navigate to next page
                        if current_page < max_pages:
                            if not self._navigate_to_next_page():
                                print("❌ Failed to navigate to next page")
                                break
                        
                        current_page += 1
                        
                    else:
                        consecutive_failures += 1
                        print(f"⚠️ Page {current_page} failed (attempt {consecutive_failures}/5)")
                        
                        if consecutive_failures < 5:
                            print("🔄 Retrying in 10 seconds...")
                            time.sleep(10)
                        else:
                            print("❌ Too many consecutive failures, stopping")
                            break
                
                except KeyboardInterrupt:
                    print("\n⏹️ Scraping interrupted by user")
                    break
                except Exception as e:
                    print(f"❌ Error on page {current_page}: {e}")
                    consecutive_failures += 1
                    self.stats['errors'] += 1
            
            # Final save and report
            self._save_final_results()
            self._final_report()
            
            return True
            
        except Exception as e:
            print(f"❌ Full-scale scraping failed: {e}")
            return False
    
    def _scrape_page_intelligently(self, page_number: int) -> bool:
        """Scrape a single page with intelligent conversion"""
        
        print(f"\n📄 SCRAPING PAGE {page_number}")
        print("-" * 40)
        
        try:
            # Wait for page to load
            time.sleep(3)
            
            # Extract properties using advanced method
            print("🔍 Extracting properties...")
            raw_properties = self.scraper.extract_properties_smart()
            
            if not raw_properties:
                print("❌ No properties extracted")
                return False
            
            print(f"✅ Extracted {len(raw_properties)} raw properties")
            self.stats['properties_extracted'] += len(raw_properties)
            
            # Convert to intelligent format
            print("🧠 Converting to intelligent sales format...")
            intelligent_properties = []
            
            for raw_prop in raw_properties:
                try:
                    # Add page number to raw data
                    raw_prop['page_number'] = page_number
                    
                    # Convert to intelligent format
                    intelligent_prop = IntelligentSalesSchema.create_intelligent_property(raw_prop)
                    
                    if intelligent_prop:
                        # Add metadata
                        intelligent_prop['page_scraped'] = page_number
                        intelligent_prop['scraped_at'] = datetime.now().isoformat()
                        
                        intelligent_properties.append(intelligent_prop)
                        self.stats['properties_converted'] += 1
                
                except Exception as e:
                    print(f"⚠️ Error converting property: {e}")
                    continue
            
            print(f"✅ Converted {len(intelligent_properties)} to intelligent format")
            
            # Add to collection
            self.all_properties.extend(intelligent_properties)
            
            # Show sample
            if intelligent_properties:
                sample = intelligent_properties[0]
                print(f"📋 Sample: {sample['title']} - {sample['price']} ({sample['market_segment']})")
            
            return True
            
        except Exception as e:
            print(f"❌ Error scraping page {page_number}: {e}")
            return False
    
    def _navigate_to_page(self, page_number: int):
        """Navigate to specific page"""
        try:
            current_url = self.scraper.driver.current_url
            if page_number == 1:
                # Remove page number for page 1
                target_url = current_url.split('/property-for-sale/')[0] + '/property-for-sale?' + current_url.split('?')[1]
            else:
                # Add/replace page number
                if '/property-for-sale/' in current_url and '?' in current_url:
                    base_url = current_url.split('/property-for-sale/')[0]
                    params = current_url.split('?')[1]
                    target_url = f"{base_url}/property-for-sale/{page_number}?{params}"
                else:
                    target_url = current_url.replace('/property-for-sale?', f'/property-for-sale/{page_number}?')
            
            self.scraper.driver.get(target_url)
            time.sleep(5)  # Wait for page load
            
        except Exception as e:
            print(f"❌ Error navigating to page {page_number}: {e}")
    
    def _navigate_to_next_page(self) -> bool:
        """Navigate to next page"""
        try:
            return self.scraper.click_next_page()
        except Exception as e:
            print(f"❌ Error navigating to next page: {e}")
            return False
    
    def _report_progress(self):
        """Report scraping progress"""
        elapsed = datetime.now() - self.stats['start_time']
        pages_per_hour = self.stats['pages_scraped'] / (elapsed.total_seconds() / 3600)
        properties_per_page = self.stats['properties_converted'] / self.stats['pages_scraped'] if self.stats['pages_scraped'] > 0 else 0
        
        print(f"\n📊 PROGRESS REPORT:")
        print(f"   Pages scraped: {self.stats['pages_scraped']}")
        print(f"   Properties converted: {self.stats['properties_converted']}")
        print(f"   Average per page: {properties_per_page:.1f}")
        print(f"   Speed: {pages_per_hour:.1f} pages/hour")
        print(f"   Elapsed time: {elapsed}")
        print(f"   Errors: {self.stats['errors']}")
    
    def _save_checkpoint(self):
        """Save checkpoint data"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            checkpoint_file = f"data/checkpoint_intelligent_{timestamp}.json"
            
            checkpoint_data = {
                'metadata': self.stats.copy(),
                'properties': self.all_properties
            }
            
            with open(checkpoint_file, 'w', encoding='utf-8') as f:
                json.dump(checkpoint_data, f, indent=2, ensure_ascii=False)
            
            print(f"💾 Checkpoint saved: {checkpoint_file}")
            
        except Exception as e:
            print(f"⚠️ Error saving checkpoint: {e}")
    
    def _save_final_results(self):
        """Save final results"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            final_file = f"data/intelligent_full_scale_{timestamp}.json"
            
            # Prepare final data
            final_data = {
                'metadata': {
                    'total_pages_scraped': self.stats['pages_scraped'],
                    'total_properties': len(self.all_properties),
                    'scraping_start': self.stats['start_time'].isoformat(),
                    'scraping_end': datetime.now().isoformat(),
                    'total_errors': self.stats['errors'],
                    'schema_version': 'intelligent_sales_v1.0'
                },
                'properties': self.all_properties
            }
            
            with open(final_file, 'w', encoding='utf-8') as f:
                json.dump(final_data, f, indent=2, ensure_ascii=False)
            
            print(f"💾 Final results saved: {final_file}")
            return final_file
            
        except Exception as e:
            print(f"❌ Error saving final results: {e}")
            return None
    
    def _final_report(self):
        """Generate final scraping report"""
        elapsed = datetime.now() - self.stats['start_time']
        
        print(f"\n🎉 FULL-SCALE INTELLIGENT SCRAPING COMPLETE!")
        print("=" * 60)
        print(f"📊 FINAL STATISTICS:")
        print(f"   Pages scraped: {self.stats['pages_scraped']}")
        print(f"   Properties extracted: {self.stats['properties_extracted']}")
        print(f"   Properties converted: {self.stats['properties_converted']}")
        print(f"   Conversion rate: {self.stats['properties_converted']/self.stats['properties_extracted']*100:.1f}%")
        print(f"   Average per page: {self.stats['properties_converted']/self.stats['pages_scraped']:.1f}")
        print(f"   Total time: {elapsed}")
        print(f"   Speed: {self.stats['pages_scraped']/(elapsed.total_seconds()/3600):.1f} pages/hour")
        print(f"   Errors: {self.stats['errors']}")
        
        # Market analysis
        if self.all_properties:
            self._analyze_market_data()
    
    def _analyze_market_data(self):
        """Analyze the collected market data"""
        print(f"\n📈 MARKET ANALYSIS:")
        
        # Market segments
        segments = {}
        for prop in self.all_properties:
            segment = prop.get('market_segment', 'Unknown')
            segments[segment] = segments.get(segment, 0) + 1
        
        print(f"   Market segments:")
        for segment, count in sorted(segments.items(), key=lambda x: x[1], reverse=True):
            percentage = count / len(self.all_properties) * 100
            print(f"     {segment}: {count} ({percentage:.1f}%)")
        
        # Price analysis
        prices = [p.get('price', '').replace('S$ ', '').replace(',', '') for p in self.all_properties]
        numeric_prices = []
        for price in prices:
            try:
                numeric_prices.append(int(price))
            except:
                continue
        
        if numeric_prices:
            print(f"   Price range: S$ {min(numeric_prices):,} - S$ {max(numeric_prices):,}")
            print(f"   Average price: S$ {sum(numeric_prices)//len(numeric_prices):,}")

def main():
    """Main execution function"""
    
    print("🚀 FULL-SCALE INTELLIGENT PROPERTY SCRAPER")
    print("=" * 80)
    
    # Get user input for scraping parameters
    try:
        max_pages = int(input("📄 How many pages to scrape? (default 2600): ") or "2600")
        start_page = int(input("🔢 Start from which page? (default 1): ") or "1")
    except ValueError:
        max_pages = 2600
        start_page = 1
    
    print(f"\n🎯 Configuration:")
    print(f"   Pages to scrape: {max_pages}")
    print(f"   Starting page: {start_page}")
    print(f"   Expected properties: ~{max_pages * 16}")
    print(f"   Schema: Intelligent sales format")
    
    confirm = input("\n🚀 Start full-scale scraping? (y/N): ").lower()
    if confirm != 'y':
        print("❌ Scraping cancelled")
        return
    
    # Start scraping
    scraper = FullScaleIntelligentScraper()
    success = scraper.start_full_scale_scraping(max_pages, start_page)
    
    if success:
        print("\n🎉 Full-scale intelligent scraping completed successfully!")
    else:
        print("\n❌ Full-scale scraping failed")

if __name__ == "__main__":
    main()
