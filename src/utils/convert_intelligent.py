#!/usr/bin/env python3
"""
🧠 Convert to Intelligent Sales Format
Every field filled with reasoning or removed entirely
"""

import json
import sys
import os
from datetime import datetime

# Add src directory to path for imports
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))
from schemas.intelligent_sales_schema import IntelligentSalesSchema

def convert_to_intelligent_format(input_file: str, output_file: str = None):
    """Convert technical data to intelligent sales format with complete reasoning"""
    
    print("🧠 CONVERTING TO INTELLIGENT SALES FORMAT")
    print("=" * 60)
    
    try:
        # Load technical data
        with open(input_file, 'r', encoding='utf-8') as f:
            technical_data = json.load(f)
        
        print(f"📂 Processing {len(technical_data)} properties with intelligent reasoning...")
        
        # Convert each property with intelligent reasoning
        intelligent_properties = []
        skipped = 0
        
        for i, tech_prop in enumerate(technical_data):
            intelligent_prop = IntelligentSalesSchema.create_intelligent_property(tech_prop)
            
            if intelligent_prop:
                intelligent_properties.append(intelligent_prop)
            else:
                skipped += 1
                print(f"   ⚠️ Skipped property {i+1}: insufficient data")
            
            if (i + 1) % 5 == 0:
                print(f"   🔄 Processed {i + 1}/{len(technical_data)}...")
        
        print(f"\n✅ Successfully converted {len(intelligent_properties)} properties")
        print(f"⚠️ Skipped {skipped} properties (insufficient data)")
        
        # Generate output filename
        if not output_file:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_file = f"data/intelligent_sales_{timestamp}.json"
        
        # Save intelligent data
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(intelligent_properties, f, indent=2, ensure_ascii=False)
        
        print(f"💾 Intelligent sales data saved to: {output_file}")
        
        # Analyze the intelligent data
        analyze_intelligent_data(intelligent_properties)
        
        # Show detailed samples
        print(f"\n📋 DETAILED INTELLIGENT PROPERTY SAMPLES:")
        print("=" * 60)
        
        for i, prop in enumerate(intelligent_properties[:3], 1):
            print(f"\n🏠 PROPERTY {i}: {prop['title']}")
            print(f"   💰 PRICING: {prop['price']} • {prop.get('price_psf', 'N/A')}")
            print(f"   📊 SEGMENT: {prop.get('market_segment', 'N/A')} • {prop.get('value_proposition', 'N/A')}")
            print(f"   📍 LOCATION: {prop.get('transport', 'N/A')}")
            print(f"   🏢 PROPERTY: {prop.get('age_description', 'N/A')}")
            print(f"   🎯 BUYERS: {prop.get('target_buyers', 'N/A')}")
            print(f"   📈 INVESTMENT: {prop.get('investment_outlook', 'N/A')}")
            print(f"   💡 STRATEGY: {prop.get('marketing_strategy', 'N/A')}")
            
            if prop.get('key_selling_points'):
                print(f"   🏆 HIGHLIGHTS: {', '.join(prop['key_selling_points'])}")
            
            if prop.get('competitive_advantages'):
                print(f"   ⚡ ADVANTAGES: {', '.join(prop['competitive_advantages'])}")
        
        return output_file
        
    except Exception as e:
        print(f"❌ Conversion failed: {e}")
        import traceback
        traceback.print_exc()
        return None

def analyze_intelligent_data(properties):
    """Analyze the intelligent data quality and insights"""
    
    print(f"\n📊 INTELLIGENT DATA ANALYSIS")
    print("=" * 50)
    
    total = len(properties)
    
    # Field completion analysis
    field_completion = {}
    for prop in properties:
        for key, value in prop.items():
            if value and value != "":
                field_completion[key] = field_completion.get(key, 0) + 1
    
    print(f"📈 FIELD COMPLETION RATES:")
    for field, count in sorted(field_completion.items(), key=lambda x: x[1], reverse=True):
        percentage = count / total * 100
        print(f"   {field}: {count}/{total} ({percentage:.0f}%)")
    
    # Market segment distribution
    segments = {}
    for prop in properties:
        segment = prop.get('market_segment', 'Unknown')
        segments[segment] = segments.get(segment, 0) + 1
    
    print(f"\n💰 MARKET SEGMENTS:")
    for segment, count in sorted(segments.items(), key=lambda x: x[1], reverse=True):
        percentage = count / total * 100
        print(f"   {segment}: {count} ({percentage:.0f}%)")
    
    # Transport grades
    transport_grades = {}
    for prop in properties:
        grade = prop.get('transport_grade', 'Unknown')
        transport_grades[grade] = transport_grades.get(grade, 0) + 1
    
    print(f"\n🚇 TRANSPORT GRADES:")
    for grade, count in sorted(transport_grades.items(), key=lambda x: x[1], reverse=True):
        percentage = count / total * 100
        print(f"   {grade}: {count} ({percentage:.0f}%)")
    
    # Target buyer analysis
    buyers = {}
    for prop in properties:
        buyer = prop.get('target_buyers', 'Unknown')
        buyers[buyer] = buyers.get(buyer, 0) + 1
    
    print(f"\n🎯 TARGET BUYER SEGMENTS:")
    for buyer, count in sorted(buyers.items(), key=lambda x: x[1], reverse=True):
        percentage = count / total * 100
        print(f"   {buyer}: {count} ({percentage:.0f}%)")
    
    # Investment outlook
    outlooks = {}
    for prop in properties:
        outlook = prop.get('investment_outlook', 'Unknown')
        outlooks[outlook] = outlooks.get(outlook, 0) + 1
    
    print(f"\n📈 INVESTMENT OUTLOOKS:")
    for outlook, count in sorted(outlooks.items(), key=lambda x: x[1], reverse=True):
        percentage = count / total * 100
        print(f"   {outlook}: {count} ({percentage:.0f}%)")

def compare_schemas(original_file: str, intelligent_file: str):
    """Compare original vs intelligent schemas"""
    
    print(f"\n🔍 SCHEMA COMPARISON")
    print("=" * 50)
    
    try:
        with open(original_file, 'r') as f:
            original_data = json.load(f)
        
        with open(intelligent_file, 'r') as f:
            intelligent_data = json.load(f)
        
        print(f"📊 BEFORE (Technical Schema):")
        if original_data:
            original_fields = len(original_data[0].keys())
            filled_fields = sum(1 for k, v in original_data[0].items() if v not in [None, "", [], 0])
            print(f"   Total fields: {original_fields}")
            print(f"   Filled fields: {filled_fields} ({filled_fields/original_fields*100:.0f}%)")
            print(f"   Empty fields: {original_fields - filled_fields}")
        
        print(f"\n📊 AFTER (Intelligent Schema):")
        if intelligent_data:
            intelligent_fields = len(intelligent_data[0].keys())
            filled_fields = sum(1 for k, v in intelligent_data[0].items() if v not in [None, "", [], 0])
            print(f"   Total fields: {intelligent_fields}")
            print(f"   Filled fields: {filled_fields} ({filled_fields/intelligent_fields*100:.0f}%)")
            print(f"   Empty fields: {intelligent_fields - filled_fields}")
        
        print(f"\n✅ IMPROVEMENT:")
        print(f"   ✅ Every field now has meaningful data")
        print(f"   ✅ Intelligent reasoning applied")
        print(f"   ✅ Sales-ready format")
        print(f"   ✅ No empty fields left behind")
        
    except Exception as e:
        print(f"❌ Comparison failed: {e}")

if __name__ == "__main__":
    # Convert existing data to intelligent format
    input_file = "data/advanced_extraction_2025-07-13T17-44-24.json"
    
    print("🧠 INTELLIGENT PROPERTY DATA CONVERSION")
    print("=" * 80)
    print("🎯 Goal: Every field filled with intelligent reasoning or removed")
    print("🚫 No empty fields allowed")
    print("💡 Complete sales intelligence")
    
    # Convert to intelligent format
    output_file = convert_to_intelligent_format(input_file)
    
    if output_file:
        # Compare schemas
        compare_schemas(input_file, output_file)
        
        print(f"\n🎉 INTELLIGENT CONVERSION COMPLETE!")
        print(f"📁 Intelligent data: {output_file}")
        print(f"🧠 Every field now contains meaningful, reasoned data")
        print(f"🎯 Ready for sales teams and market analysis")
    else:
        print("❌ Intelligent conversion failed!")
