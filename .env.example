# Database Configuration
DATABASE_URL=postgresql://username:password@localhost:5432/propertyguru_db
REDIS_URL=redis://localhost:6379/0

# API Keys
GEMINI_API_KEY=your_gemini_api_key_here
OPENAI_API_KEY=your_openai_api_key_here

# Scraping Configuration
SCRAPING_DELAY_MIN=1
SCRAPING_DELAY_MAX=3
MAX_CONCURRENT_SCRAPERS=3
USER_AGENT_ROTATION=true

# Proxy Configuration (Optional)
PROXY_ENABLED=false
PROXY_LIST_URL=
PROXY_USERNAME=
PROXY_PASSWORD=

# Security
SECRET_KEY=your_secret_key_here
JWT_ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# Application Settings
DEBUG=true
LOG_LEVEL=INFO
API_HOST=0.0.0.0
API_PORT=8000

# Monitoring
PROMETHEUS_PORT=8001
ENABLE_METRICS=true
