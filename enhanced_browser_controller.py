#!/usr/bin/env python3
"""
Enhanced Browser Controller for PropertyGuru Scraping
Improved version with better error handling and CAPTCHA support
"""

import sys
import time
import json
import logging
from pathlib import Path

# Add scraper to path
sys.path.append(str(Path(__file__).parent))

from scraper.existing_browser_controller import ExistingBrowserController

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def enhanced_property_extraction(controller):
    """Enhanced property extraction with better error handling"""
    try:
        print("🔍 Analyzing page structure...")
        
        # Wait for page to stabilize
        time.sleep(3)
        
        # Check page source for debugging
        page_source = controller.driver.page_source
        page_title = controller.driver.title
        current_url = controller.driver.current_url
        
        print(f"📄 Page Title: {page_title}")
        print(f"🔗 Current URL: {current_url}")
        
        # Check for various blocking scenarios
        if "cloudflare" in page_source.lower():
            print("🚫 Cloudflare protection detected")
            return handle_cloudflare_scenario(controller)
        
        if "captcha" in page_source.lower():
            print("🤖 CAPTCHA detected")
            return handle_captcha_scenario(controller)
        
        if "access denied" in page_source.lower():
            print("🚫 Access denied detected")
            return []
        
        # Try to find property elements with multiple strategies
        return try_multiple_extraction_strategies(controller)
        
    except Exception as e:
        logger.error(f"Enhanced extraction failed: {e}")
        return []


def handle_cloudflare_scenario(controller):
    """Handle Cloudflare protection scenario"""
    print("\n🛡️ Cloudflare Protection Detected")
    print("=" * 50)
    print("The page is protected by Cloudflare. Here's what to do:")
    print()
    print("1. 👀 Look at your browser window")
    print("2. 🔄 If you see 'Checking your browser', wait for it to complete")
    print("3. 🤖 If you see a CAPTCHA, solve it manually")
    print("4. ✅ Wait until you see property listings")
    print("5. 🔄 Return here and press Enter")
    print()
    
    input("⏳ Press Enter after Cloudflare check completes...")
    
    # Wait a bit more for page to load
    time.sleep(5)
    
    # Try extraction again
    return try_multiple_extraction_strategies(controller)


def handle_captcha_scenario(controller):
    """Handle CAPTCHA scenario"""
    print("\n🤖 CAPTCHA Detected")
    print("=" * 30)
    print("Please solve the CAPTCHA in your browser window")
    print("Then return here and press Enter")
    
    input("⏳ Press Enter after solving CAPTCHA...")
    
    # Wait for page to reload
    time.sleep(3)
    
    # Try extraction again
    return try_multiple_extraction_strategies(controller)


def try_multiple_extraction_strategies(controller):
    """Try multiple strategies to extract properties"""
    strategies = [
        extract_with_data_testid,
        extract_with_class_selectors,
        extract_with_generic_selectors,
        extract_with_text_analysis
    ]
    
    for i, strategy in enumerate(strategies, 1):
        try:
            print(f"🔍 Trying extraction strategy {i}...")
            properties = strategy(controller)
            
            if properties:
                print(f"✅ Strategy {i} successful: {len(properties)} properties found")
                return properties
            else:
                print(f"❌ Strategy {i} found no properties")
                
        except Exception as e:
            print(f"❌ Strategy {i} failed: {e}")
            continue
    
    print("❌ All extraction strategies failed")
    return []


def extract_with_data_testid(controller):
    """Extract using data-testid attributes"""
    from selenium.webdriver.common.by import By
    
    selectors = [
        "[data-testid='listing-card']",
        "[data-testid='property-card']",
        "[data-testid='search-result']"
    ]
    
    for selector in selectors:
        elements = controller.driver.find_elements(By.CSS_SELECTOR, selector)
        if elements:
            return extract_from_elements(controller, elements, f"data-testid: {selector}")
    
    return []


def extract_with_class_selectors(controller):
    """Extract using common class selectors"""
    from selenium.webdriver.common.by import By
    
    selectors = [
        ".listing-card",
        ".property-card",
        ".search-result",
        ".property-listing",
        ".listing-item"
    ]
    
    for selector in selectors:
        elements = controller.driver.find_elements(By.CSS_SELECTOR, selector)
        if elements:
            return extract_from_elements(controller, elements, f"class: {selector}")
    
    return []


def extract_with_generic_selectors(controller):
    """Extract using generic selectors"""
    from selenium.webdriver.common.by import By
    
    # Look for containers with multiple links (likely property listings)
    containers = controller.driver.find_elements(By.CSS_SELECTOR, "div")
    
    property_containers = []
    for container in containers:
        try:
            # Check if container has multiple property-like elements
            links = container.find_elements(By.CSS_SELECTOR, "a")
            prices = container.find_elements(By.CSS_SELECTOR, "*[class*='price'], *[class*='Price']")
            
            if len(links) >= 1 and len(prices) >= 1:
                property_containers.append(container)
                
        except:
            continue
    
    if property_containers:
        return extract_from_elements(controller, property_containers[:20], "generic containers")
    
    return []


def extract_with_text_analysis(controller):
    """Extract by analyzing text patterns"""
    from selenium.webdriver.common.by import By
    
    # Look for elements containing price patterns
    all_elements = controller.driver.find_elements(By.CSS_SELECTOR, "*")
    
    property_elements = []
    for element in all_elements:
        try:
            text = element.text
            if text and ("$" in text or "SGD" in text) and ("sqft" in text.lower() or "bedroom" in text.lower()):
                property_elements.append(element)
        except:
            continue
    
    if property_elements:
        return extract_from_elements(controller, property_elements[:10], "text analysis")
    
    return []


def extract_from_elements(controller, elements, strategy_name):
    """Extract property data from elements"""
    properties = []
    
    print(f"📊 Extracting from {len(elements)} elements using {strategy_name}")
    
    for i, element in enumerate(elements):
        try:
            prop_data = {
                "extraction_strategy": strategy_name,
                "element_index": i
            }
            
            # Extract text content
            try:
                prop_data["raw_text"] = element.text.strip()
            except:
                prop_data["raw_text"] = ""
            
            # Extract links
            try:
                from selenium.webdriver.common.by import By
                links = element.find_elements(By.CSS_SELECTOR, "a")
                if links:
                    prop_data["url"] = links[0].get_attribute("href")
                    prop_data["title"] = links[0].text.strip()
            except:
                prop_data["url"] = ""
                prop_data["title"] = ""
            
            # Extract price patterns
            text = prop_data["raw_text"]
            if text:
                import re
                
                # Price patterns
                price_patterns = [
                    r'\$[\d,]+',
                    r'SGD\s*[\d,]+',
                    r'[\d,]+\s*million',
                    r'[\d,]+k'
                ]
                
                for pattern in price_patterns:
                    match = re.search(pattern, text, re.IGNORECASE)
                    if match:
                        prop_data["price"] = match.group()
                        break
                
                # Bedroom patterns
                bedroom_match = re.search(r'(\d+)\s*(?:bed|br|bedroom)', text, re.IGNORECASE)
                if bedroom_match:
                    prop_data["bedrooms"] = bedroom_match.group(1)
                
                # Area patterns
                area_match = re.search(r'(\d+(?:,\d+)?)\s*(?:sqft|sq ft)', text, re.IGNORECASE)
                if area_match:
                    prop_data["area"] = area_match.group(1)
            
            # Only add if we found some useful data
            if prop_data.get("title") or prop_data.get("price") or len(prop_data["raw_text"]) > 20:
                properties.append(prop_data)
                
        except Exception as e:
            logger.debug(f"Error extracting from element {i}: {e}")
            continue
    
    return properties


def main():
    """Enhanced main function"""
    print("🚀 Enhanced PropertyGuru Browser Controller")
    print("=" * 60)
    print("🎯 Target: Extract properties from your existing browser")
    print("🛡️ Features: Cloudflare handling, CAPTCHA support, multiple strategies")
    print("=" * 60)
    
    controller = ExistingBrowserController()
    
    try:
        print("\n🔗 Connecting to existing browser...")
        if not controller.connect_to_existing_browser():
            print("❌ Could not connect to browser")
            print("💡 Make sure Chrome is running with debugging enabled")
            return
        
        print("✅ Connected successfully!")
        
        # Get current page info
        current_url = controller.driver.current_url
        page_title = controller.driver.title
        
        print(f"📄 Current page: {page_title}")
        print(f"🔗 URL: {current_url}")
        
        # Navigate to PropertyGuru if needed
        if "propertyguru" not in current_url.lower():
            print("🌐 Navigating to PropertyGuru...")
            target_url = "https://www.propertyguru.com.sg/property-for-sale?districtCode=D01&districtCode=D09"
            controller.navigate_to_propertyguru(target_url)
        
        # Enhanced property extraction
        properties = enhanced_property_extraction(controller)
        
        if properties:
            print(f"\n🎉 SUCCESS: Extracted {len(properties)} properties!")
            
            # Save results
            timestamp = time.strftime("%Y%m%d_%H%M%S")
            filename = f"enhanced_extraction_{timestamp}.json"
            
            with open(filename, 'w') as f:
                json.dump(properties, f, indent=2)
            
            print(f"💾 Results saved to: {filename}")
            
            # Show sample results
            print("\n📋 Sample Properties:")
            for i, prop in enumerate(properties[:5], 1):
                print(f"   {i}. Title: {prop.get('title', 'N/A')[:50]}")
                print(f"      Price: {prop.get('price', 'N/A')}")
                print(f"      Bedrooms: {prop.get('bedrooms', 'N/A')}")
                print(f"      Area: {prop.get('area', 'N/A')}")
                print(f"      Strategy: {prop.get('extraction_strategy', 'N/A')}")
                print(f"      URL: {prop.get('url', 'N/A')[:50]}...")
                print()
            
            print("🎯 Extraction successful!")
            print("📊 Review the JSON file for complete data")
            
        else:
            print("❌ No properties extracted")
            print("💡 The page might still be loading or blocked")
        
        input("\n⏳ Press Enter to finish...")
        
    except KeyboardInterrupt:
        print("\n⏹️ Interrupted by user")
    except Exception as e:
        print(f"\n💥 Error: {e}")
    finally:
        # Don't close the browser, just disconnect
        if controller:
            controller.close()


if __name__ == "__main__":
    main()
