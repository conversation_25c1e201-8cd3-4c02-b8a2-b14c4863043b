# 🔒 SurfShark VPN CLI Setup Guide for PropertyGuru Scraper

## 📋 Overview

This guide will help you set up SurfShark VPN CLI for use with the comprehensive PropertyGuru scraper. The VPN integration provides automatic IP rotation across 5 Southeast Asian server locations to avoid detection during large-scale scraping.

## 🎯 Why VPN for PropertyGuru Scraping?

- **IP Rotation**: Automatically change IP addresses to avoid rate limiting
- **Geographic Distribution**: Use servers across Southeast Asia for natural traffic patterns
- **Session Isolation**: Each scraping session uses a different IP address
- **Detection Avoidance**: Reduce risk of being blocked by PropertyGuru's anti-bot systems

## 🛠️ Installation Steps

### Step 1: SurfShark Account Setup

1. **Get SurfShark Subscription**:
   - Visit [SurfShark.com](https://surfshark.com)
   - Sign up for a subscription (required for CLI access)
   - Note your login credentials

2. **Verify Account**:
   - Confirm your email address
   - Ensure your subscription is active

### Step 2: Install SurfShark VPN CLI

#### For macOS:
```bash
# Download the installer
curl -f https://downloads.surfshark.com/macOS/stable/SurfsharkVPN.dmg -o SurfsharkVPN.dmg

# Mount and install
sudo hdiutil attach SurfsharkVPN.dmg
sudo installer -pkg "/Volumes/SurfsharkVPN/SurfsharkVPN.pkg" -target /

# Verify installation
surfshark-vpn --version
```

#### For Ubuntu/Debian:
```bash
# Add SurfShark repository
curl -f https://downloads.surfshark.com/linux/debian-install.sh | sh

# Install SurfShark VPN
sudo apt update
sudo apt install surfshark-vpn

# Verify installation
surfshark-vpn --version
```

#### For CentOS/RHEL/Fedora:
```bash
# Add SurfShark repository
curl -f https://downloads.surfshark.com/linux/centos-install.sh | sh

# Install SurfShark VPN
sudo yum install surfshark-vpn
# OR for newer versions:
sudo dnf install surfshark-vpn

# Verify installation
surfshark-vpn --version
```

#### For Windows:
```powershell
# Download from official website
# Visit: https://downloads.surfshark.com/windows/latest/SurfsharkSetup.exe
# Run the installer as Administrator

# Verify installation (in Command Prompt)
surfshark-vpn --version
```

### Step 3: Login to SurfShark CLI

```bash
# Login with your SurfShark credentials
surfshark-vpn login

# Enter your email and password when prompted
# Email: <EMAIL>
# Password: your-password
```

### Step 4: Verify Available Servers

```bash
# List all available servers
surfshark-vpn servers

# List servers by country (for Southeast Asia)
surfshark-vpn servers --country sg  # Singapore
surfshark-vpn servers --country my  # Malaysia
surfshark-vpn servers --country th  # Thailand
surfshark-vpn servers --country id  # Indonesia
surfshark-vpn servers --country ph  # Philippines
```

## 🌏 Recommended Server Configuration

The PropertyGuru scraper is configured to use these specific servers:

```bash
# Singapore servers (primary)
sg-sng.prod.surfshark.com

# Malaysia servers (backup)
my-kul.prod.surfshark.com

# Thailand servers (backup)
th-bkk.prod.surfshark.com

# Indonesia servers (backup)
id-jkt.prod.surfshark.com

# Philippines servers (backup)
ph-mnl.prod.surfshark.com
```

## 🧪 Test VPN Connection

### Manual Connection Test:
```bash
# Connect to Singapore server
surfshark-vpn connect sg-sng.prod.surfshark.com

# Check your IP address
curl https://httpbin.org/ip

# Disconnect
surfshark-vpn disconnect

# Check IP again (should be different)
curl https://httpbin.org/ip
```

### Test with PropertyGuru Scraper:
```bash
# Run scraper with VPN enabled (test mode)
python run_comprehensive_scraper.py --config test --vpn

# The scraper will automatically:
# 1. Connect to a random server
# 2. Verify IP change
# 3. Start scraping
# 4. Rotate servers as needed
```

## ⚙️ Configuration for PropertyGuru Scraper

### VPN Settings in Scraper:

The comprehensive scraper automatically handles VPN management:

```python
# VPN is enabled in development and production configs
configs = {
    "development": {
        "use_vpn": True,  # VPN enabled
        "session_duration_minutes": 30  # Rotate every 30 minutes
    },
    "production": {
        "use_vpn": True,  # VPN enabled
        "session_duration_minutes": 60  # Rotate every hour
    }
}
```

### Server Rotation Strategy:

1. **Initial Connection**: Random server from available list
2. **Session Rotation**: New server every 30-60 minutes
3. **Error Recovery**: Automatic server change on connection issues
4. **Geographic Distribution**: Balanced usage across Southeast Asian servers

## 🔧 Troubleshooting

### Common Issues and Solutions:

#### 1. "Command not found: surfshark-vpn"
```bash
# Check if installed correctly
which surfshark-vpn

# If not found, reinstall:
# - macOS: Re-run the .dmg installer
# - Linux: Check package manager installation
# - Windows: Run installer as Administrator
```

#### 2. "Authentication failed"
```bash
# Re-login with correct credentials
surfshark-vpn logout
surfshark-vpn login

# Verify account status at surfshark.com
```

#### 3. "Connection timeout"
```bash
# Try different server
surfshark-vpn connect my-kul.prod.surfshark.com

# Check internet connection
ping google.com

# Restart VPN service (Linux/macOS)
sudo systemctl restart surfshark-vpn
```

#### 4. "Server not available"
```bash
# List current available servers
surfshark-vpn servers --country sg

# Use any available Singapore server
surfshark-vpn connect [server-name-from-list]
```

### VPN Status Commands:
```bash
# Check connection status
surfshark-vpn status

# Show current IP
surfshark-vpn ip

# Show connection logs
surfshark-vpn logs

# Disconnect if needed
surfshark-vpn disconnect
```

## 🚀 Using VPN with PropertyGuru Scraper

### Quick Start with VPN:
```bash
# Test configuration (2 pages, VPN disabled for testing)
python run_comprehensive_scraper.py --config test --no-vpn

# Development with VPN (10 pages, VPN enabled)
python run_comprehensive_scraper.py --config development

# Production with VPN (unlimited pages, full VPN rotation)
python run_comprehensive_scraper.py --config production
```

### Custom VPN Configuration:
```bash
# Enable VPN with custom settings
python run_comprehensive_scraper.py \
    --pages 20 \
    --vpn \
    --headless \
    --delay-min 8 \
    --delay-max 20 \
    --session-duration 45
```

## 📊 VPN Performance Monitoring

The scraper provides VPN statistics in the results:

```json
{
  "statistics": {
    "vpn_rotations": 3,
    "current_ip": "103.xxx.xxx.xxx"
  },
  "configuration": {
    "use_vpn": true,
    "session_duration_minutes": 60
  }
}
```

## ⚠️ Important Notes

### Legal Considerations:
- ✅ **Respect Terms of Service**: Always comply with PropertyGuru's terms
- ✅ **Rate Limiting**: Built-in delays prevent server overload
- ✅ **Data Usage**: Ensure compliance with data protection laws

### Technical Considerations:
- 🔒 **VPN Overhead**: Adds 1-2 seconds per request (worth it for protection)
- 🌐 **Server Selection**: Southeast Asian servers provide best performance
- 🔄 **Automatic Rotation**: Scraper handles all VPN management automatically
- 💾 **Fallback Mode**: Continues without VPN if connection fails

## ✅ Verification Checklist

Before running production scraping:

- [ ] SurfShark VPN CLI installed and working
- [ ] Successfully logged in with valid subscription
- [ ] Can connect to Singapore server manually
- [ ] IP address changes when connecting/disconnecting
- [ ] Test scraper runs with `--config test --vpn`
- [ ] No connection errors in scraper logs

## 🎯 Ready to Scrape!

Once VPN is set up, you can run the comprehensive PropertyGuru scraper with full IP rotation protection:

```bash
# Full production scrape with VPN protection
python run_comprehensive_scraper.py --config production
```

The scraper will automatically handle all VPN operations, providing maximum protection while scraping all 52,383 Singapore properties! 🏠🔒
