"""
Pydantic schemas for request/response validation
"""
from datetime import datetime, date
from typing import Optional, List, Dict, Any
from decimal import Decimal
from pydantic import BaseModel, Field, ConfigDict


class PropertyBase(BaseModel):
    """Base property schema"""
    property_guru_id: str = Field(..., max_length=50)
    title: str = Field(..., max_length=500)
    description: Optional[str] = None
    property_type: str = Field(..., max_length=50)  # 'HDB', 'Condo', 'Landed', 'Commercial'
    listing_type: str = Field(..., max_length=20)   # 'sale', 'rent'
    price: Optional[Decimal] = None
    price_psf: Optional[Decimal] = None
    bedrooms: Optional[int] = None
    bathrooms: Optional[int] = None
    floor_area_sqft: Optional[int] = None
    land_area_sqft: Optional[int] = None
    floor_level: Optional[str] = None
    furnishing: Optional[str] = None
    tenure: Optional[str] = None
    built_year: Optional[int] = None
    address: str
    postal_code: Optional[str] = None
    district: Optional[int] = None
    latitude: Optional[Decimal] = None
    longitude: Optional[Decimal] = None
    mrt_distance_m: Optional[int] = None
    nearest_mrt: Optional[str] = None
    listing_date: Optional[date] = None
    available_date: Optional[date] = None
    status: str = "active"
    images: Optional[List[str]] = None
    amenities: Optional[List[str]] = None
    additional_info: Optional[Dict[str, Any]] = None


class PropertyCreate(PropertyBase):
    """Schema for creating a property"""
    agent_id: Optional[int] = None


class PropertyUpdate(BaseModel):
    """Schema for updating a property"""
    title: Optional[str] = None
    description: Optional[str] = None
    price: Optional[Decimal] = None
    price_psf: Optional[Decimal] = None
    status: Optional[str] = None
    images: Optional[List[str]] = None
    amenities: Optional[List[str]] = None
    additional_info: Optional[Dict[str, Any]] = None


class PropertyResponse(PropertyBase):
    """Schema for property response"""
    id: int
    agent_id: Optional[int] = None
    created_at: datetime
    updated_at: datetime
    scraped_at: datetime
    
    model_config = ConfigDict(from_attributes=True)


class AgentBase(BaseModel):
    """Base agent schema"""
    name: str = Field(..., max_length=200)
    agency: Optional[str] = None
    phone: Optional[str] = None
    email: Optional[str] = None
    cea_number: Optional[str] = None
    profile_url: Optional[str] = None
    rating: Optional[Decimal] = None


class AgentCreate(AgentBase):
    """Schema for creating an agent"""
    pass


class AgentResponse(AgentBase):
    """Schema for agent response"""
    id: int
    total_listings: int = 0
    created_at: datetime
    updated_at: datetime
    
    model_config = ConfigDict(from_attributes=True)


class PropertyHistoryResponse(BaseModel):
    """Schema for property history response"""
    id: int
    property_id: int
    field_name: str
    old_value: Optional[str] = None
    new_value: Optional[str] = None
    change_type: str
    change_percentage: Optional[Decimal] = None
    changed_at: datetime
    detected_by: str = "scraper"
    
    model_config = ConfigDict(from_attributes=True)


class ScrapingJobCreate(BaseModel):
    """Schema for creating a scraping job"""
    job_type: str = Field(..., max_length=50)
    filters: Optional[Dict[str, Any]] = None


class ScrapingJobResponse(BaseModel):
    """Schema for scraping job response"""
    id: int
    job_id: str
    job_type: str
    status: str
    filters: Optional[Dict[str, Any]] = None
    total_pages: Optional[int] = None
    pages_scraped: int = 0
    properties_found: int = 0
    properties_new: int = 0
    properties_updated: int = 0
    errors_count: int = 0
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    created_at: datetime
    error_message: Optional[str] = None
    
    model_config = ConfigDict(from_attributes=True)


class SearchQueryCreate(BaseModel):
    """Schema for creating a search query log"""
    query_text: str
    filters_applied: Optional[Dict[str, Any]] = None
    results_count: Optional[int] = None
    user_ip: Optional[str] = None
    user_agent: Optional[str] = None
    response_time_ms: Optional[int] = None


class PropertySearchFilters(BaseModel):
    """Schema for property search filters"""
    property_type: Optional[str] = None
    listing_type: Optional[str] = None
    min_price: Optional[Decimal] = None
    max_price: Optional[Decimal] = None
    bedrooms: Optional[int] = None
    min_bedrooms: Optional[int] = None
    max_bedrooms: Optional[int] = None
    district: Optional[int] = None
    postal_code: Optional[str] = None
    mrt_distance_max: Optional[int] = None
    status: Optional[str] = "active"


class PropertySearchResponse(BaseModel):
    """Schema for property search response"""
    properties: List[PropertyResponse]
    total: int
    page: int
    limit: int
    pages: int


class DatabaseHealthResponse(BaseModel):
    """Schema for database health check response"""
    status: str
    connection: str
    property_count: Optional[int] = None
    engine_pool_size: Optional[int] = None
    engine_pool_checked_out: Optional[int] = None
    error: Optional[str] = None
