"""
SQLAlchemy ORM models for PropertyGuru scraper database
"""
from datetime import datetime
from typing import Optional
from sqlalchemy import (
    Column, Integer, String, Text, DECIMAL, Date, DateTime, 
    Boolean, ForeignKey, Index, JSON
)
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func

Base = declarative_base()


class Property(Base):
    """Property listings table"""
    __tablename__ = "properties"
    
    # Primary key
    id = Column(Integer, primary_key=True, index=True)
    property_guru_id = Column(String(50), unique=True, nullable=False, index=True)
    
    # Basic info
    title = Column(String(500), nullable=False)
    description = Column(Text)
    property_type = Column(String(50), nullable=False, index=True)  # 'HDB', 'Condo', 'Landed', 'Commercial'
    listing_type = Column(String(20), nullable=False, index=True)   # 'sale', 'rent'
    
    # Pricing
    price = Column(DECIMAL(15, 2), index=True)
    price_psf = Column(DECIMAL(10, 2))
    
    # Property details
    bedrooms = Column(Integer, index=True)
    bathrooms = Column(Integer)
    floor_area_sqft = Column(Integer)
    land_area_sqft = Column(Integer)
    floor_level = Column(String(20))
    furnishing = Column(String(50))
    tenure = Column(String(100))
    built_year = Column(Integer)
    
    # Location
    address = Column(Text, nullable=False)
    postal_code = Column(String(10), index=True)
    district = Column(Integer, index=True)
    latitude = Column(DECIMAL(10, 8))
    longitude = Column(DECIMAL(11, 8))
    mrt_distance_m = Column(Integer)
    nearest_mrt = Column(String(100))
    
    # Dates
    listing_date = Column(Date)
    available_date = Column(Date)
    
    # Status
    status = Column(String(20), default='active', index=True)  # 'active', 'sold', 'rented', 'expired'
    
    # Agent relationship
    agent_id = Column(Integer, ForeignKey('agents.id'))
    agent = relationship("Agent", back_populates="properties")
    
    # JSON fields for flexible data
    images = Column(JSON)  # Array of image URLs
    amenities = Column(JSON)  # Array of amenities
    additional_info = Column(JSON)  # Flexible JSON for extra attributes
    
    # Timestamps
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())
    scraped_at = Column(DateTime, default=func.now())
    
    # Relationships
    history = relationship("PropertyHistory", back_populates="property", cascade="all, delete-orphan")
    
    def __repr__(self):
        return f"<Property(id={self.id}, title='{self.title[:50]}...', price={self.price})>"


class Agent(Base):
    """Real estate agents table"""
    __tablename__ = "agents"
    
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(200), nullable=False)
    agency = Column(String(200), index=True)
    phone = Column(String(20))
    email = Column(String(100))
    cea_number = Column(String(20), unique=True, index=True)
    profile_url = Column(Text)
    rating = Column(DECIMAL(3, 2))
    total_listings = Column(Integer, default=0)
    
    # Timestamps
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())
    
    # Relationships
    properties = relationship("Property", back_populates="agent")
    
    def __repr__(self):
        return f"<Agent(id={self.id}, name='{self.name}', agency='{self.agency}')>"


class PropertyHistory(Base):
    """Property change history table"""
    __tablename__ = "property_history"
    
    id = Column(Integer, primary_key=True, index=True)
    property_id = Column(Integer, ForeignKey('properties.id', ondelete='CASCADE'), nullable=False, index=True)
    
    # Change details
    field_name = Column(String(50), nullable=False)
    old_value = Column(Text)
    new_value = Column(Text)
    change_type = Column(String(20), nullable=False, index=True)  # 'price_change', 'status_change', 'update'
    change_percentage = Column(DECIMAL(5, 2))  # For price changes
    
    # Metadata
    changed_at = Column(DateTime, default=func.now(), index=True)
    detected_by = Column(String(50), default='scraper')
    
    # Relationships
    property = relationship("Property", back_populates="history")
    
    def __repr__(self):
        return f"<PropertyHistory(id={self.id}, property_id={self.property_id}, change_type='{self.change_type}')>"


class ScrapingJob(Base):
    """Scraping jobs tracking table"""
    __tablename__ = "scraping_jobs"
    
    id = Column(Integer, primary_key=True, index=True)
    job_id = Column(String(36), unique=True, nullable=False, index=True)  # UUID
    
    # Job details
    job_type = Column(String(50), nullable=False)  # 'full_scrape', 'incremental', 'single_property'
    status = Column(String(20), default='pending', index=True)  # 'pending', 'running', 'completed', 'failed'
    filters = Column(JSON)  # Search filters used
    
    # Progress tracking
    total_pages = Column(Integer)
    pages_scraped = Column(Integer, default=0)
    properties_found = Column(Integer, default=0)
    properties_new = Column(Integer, default=0)
    properties_updated = Column(Integer, default=0)
    errors_count = Column(Integer, default=0)
    
    # Timestamps
    started_at = Column(DateTime)
    completed_at = Column(DateTime)
    created_at = Column(DateTime, default=func.now(), index=True)
    
    # Error handling
    error_message = Column(Text)
    
    def __repr__(self):
        return f"<ScrapingJob(id={self.id}, job_id='{self.job_id}', status='{self.status}')>"


class SearchQuery(Base):
    """Search queries analytics table"""
    __tablename__ = "search_queries"
    
    id = Column(Integer, primary_key=True, index=True)
    query_text = Column(Text, nullable=False)
    filters_applied = Column(JSON)
    results_count = Column(Integer)
    
    # User tracking (anonymous)
    user_ip = Column(String(45))
    user_agent = Column(Text)
    
    # Performance
    response_time_ms = Column(Integer)
    
    # Timestamp
    created_at = Column(DateTime, default=func.now(), index=True)
    
    def __repr__(self):
        return f"<SearchQuery(id={self.id}, query='{self.query_text[:50]}...', results={self.results_count})>"


# Create indexes for better performance
Index('idx_properties_type_listing', Property.property_type, Property.listing_type)
Index('idx_properties_location', Property.district, Property.postal_code)
Index('idx_property_history_property_changed', PropertyHistory.property_id, PropertyHistory.changed_at)
