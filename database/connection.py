"""
Database connection and session management
"""
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.pool import StaticPool
from config.settings import settings
from database.models import Base
import logging

logger = logging.getLogger(__name__)

# Create database engine
engine = create_engine(
    settings.database_url,
    # Connection pool settings
    pool_size=10,
    max_overflow=20,
    pool_pre_ping=True,  # Verify connections before use
    pool_recycle=3600,   # Recycle connections every hour
    echo=settings.debug  # Log SQL queries in debug mode
)

# Create session factory
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)


def create_tables():
    """Create all database tables"""
    try:
        Base.metadata.create_all(bind=engine)
        logger.info("Database tables created successfully")
    except Exception as e:
        logger.error(f"Error creating database tables: {e}")
        raise


def get_db() -> Session:
    """
    Dependency to get database session
    Use this in FastAPI endpoints with Depends(get_db)
    """
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()


async def get_db_async() -> Session:
    """
    Async version of get_db for async endpoints
    """
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()


def check_database_connection() -> bool:
    """
    Check if database connection is working
    Returns True if connection is successful, False otherwise
    """
    try:
        db = SessionLocal()
        # Try to execute a simple query
        from sqlalchemy import text
        db.execute(text("SELECT 1"))
        db.close()
        return True
    except Exception as e:
        logger.error(f"Database connection check failed: {e}")
        return False


class DatabaseManager:
    """Database management utilities"""

    @staticmethod
    def init_db():
        """Initialize database with tables"""
        create_tables()

    @staticmethod
    def get_session() -> Session:
        """Get a new database session"""
        return SessionLocal()

    @staticmethod
    def close_session(db: Session):
        """Close database session"""
        db.close()

    @staticmethod
    def health_check() -> dict:
        """Comprehensive database health check"""
        try:
            db = SessionLocal()

            # Test basic connection
            from sqlalchemy import text
            db.execute(text("SELECT 1"))

            # Test table access
            from database.models import Property
            property_count = db.query(Property).count()

            db.close()

            return {
                "status": "healthy",
                "connection": "ok",
                "property_count": property_count,
                "engine_pool_size": engine.pool.size(),
                "engine_pool_checked_out": engine.pool.checkedout()
            }
        except Exception as e:
            logger.error(f"Database health check failed: {e}")
            return {
                "status": "unhealthy",
                "error": str(e),
                "connection": "failed"
            }
