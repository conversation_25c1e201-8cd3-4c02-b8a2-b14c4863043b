"""
Property repository for database operations
"""
from typing import List, Optional, Dict, Any
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, desc, asc
from database.models import Property, Agent, PropertyHistory
from database.schemas import PropertyCreate, PropertyUpdate, PropertySearchFilters
import logging

logger = logging.getLogger(__name__)


class PropertyRepository:
    """Repository for property-related database operations"""
    
    def __init__(self, db: Session):
        self.db = db
    
    def create_property(self, property_data: PropertyCreate) -> Property:
        """Create a new property"""
        try:
            db_property = Property(**property_data.model_dump())
            self.db.add(db_property)
            self.db.commit()
            self.db.refresh(db_property)
            logger.info(f"Created property with ID: {db_property.id}")
            return db_property
        except Exception as e:
            self.db.rollback()
            logger.error(f"Error creating property: {e}")
            raise
    
    def get_property_by_id(self, property_id: int) -> Optional[Property]:
        """Get property by ID"""
        return self.db.query(Property).filter(Property.id == property_id).first()
    
    def get_property_by_guru_id(self, property_guru_id: str) -> Optional[Property]:
        """Get property by PropertyGuru ID"""
        return self.db.query(Property).filter(Property.property_guru_id == property_guru_id).first()
    
    def update_property(self, property_id: int, property_data: PropertyUpdate) -> Optional[Property]:
        """Update an existing property"""
        try:
            db_property = self.get_property_by_id(property_id)
            if not db_property:
                return None
            
            # Store old values for history tracking
            old_values = {}
            update_data = property_data.model_dump(exclude_unset=True)
            
            for field, new_value in update_data.items():
                if hasattr(db_property, field):
                    old_value = getattr(db_property, field)
                    if old_value != new_value:
                        old_values[field] = old_value
                        setattr(db_property, field, new_value)
            
            if old_values:
                self.db.commit()
                self.db.refresh(db_property)
                
                # Create history records for changes
                self._create_history_records(property_id, old_values, update_data)
                
                logger.info(f"Updated property ID: {property_id}, fields: {list(old_values.keys())}")
            
            return db_property
        except Exception as e:
            self.db.rollback()
            logger.error(f"Error updating property {property_id}: {e}")
            raise
    
    def delete_property(self, property_id: int) -> bool:
        """Delete a property"""
        try:
            db_property = self.get_property_by_id(property_id)
            if not db_property:
                return False
            
            self.db.delete(db_property)
            self.db.commit()
            logger.info(f"Deleted property ID: {property_id}")
            return True
        except Exception as e:
            self.db.rollback()
            logger.error(f"Error deleting property {property_id}: {e}")
            raise
    
    def search_properties(
        self, 
        filters: PropertySearchFilters, 
        page: int = 1, 
        limit: int = 20,
        sort_by: str = "created_at",
        sort_order: str = "desc"
    ) -> Dict[str, Any]:
        """Search properties with filters and pagination"""
        try:
            query = self.db.query(Property)
            
            # Apply filters
            if filters.property_type:
                query = query.filter(Property.property_type == filters.property_type)
            
            if filters.listing_type:
                query = query.filter(Property.listing_type == filters.listing_type)
            
            if filters.min_price:
                query = query.filter(Property.price >= filters.min_price)
            
            if filters.max_price:
                query = query.filter(Property.price <= filters.max_price)
            
            if filters.bedrooms:
                query = query.filter(Property.bedrooms == filters.bedrooms)
            
            if filters.min_bedrooms:
                query = query.filter(Property.bedrooms >= filters.min_bedrooms)
            
            if filters.max_bedrooms:
                query = query.filter(Property.bedrooms <= filters.max_bedrooms)
            
            if filters.district:
                query = query.filter(Property.district == filters.district)
            
            if filters.postal_code:
                query = query.filter(Property.postal_code == filters.postal_code)
            
            if filters.mrt_distance_max:
                query = query.filter(Property.mrt_distance_m <= filters.mrt_distance_max)
            
            if filters.status:
                query = query.filter(Property.status == filters.status)
            
            # Get total count
            total = query.count()
            
            # Apply sorting
            sort_column = getattr(Property, sort_by, Property.created_at)
            if sort_order.lower() == "desc":
                query = query.order_by(desc(sort_column))
            else:
                query = query.order_by(asc(sort_column))
            
            # Apply pagination
            offset = (page - 1) * limit
            properties = query.offset(offset).limit(limit).all()
            
            # Calculate pagination info
            pages = (total + limit - 1) // limit
            
            return {
                "properties": properties,
                "total": total,
                "page": page,
                "limit": limit,
                "pages": pages
            }
        except Exception as e:
            logger.error(f"Error searching properties: {e}")
            raise
    
    def get_properties_by_agent(self, agent_id: int, limit: int = 50) -> List[Property]:
        """Get properties by agent ID"""
        return (
            self.db.query(Property)
            .filter(Property.agent_id == agent_id)
            .order_by(desc(Property.created_at))
            .limit(limit)
            .all()
        )
    
    def get_property_history(self, property_id: int) -> List[PropertyHistory]:
        """Get property change history"""
        return (
            self.db.query(PropertyHistory)
            .filter(PropertyHistory.property_id == property_id)
            .order_by(desc(PropertyHistory.changed_at))
            .all()
        )
    
    def get_recent_properties(self, limit: int = 20) -> List[Property]:
        """Get recently added properties"""
        return (
            self.db.query(Property)
            .filter(Property.status == "active")
            .order_by(desc(Property.created_at))
            .limit(limit)
            .all()
        )
    
    def get_property_stats(self) -> Dict[str, Any]:
        """Get property statistics"""
        try:
            total_properties = self.db.query(Property).count()
            active_properties = self.db.query(Property).filter(Property.status == "active").count()
            
            # Properties by type
            property_types = (
                self.db.query(Property.property_type, self.db.func.count(Property.id))
                .group_by(Property.property_type)
                .all()
            )
            
            # Properties by listing type
            listing_types = (
                self.db.query(Property.listing_type, self.db.func.count(Property.id))
                .group_by(Property.listing_type)
                .all()
            )
            
            return {
                "total_properties": total_properties,
                "active_properties": active_properties,
                "property_types": dict(property_types),
                "listing_types": dict(listing_types)
            }
        except Exception as e:
            logger.error(f"Error getting property stats: {e}")
            raise
    
    def _create_history_records(self, property_id: int, old_values: Dict, new_values: Dict):
        """Create history records for property changes"""
        try:
            for field, old_value in old_values.items():
                new_value = new_values.get(field)
                
                # Determine change type
                change_type = "update"
                change_percentage = None
                
                if field == "price" and old_value and new_value:
                    change_type = "price_change"
                    change_percentage = ((float(new_value) - float(old_value)) / float(old_value)) * 100
                elif field == "status":
                    change_type = "status_change"
                
                history_record = PropertyHistory(
                    property_id=property_id,
                    field_name=field,
                    old_value=str(old_value) if old_value is not None else None,
                    new_value=str(new_value) if new_value is not None else None,
                    change_type=change_type,
                    change_percentage=change_percentage
                )
                
                self.db.add(history_record)
            
            self.db.commit()
        except Exception as e:
            logger.error(f"Error creating history records: {e}")
            raise
