#!/usr/bin/env python3
"""
🔍 Inspect PropertyGuru Pagination
Find the correct selectors for next button
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'scraper'))

from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
import time

def inspect_pagination():
    """Inspect the pagination structure on PropertyGuru"""
    print("🔍 Inspecting PropertyGuru Pagination")
    print("=" * 50)
    
    try:
        # Connect to existing Chrome
        chrome_options = Options()
        chrome_options.add_experimental_option("debuggerAddress", "127.0.0.1:9222")
        driver = webdriver.Chrome(options=chrome_options)
        
        print("✅ Connected to Chrome")
        print(f"📄 Current page: {driver.title}")
        print(f"🔗 URL: {driver.current_url}")
        
        # Look for pagination elements
        print("\n🔍 Searching for pagination elements...")
        
        # Common pagination selectors
        selectors_to_try = [
            # Generic pagination
            '.pagination',
            '.pager',
            '.page-nav',
            '.page-navigation',
            '[class*="pagination"]',
            '[class*="pager"]',
            '[class*="page"]',
            
            # Next button variations
            'a[aria-label*="next"]',
            'a[aria-label*="Next"]',
            'button[aria-label*="next"]',
            'button[aria-label*="Next"]',
            'a[title*="next"]',
            'a[title*="Next"]',
            'button[title*="next"]',
            'button[title*="Next"]',
            
            # Text-based
            'a:contains("Next")',
            'button:contains("Next")',
            'a:contains("next")',
            'button:contains("next")',
            'a:contains(">")',
            'button:contains(">")',
            
            # PropertyGuru specific (guessing)
            '[data-testid*="next"]',
            '[data-testid*="pagination"]',
            '.next-page',
            '.pagination-next',
            '.btn-next',
            '.page-next'
        ]
        
        found_elements = []
        
        for selector in selectors_to_try:
            try:
                if ':contains(' in selector:
                    # Handle text-based selectors with XPath
                    if 'Next' in selector:
                        elements = driver.find_elements(By.XPATH, "//*[contains(text(), 'Next')]")
                    elif 'next' in selector:
                        elements = driver.find_elements(By.XPATH, "//*[contains(text(), 'next')]")
                    elif '>' in selector:
                        elements = driver.find_elements(By.XPATH, "//*[contains(text(), '>')]")
                    else:
                        continue
                else:
                    elements = driver.find_elements(By.CSS_SELECTOR, selector)
                
                if elements:
                    print(f"\n✅ Found {len(elements)} elements with: {selector}")
                    for i, element in enumerate(elements[:3]):  # Show first 3
                        try:
                            tag = element.tag_name
                            text = element.text.strip()[:50]
                            classes = element.get_attribute('class')
                            enabled = element.is_enabled()
                            displayed = element.is_displayed()
                            
                            print(f"   {i+1}. <{tag}> '{text}' class='{classes}' enabled={enabled} displayed={displayed}")
                            
                            if enabled and displayed and (tag in ['a', 'button']):
                                found_elements.append({
                                    'selector': selector,
                                    'element': element,
                                    'tag': tag,
                                    'text': text,
                                    'classes': classes
                                })
                        except Exception as e:
                            print(f"   {i+1}. Error getting element info: {e}")
            except Exception as e:
                continue
        
        # Look for page numbers to understand structure
        print("\n🔢 Looking for page numbers...")
        page_selectors = [
            'a[href*="page="]',
            'button[data-page]',
            '.page-number',
            '.page-item',
            '[class*="page-"]'
        ]
        
        for selector in page_selectors:
            try:
                elements = driver.find_elements(By.CSS_SELECTOR, selector)
                if elements:
                    print(f"\n📄 Found {len(elements)} page elements with: {selector}")
                    for i, element in enumerate(elements[:5]):
                        try:
                            text = element.text.strip()
                            href = element.get_attribute('href')
                            print(f"   {i+1}. '{text}' href='{href}'")
                        except:
                            pass
            except:
                continue
        
        # Try to find any clickable elements at the bottom of the page
        print("\n🔍 Looking for clickable elements at bottom of page...")
        try:
            # Scroll to bottom
            driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
            time.sleep(2)
            
            # Look for clickable elements in the bottom area
            bottom_elements = driver.find_elements(By.XPATH, "//a | //button")
            bottom_clickable = []
            
            for element in bottom_elements:
                try:
                    if element.is_displayed() and element.is_enabled():
                        text = element.text.strip().lower()
                        if any(word in text for word in ['next', '>', 'more', 'page']):
                            bottom_clickable.append(element)
                except:
                    continue
            
            if bottom_clickable:
                print(f"✅ Found {len(bottom_clickable)} potentially relevant clickable elements:")
                for i, element in enumerate(bottom_clickable[:5]):
                    try:
                        tag = element.tag_name
                        text = element.text.strip()[:30]
                        classes = element.get_attribute('class')
                        print(f"   {i+1}. <{tag}> '{text}' class='{classes}'")
                    except:
                        pass
        except Exception as e:
            print(f"❌ Error scrolling/searching bottom: {e}")
        
        # Summary
        print(f"\n📊 Summary:")
        print(f"   Found {len(found_elements)} potential next buttons")
        
        if found_elements:
            print("\n🎯 Best candidates for next button:")
            for i, elem in enumerate(found_elements[:3], 1):
                print(f"   {i}. {elem['selector']} -> <{elem['tag']}> '{elem['text']}'")
        else:
            print("❌ No suitable next button found")
            print("💡 The page might use JavaScript pagination or infinite scroll")
        
    except Exception as e:
        print(f"❌ Inspection failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    inspect_pagination()
