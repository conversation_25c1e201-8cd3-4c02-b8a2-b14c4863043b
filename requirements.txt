# Web Scraping
selenium>=4.15.0
webdriver-manager>=4.0.0
beautifulsoup4>=4.12.0
requests>=2.31.0
lxml>=4.9.0
selenium-stealth>=1.0.6
undetected-chromedriver>=3.5.0

# Web Framework
fastapi>=0.104.0
uvicorn[standard]>=0.24.0
python-multipart>=0.0.6
pydantic-settings>=2.0.0

# Database
sqlalchemy>=2.0.20
alembic>=1.12.0
psycopg2-binary>=2.9.0
redis>=5.0.0

# AI Integration
google-generativeai>=0.3.0
openai>=1.3.0

# Data Processing
pandas>=2.0.0
numpy>=1.24.0
pydantic>=2.5.0
python-dateutil>=2.8.0

# Task Queue
celery>=5.3.0
kombu>=5.3.0

# Authentication & Security
python-jose[cryptography]>=3.3.0
passlib[bcrypt]>=1.7.0
python-dotenv>=1.0.0

# Testing
pytest>=7.4.0
pytest-asyncio>=0.21.0
pytest-mock>=3.12.0
httpx>=0.25.0

# Monitoring & Logging
prometheus-client>=0.19.0
structlog>=23.2.0

# Utilities
click>=8.1.0
tqdm>=4.66.0
schedule>=1.2.0
