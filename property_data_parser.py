#!/usr/bin/env python3
"""
🏠 PropertyGuru Data Parser
Extracts clean, structured property data from raw scraped content
"""

import json
import re
from typing import List, Dict, Any
from datetime import datetime

class PropertyDataParser:
    def __init__(self):
        self.property_patterns = {
            'name': r'([A-Z][a-zA-Z\s&@]+(?:Residences?|Towers?|Hill|House|Nine|Waterfront|Handy|Paterson|Promont|Emerald|Shenton|Newton|Zion|Hijauan|Cairnhill|Attitude|Leonie|Wharf|Abode|Tribeca|Haus))',
            'address': r'(\d+[A-Z]?\s+[A-Za-z\s]+(?:Road|Street|Avenue|Hill|Way|Circle|Drive))',
            'price': r'S\$\s*([\d,]+,\d+)',
            'bedrooms': r'(\d+)\s+Beds?',
            'bathrooms': r'(\d+)\s+Baths?',
            'area': r'(\d+,?\d*)\s+sqft',
            'psf': r'S\$\s*([\d,]+\.?\d*)\s+psf',
            'mrt_distance': r'(\d+)\s+min\s+\((\d+)\s+m\)\s+from\s+([A-Z0-9]+\s+[A-Za-z\s]+MRT\s+Station)',
            'property_type': r'(Condominium|Apartment|HDB|Executive Condominium|House)',
            'tenure': r'(Freehold|99-year Leasehold|999-year Leasehold|\d+-year Leasehold)',
            'built_year': r'Built:\s*(\d{4})',
            'new_project': r'New Project:\s*(\d{4})',
            'agent_name': r'Listed by\s*([A-Za-z\s\u4e00-\u9fff]+)',
            'listing_time': r'Listed on\s+([A-Za-z]+\s+\d+,\s+\d+)\s+\(([^)]+)\s+ago\)'
        }
    
    def extract_properties_from_raw_text(self, raw_text: str) -> List[Dict[str, Any]]:
        """Extract structured property data from raw scraped text"""
        properties = []
        
        # Split text into potential property blocks
        # Look for property names followed by addresses and prices
        property_blocks = self._split_into_property_blocks(raw_text)
        
        for i, block in enumerate(property_blocks):
            property_data = self._extract_property_from_block(block, i)
            if property_data and self._is_valid_property(property_data):
                properties.append(property_data)
        
        return properties
    
    def _split_into_property_blocks(self, text: str) -> List[str]:
        """Split raw text into individual property blocks"""
        # Look for patterns that indicate property listings
        # Properties typically start with a name and have price info
        
        # Split by common property indicators
        lines = text.split('\n')
        blocks = []
        current_block = []
        
        for line in lines:
            line = line.strip()
            if not line:
                continue
                
            # Check if this line starts a new property
            if (re.search(r'S\$\s*[\d,]+', line) and 
                re.search(r'\d+\s+Beds?', line) and
                len(current_block) > 5):
                # Save previous block
                if current_block:
                    blocks.append('\n'.join(current_block))
                current_block = [line]
            else:
                current_block.append(line)
        
        # Add the last block
        if current_block:
            blocks.append('\n'.join(current_block))
        
        return blocks
    
    def _extract_property_from_block(self, block: str, index: int) -> Dict[str, Any]:
        """Extract property data from a single text block"""
        property_data = {
            'id': f'property_{index}',
            'extraction_timestamp': datetime.now().isoformat(),
            'source': 'PropertyGuru'
        }
        
        # Extract each field using regex patterns
        for field, pattern in self.property_patterns.items():
            matches = re.findall(pattern, block, re.IGNORECASE)
            if matches:
                if field == 'mrt_distance':
                    # Special handling for MRT distance (returns tuple)
                    if len(matches[0]) == 3:
                        property_data['mrt_minutes'] = matches[0][0]
                        property_data['mrt_meters'] = matches[0][1]
                        property_data['mrt_station'] = matches[0][2]
                elif field == 'listing_time':
                    # Special handling for listing time (returns tuple)
                    if len(matches[0]) == 2:
                        property_data['listing_date'] = matches[0][0]
                        property_data['listing_ago'] = matches[0][1]
                else:
                    property_data[field] = matches[0]
        
        # Clean and format the data
        property_data = self._clean_property_data(property_data)
        
        return property_data
    
    def _clean_property_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Clean and format property data"""
        # Remove commas from numeric fields
        numeric_fields = ['price', 'area', 'psf', 'bedrooms', 'bathrooms', 'built_year', 'new_project']
        for field in numeric_fields:
            if field in data and isinstance(data[field], str):
                data[field] = data[field].replace(',', '')
                try:
                    if field in ['bedrooms', 'bathrooms', 'built_year', 'new_project']:
                        data[field] = int(data[field])
                    elif field in ['price', 'area']:
                        data[field] = int(data[field])
                    elif field == 'psf':
                        data[field] = float(data[field])
                except ValueError:
                    pass
        
        # Clean text fields
        text_fields = ['name', 'address', 'agent_name']
        for field in text_fields:
            if field in data and isinstance(data[field], str):
                data[field] = data[field].strip()
        
        return data
    
    def _is_valid_property(self, data: Dict[str, Any]) -> bool:
        """Check if extracted property data is valid"""
        required_fields = ['price', 'bedrooms']
        return all(field in data for field in required_fields)
    
    def parse_json_file(self, json_file_path: str) -> List[Dict[str, Any]]:
        """Parse properties from a JSON file containing raw scraped data"""
        with open(json_file_path, 'r', encoding='utf-8') as f:
            raw_data = json.load(f)
        
        all_properties = []
        
        for item in raw_data:
            if 'raw_text' in item:
                properties = self.extract_properties_from_raw_text(item['raw_text'])
                all_properties.extend(properties)
        
        return all_properties
    
    def save_parsed_properties(self, properties: List[Dict[str, Any]], output_file: str):
        """Save parsed properties to JSON file"""
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(properties, f, indent=2, ensure_ascii=False)
        
        print(f"💾 Saved {len(properties)} parsed properties to {output_file}")

def main():
    parser = PropertyDataParser()
    
    # Parse the raw scraped data
    input_file = "enhanced_extraction_20250712_211519.json"
    output_file = f"parsed_properties_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    
    print("🔍 Parsing raw property data...")
    properties = parser.parse_json_file(input_file)
    
    print(f"✅ Extracted {len(properties)} properties")
    
    # Display sample properties
    print("\n📋 Sample Properties:")
    for i, prop in enumerate(properties[:5], 1):
        print(f"\n   {i}. {prop.get('name', 'Unknown')}")
        print(f"      Address: {prop.get('address', 'N/A')}")
        print(f"      Price: S$ {prop.get('price', 'N/A'):,}" if isinstance(prop.get('price'), int) else f"      Price: {prop.get('price', 'N/A')}")
        print(f"      Bedrooms: {prop.get('bedrooms', 'N/A')}")
        print(f"      Area: {prop.get('area', 'N/A')} sqft")
        print(f"      Type: {prop.get('property_type', 'N/A')}")
        print(f"      Tenure: {prop.get('tenure', 'N/A')}")
    
    # Save parsed data
    parser.save_parsed_properties(properties, output_file)
    
    return output_file

if __name__ == "__main__":
    main()
