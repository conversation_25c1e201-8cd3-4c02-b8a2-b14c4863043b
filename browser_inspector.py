#!/usr/bin/env python3
"""
🔍 Browser Inspector
Checks current browser state and helps with debugging
"""

import time
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By

def inspect_browser():
    """Inspect current browser state"""
    try:
        # Connect to existing browser
        chrome_options = Options()
        chrome_options.add_experimental_option("debuggerAddress", "127.0.0.1:9222")
        driver = webdriver.Chrome(options=chrome_options)
        
        print("🔗 Connected to browser")
        print(f"📄 Current URL: {driver.current_url}")
        print(f"📄 Page Title: {driver.title}")
        
        # Check page content
        body_text = driver.find_element(By.TAG_NAME, "body").text[:500]
        print(f"\n📝 Page Content (first 500 chars):")
        print(body_text)
        
        # Check for Cloudflare
        if "cloudflare" in body_text.lower() or "just a moment" in body_text.lower():
            print("\n🛡️ Cloudflare protection detected!")
            print("💡 Waiting for <PERSON><PERSON>lar<PERSON> to pass...")
            
            # Wait and check again
            for i in range(10):
                time.sleep(3)
                try:
                    new_title = driver.title
                    if "just a moment" not in new_title.lower():
                        print(f"✅ Cloudflare passed! New title: {new_title}")
                        break
                    print(f"⏳ Still waiting... ({i+1}/10)")
                except:
                    pass
        
        # Look for property elements
        print("\n🔍 Looking for property elements...")
        
        # Try different selectors
        selectors_to_try = [
            "div[data-testid*='listing']",
            "div[class*='listing']",
            "div[class*='property']",
            "div:contains('S$')",
            "div:contains('Beds')",
            "*[class*='card']"
        ]
        
        for selector in selectors_to_try:
            try:
                elements = driver.find_elements(By.CSS_SELECTOR, selector)
                if elements:
                    print(f"✅ Found {len(elements)} elements with selector: {selector}")
                    # Show first element text
                    if elements[0].text:
                        print(f"   Sample text: {elements[0].text[:100]}...")
                else:
                    print(f"❌ No elements found with: {selector}")
            except Exception as e:
                print(f"❌ Error with selector {selector}: {e}")
        
        # Get all text content
        print(f"\n📊 Total page text length: {len(driver.page_source)} characters")
        
        driver.quit()
        
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    inspect_browser()
