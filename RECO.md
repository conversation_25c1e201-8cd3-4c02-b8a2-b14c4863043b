Recommendation for PropertyGuru
DO NOT use Firefox - it's more detectable by Cloudflare.
Priority order:

Undetected Chrome - Best success rate for Cloudflare
Rotating proxies - Essential for avoiding IP blocks
Request throttling - Mimic human behavior
Cloudscraper - For simpler pages without heavy JS

Install requirements:
bashpip install undetected-chromedriver
pip install cloudscraper
pip install playwright
Success factors for PropertyGuru:

Residential proxies (not data center)
Realistic timing patterns (2-30 seconds between actions)
Browser fingerprint rotation
Session management (don't reuse the same browser instance)

The security warning you saw with Chrome is actually the least of your concerns with PropertyGuru - Cloudflare protection is the main challenge.