#!/usr/bin/env python3
"""
🛡️ Manual Cloudflare Bypass Helper
Waits for you to manually bypass Cloudflare, then extracts data
"""

import time
import json
from datetime import datetime
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
import re

class ManualCloudflareBypass:
    def __init__(self):
        self.driver = None
        
    def connect_to_browser(self):
        """Connect to existing browser"""
        try:
            chrome_options = Options()
            chrome_options.add_experimental_option("debuggerAddress", "127.0.0.1:9222")
            self.driver = webdriver.Chrome(options=chrome_options)
            print("✅ Connected to browser")
            return True
        except Exception as e:
            print(f"❌ Connection failed: {e}")
            return False
    
    def wait_for_cloudflare_bypass(self):
        """Wait for user to manually bypass Cloudflare"""
        print("\n🛡️ CLOUDFLARE BYPASS REQUIRED")
        print("=" * 50)
        print("Please help us get past Cloudflare:")
        print("1. In your Chrome browser, you should see a Cloudflare page")
        print("2. Wait for it to automatically pass (usually 5-10 seconds)")
        print("3. OR solve any CAPTCHA if presented")
        print("4. Once you see PropertyGuru property listings, come back here")
        print("5. Press Enter when PropertyGuru is fully loaded")
        
        input("\n⏳ Press Enter when PropertyGuru is loaded and ready...")
        
        # Check if we're past Cloudflare
        for attempt in range(5):
            try:
                current_url = self.driver.current_url
                page_title = self.driver.title
                
                print(f"🔍 Checking page... (Attempt {attempt + 1})")
                print(f"   URL: {current_url}")
                print(f"   Title: {page_title}")
                
                if "propertyguru.com.sg" in current_url and "just a moment" not in page_title.lower():
                    print("✅ Successfully past Cloudflare!")
                    return True
                    
                time.sleep(2)
                
            except Exception as e:
                print(f"⚠️ Check failed: {e}")
        
        print("❌ Still seems to be blocked. Let's try anyway...")
        return False
    
    def extract_property_data(self):
        """Extract property data from the current page"""
        properties = []
        
        try:
            print("\n🔍 Extracting property data...")
            
            # Wait a bit for page to fully load
            time.sleep(3)
            
            # Get page source and look for property data
            page_source = self.driver.page_source
            
            # Method 1: Look for property cards in HTML
            print("📊 Method 1: Looking for property elements...")
            
            # Try to find property listing elements
            property_selectors = [
                '[data-testid*="listing"]',
                '[class*="listing-card"]',
                '[class*="property-card"]',
                'div[class*="card"]',
                'article',
                'div[class*="item"]'
            ]
            
            property_elements = []
            for selector in property_selectors:
                try:
                    elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    if elements:
                        print(f"   ✅ Found {len(elements)} elements with: {selector}")
                        property_elements = elements
                        break
                except:
                    continue
            
            # Method 2: Extract from page text using regex
            if not property_elements:
                print("📊 Method 2: Extracting from page text...")
                properties = self._extract_from_page_text()
            else:
                print(f"📊 Processing {len(property_elements)} property elements...")
                for i, element in enumerate(property_elements[:20]):
                    try:
                        prop_data = self._extract_from_element(element, i)
                        if prop_data:
                            properties.append(prop_data)
                    except Exception as e:
                        print(f"⚠️ Error processing element {i}: {e}")
            
        except Exception as e:
            print(f"❌ Extraction error: {e}")
        
        return properties
    
    def _extract_from_page_text(self):
        """Extract properties from page text using regex"""
        properties = []
        
        try:
            # Get all text from the page
            page_text = self.driver.find_element(By.TAG_NAME, "body").text
            
            # Look for property patterns in the text
            # Pattern: Property name, price, beds, area
            property_pattern = r'([A-Z][a-zA-Z\s&@]+(?:Residences?|Towers?|Hill|House|Nine|Waterfront|Handy|Paterson|Promont|Emerald|Shenton|Newton|Zion|Hijauan|Cairnhill|Attitude|Leonie|Wharf|Abode|Tribeca|Haus))[^S]*S\$\s*([\d,]+)[^0-9]*(\d+)\s+Beds?[^0-9]*(\d+)\s+Baths?[^0-9]*(\d+,?\d*)\s+sqft'
            
            matches = re.findall(property_pattern, page_text, re.MULTILINE | re.DOTALL)
            
            for i, match in enumerate(matches[:20]):
                try:
                    property_data = {
                        'id': f'property_{i}',
                        'name': match[0].strip(),
                        'price': int(match[1].replace(',', '')),
                        'bedrooms': int(match[2]),
                        'bathrooms': int(match[3]),
                        'area': int(match[4].replace(',', '')),
                        'extraction_timestamp': datetime.now().isoformat(),
                        'source': 'PropertyGuru'
                    }
                    properties.append(property_data)
                except Exception as e:
                    print(f"⚠️ Error parsing property {i}: {e}")
            
            print(f"✅ Extracted {len(properties)} properties from page text")
            
        except Exception as e:
            print(f"❌ Text extraction error: {e}")
        
        return properties
    
    def _extract_from_element(self, element, index):
        """Extract property data from a single element"""
        try:
            text = element.text
            
            # Extract basic info using regex
            property_data = {'id': f'property_{index}'}
            
            # Price
            price_match = re.search(r'S\$\s*([\d,]+)', text)
            if price_match:
                property_data['price'] = int(price_match.group(1).replace(',', ''))
            
            # Bedrooms
            bed_match = re.search(r'(\d+)\s+Beds?', text)
            if bed_match:
                property_data['bedrooms'] = int(bed_match.group(1))
            
            # Area
            area_match = re.search(r'(\d+,?\d*)\s+sqft', text)
            if area_match:
                property_data['area'] = int(area_match.group(1).replace(',', ''))
            
            # Only return if we have essential data
            if 'price' in property_data and 'bedrooms' in property_data:
                property_data['extraction_timestamp'] = datetime.now().isoformat()
                property_data['source'] = 'PropertyGuru'
                return property_data
            
        except Exception as e:
            print(f"⚠️ Element extraction error: {e}")
        
        return None
    
    def save_properties(self, properties):
        """Save extracted properties to file"""
        if not properties:
            print("❌ No properties to save")
            return None
        
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f'manual_extraction_{timestamp}.json'
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(properties, f, indent=2, ensure_ascii=False)
        
        print(f"💾 Saved {len(properties)} properties to {filename}")
        return filename
    
    def close(self):
        """Close browser connection"""
        if self.driver:
            self.driver.quit()

def main():
    scraper = ManualCloudflareBypass()
    
    try:
        # Connect to browser
        if not scraper.connect_to_browser():
            return
        
        # Wait for manual Cloudflare bypass
        scraper.wait_for_cloudflare_bypass()
        
        # Extract properties
        properties = scraper.extract_property_data()
        
        if properties:
            print(f"\n🎉 SUCCESS! Extracted {len(properties)} properties")
            
            # Show sample
            print("\n📋 Sample Properties:")
            for i, prop in enumerate(properties[:3], 1):
                print(f"   {i}. {prop.get('name', 'Unknown')}")
                print(f"      Price: S$ {prop.get('price', 'N/A'):,}" if isinstance(prop.get('price'), int) else f"      Price: {prop.get('price', 'N/A')}")
                print(f"      Bedrooms: {prop.get('bedrooms', 'N/A')}")
                print(f"      Area: {prop.get('area', 'N/A')} sqft")
            
            # Save data
            filename = scraper.save_properties(properties)
            print(f"\n✅ Extraction complete! Data saved to: {filename}")
            
        else:
            print("❌ No properties extracted")
    
    except Exception as e:
        print(f"❌ Error: {e}")
    
    finally:
        scraper.close()

if __name__ == "__main__":
    main()
