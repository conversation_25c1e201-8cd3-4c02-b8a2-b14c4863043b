#!/usr/bin/env python3
"""
🎯 Precise PropertyGuru Scraper
Uses specific CSS selectors to extract clean property data
"""

import json
import time
import logging
from datetime import datetime
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.common.exceptions import TimeoutException, NoSuchElementException
import re

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class PrecisePropertyScraper:
    def __init__(self):
        self.driver = None
        self.wait = None
        
    def connect_to_existing_browser(self):
        """Connect to existing Chrome browser with debugging enabled"""
        try:
            chrome_options = Options()
            chrome_options.add_experimental_option("debuggerAddress", "127.0.0.1:9222")
            
            self.driver = webdriver.Chrome(options=chrome_options)
            self.wait = WebDriverWait(self.driver, 10)
            
            logger.info("✅ Connected to existing Chrome browser")
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to connect to browser: {e}")
            return False
    
    def navigate_to_propertyguru(self):
        """Navigate to PropertyGuru property listings"""
        url = "https://www.propertyguru.com.sg/property-for-sale?districtCode=D01&districtCode=D09"
        
        try:
            logger.info(f"🌐 Navigating to: {url}")
            self.driver.get(url)
            
            # Wait for page to load
            self.wait.until(EC.presence_of_element_located((By.TAG_NAME, "body")))
            time.sleep(3)
            
            logger.info(f"📄 Page loaded: {self.driver.title}")
            return True
            
        except Exception as e:
            logger.error(f"❌ Navigation failed: {e}")
            return False
    
    def extract_properties(self):
        """Extract property data using precise CSS selectors"""
        properties = []
        
        try:
            # Wait for property listings to load
            logger.info("🔍 Looking for property listings...")
            
            # Try multiple selectors for property cards
            property_selectors = [
                '[data-testid="listing-card"]',
                '.listing-card',
                '.property-card',
                '[class*="listing"]',
                '[class*="property"]'
            ]
            
            property_elements = []
            for selector in property_selectors:
                try:
                    elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    if elements:
                        logger.info(f"✅ Found {len(elements)} properties using selector: {selector}")
                        property_elements = elements
                        break
                except:
                    continue
            
            if not property_elements:
                # Fallback: look for any elements containing property data
                logger.info("🔄 Using fallback method...")
                property_elements = self.driver.find_elements(By.XPATH, "//div[contains(text(), 'S$') and contains(text(), 'Beds')]")
            
            logger.info(f"📊 Processing {len(property_elements)} property elements...")
            
            for i, element in enumerate(property_elements[:20]):  # Limit to first 20
                try:
                    property_data = self._extract_property_data(element, i)
                    if property_data:
                        properties.append(property_data)
                        logger.info(f"✅ Extracted property {i+1}: {property_data.get('name', 'Unknown')}")
                except Exception as e:
                    logger.warning(f"⚠️ Failed to extract property {i+1}: {e}")
                    continue
            
        except Exception as e:
            logger.error(f"❌ Property extraction failed: {e}")
        
        return properties
    
    def _extract_property_data(self, element, index):
        """Extract data from a single property element"""
        property_data = {
            'id': f'property_{index}',
            'extraction_timestamp': datetime.now().isoformat(),
            'source': 'PropertyGuru'
        }
        
        try:
            # Get the full text content
            full_text = element.text
            
            # Extract property name
            name_patterns = [
                r'([A-Z][a-zA-Z\s&@]+(?:Residences?|Towers?|Hill|House|Nine|Waterfront|Handy|Paterson|Promont|Emerald|Shenton|Newton|Zion|Hijauan|Cairnhill|Attitude|Leonie|Wharf|Abode|Tribeca|Haus))',
                r'([A-Z][a-zA-Z\s]+(?:at|@)\s+[A-Z][a-zA-Z\s]+)',
                r'(\d+\s+[A-Z][a-zA-Z\s]+)'
            ]
            
            for pattern in name_patterns:
                match = re.search(pattern, full_text)
                if match:
                    property_data['name'] = match.group(1).strip()
                    break
            
            # Extract price
            price_match = re.search(r'S\$\s*([\d,]+)', full_text)
            if price_match:
                price_str = price_match.group(1).replace(',', '')
                property_data['price'] = int(price_str)
            
            # Extract bedrooms
            bed_match = re.search(r'(\d+)\s+Beds?', full_text)
            if bed_match:
                property_data['bedrooms'] = int(bed_match.group(1))
            
            # Extract bathrooms
            bath_match = re.search(r'(\d+)\s+Baths?', full_text)
            if bath_match:
                property_data['bathrooms'] = int(bath_match.group(1))
            
            # Extract area
            area_match = re.search(r'(\d+,?\d*)\s+sqft', full_text)
            if area_match:
                area_str = area_match.group(1).replace(',', '')
                property_data['area'] = int(area_str)
            
            # Extract PSF
            psf_match = re.search(r'S\$\s*([\d,]+\.?\d*)\s+psf', full_text)
            if psf_match:
                psf_str = psf_match.group(1).replace(',', '')
                property_data['psf'] = float(psf_str)
            
            # Extract property type
            type_match = re.search(r'(Condominium|Apartment|HDB|Executive Condominium|House)', full_text)
            if type_match:
                property_data['property_type'] = type_match.group(1)
            
            # Extract tenure
            tenure_match = re.search(r'(Freehold|99-year Leasehold|999-year Leasehold|\d+-year Leasehold)', full_text)
            if tenure_match:
                property_data['tenure'] = tenure_match.group(1)
            
            # Extract address
            address_match = re.search(r'(\d+[A-Z]?\s+[A-Za-z\s]+(?:Road|Street|Avenue|Hill|Way|Circle|Drive))', full_text)
            if address_match:
                property_data['address'] = address_match.group(1).strip()
            
            # Extract MRT info
            mrt_match = re.search(r'(\d+)\s+min\s+\((\d+)\s+m\)\s+from\s+([A-Z0-9]+\s+[A-Za-z\s]+MRT\s+Station)', full_text)
            if mrt_match:
                property_data['mrt_minutes'] = int(mrt_match.group(1))
                property_data['mrt_meters'] = int(mrt_match.group(2))
                property_data['mrt_station'] = mrt_match.group(3)
            
            # Extract agent name
            agent_match = re.search(r'Listed by\s*([A-Za-z\s\u4e00-\u9fff]+)', full_text)
            if agent_match:
                property_data['agent_name'] = agent_match.group(1).strip()
            
            # Only return if we have essential data
            if 'price' in property_data and 'bedrooms' in property_data:
                return property_data
            
        except Exception as e:
            logger.warning(f"⚠️ Error extracting property data: {e}")
        
        return None
    
    def save_properties(self, properties, filename=None):
        """Save properties to JSON file"""
        if not filename:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f'precise_properties_{timestamp}.json'
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(properties, f, indent=2, ensure_ascii=False)
        
        logger.info(f"💾 Saved {len(properties)} properties to {filename}")
        return filename
    
    def close(self):
        """Close the browser connection"""
        if self.driver:
            logger.info("🔌 Disconnecting from browser (browser remains open)")
            self.driver.quit()

def main():
    scraper = PrecisePropertyScraper()
    
    try:
        # Connect to existing browser
        if not scraper.connect_to_existing_browser():
            print("❌ Failed to connect to browser")
            return
        
        # Navigate to PropertyGuru
        if not scraper.navigate_to_propertyguru():
            print("❌ Failed to navigate to PropertyGuru")
            return
        
        # Extract properties
        print("🔍 Extracting property data...")
        properties = scraper.extract_properties()
        
        if properties:
            print(f"✅ Successfully extracted {len(properties)} properties!")
            
            # Display sample properties
            print("\n📋 Sample Properties:")
            for i, prop in enumerate(properties[:5], 1):
                print(f"\n   {i}. {prop.get('name', 'Unknown')}")
                print(f"      Price: S$ {prop.get('price', 'N/A'):,}" if isinstance(prop.get('price'), int) else f"      Price: {prop.get('price', 'N/A')}")
                print(f"      Bedrooms: {prop.get('bedrooms', 'N/A')}")
                print(f"      Area: {prop.get('area', 'N/A')} sqft")
                print(f"      Type: {prop.get('property_type', 'N/A')}")
                print(f"      Address: {prop.get('address', 'N/A')}")
            
            # Save to file
            filename = scraper.save_properties(properties)
            print(f"\n💾 Data saved to: {filename}")
            
        else:
            print("❌ No properties extracted")
    
    except Exception as e:
        logger.error(f"❌ Scraping failed: {e}")
    
    finally:
        scraper.close()

if __name__ == "__main__":
    main()
