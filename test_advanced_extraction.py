#!/usr/bin/env python3
"""
🧪 Test Advanced Property Data Extraction
Test the new comprehensive data schema and extraction
"""

import sys
import os
import json
sys.path.append(os.path.join(os.path.dirname(__file__), 'scraper'))

from main_scraper import SmartPropertyScraper
from property_schema import PropertySchema

def test_advanced_extraction():
    """Test the advanced property extraction with comprehensive schema"""
    print("🧪 Testing Advanced Property Extraction")
    print("=" * 60)
    
    scraper = SmartPropertyScraper()
    
    try:
        # Connect to existing Chrome
        print("🔗 Connecting to Chrome...")
        if not scraper.connect_and_navigate():
            print("❌ Failed to connect")
            return
        
        # Handle Cloudflare
        print("🛡️ Handling Cloudflare...")
        scraper.handle_cloudflare_and_wait()
        
        # Extract properties with advanced method
        print("\n🔍 Starting advanced property extraction...")
        properties = scraper.extract_properties_smart()
        
        if properties:
            print(f"\n🎉 SUCCESS! Extracted {len(properties)} properties with advanced schema")
            
            # Analyze data quality
            print("\n📊 Data Quality Analysis:")
            total_fields = len(PropertySchema.get_schema_fields())
            
            for i, prop in enumerate(properties[:3], 1):  # Show first 3 properties
                filled_fields = sum(1 for key, value in prop.items() 
                                  if value not in [None, "", [], 0, False] or key in ["has_virtual_tour", "verified_listing"])
                quality_score = filled_fields / total_fields
                
                print(f"\n🏠 Property {i} - Quality Score: {quality_score:.1%} ({filled_fields}/{total_fields} fields)")
                print(f"   📍 Name: {prop.get('property_name', 'N/A')}")
                print(f"   💰 Price: {prop.get('price_formatted', 'N/A')}")
                print(f"   🛏️  Bedrooms: {prop.get('bedrooms', 'N/A')}")
                print(f"   🚿 Bathrooms: {prop.get('bathrooms', 'N/A')}")
                print(f"   📐 Area: {prop.get('floor_area_formatted', 'N/A')}")
                print(f"   🏢 Type: {prop.get('property_type', 'N/A')}")
                print(f"   📜 Tenure: {prop.get('tenure', 'N/A')}")
                print(f"   🚇 MRT: {prop.get('nearest_mrt', 'N/A')}")
                print(f"   👤 Agent: {prop.get('agent_name', 'N/A')}")
                print(f"   ⭐ Rating: {prop.get('agent_rating', 'N/A')}")
                print(f"   📅 Listed: {prop.get('listed_date', 'N/A')} ({prop.get('listed_time_ago', 'N/A')})")
                print(f"   🖼️  Images: {prop.get('image_count', 0)}")
                print(f"   ✅ Verified: {prop.get('verified_listing', False)}")
                print(f"   🌟 Featured: {prop.get('featured_listing', False)}")
                print(f"   🎥 Virtual Tour: {prop.get('has_virtual_tour', False)}")
                
                if prop.get('main_image_url'):
                    print(f"   📸 Image URL: {prop['main_image_url'][:50]}...")
                
                if prop.get('built_year'):
                    print(f"   🏗️  Built: {prop['built_year']}")
                
                if prop.get('price_per_sqft_formatted'):
                    print(f"   💲 PSF: {prop['price_per_sqft_formatted']}")
            
            # Show comprehensive data for one property
            if properties:
                print(f"\n📋 Complete Data Schema for Property 1:")
                print("=" * 50)
                sample_prop = properties[0]
                
                # Group fields by category
                categories = {
                    "🏠 Basic Info": ["property_name", "full_address", "district", "district_name"],
                    "💰 Pricing": ["price", "price_formatted", "price_per_sqft", "price_per_sqft_formatted"],
                    "📐 Specifications": ["bedrooms", "bathrooms", "floor_area_sqft", "floor_area_formatted", "land_area_sqft"],
                    "🏢 Property Details": ["property_type", "property_subtype", "tenure", "built_year", "completion_year"],
                    "📍 Location": ["nearest_mrt", "mrt_distance", "mrt_line", "mrt_station"],
                    "👤 Agent Info": ["agent_name", "agent_rating", "agent_company", "agent_description"],
                    "📅 Listing Info": ["listed_date", "listed_time_ago", "listing_type"],
                    "🖼️  Media": ["main_image_url", "image_count", "has_virtual_tour", "has_video"],
                    "✨ Features": ["verified_listing", "featured_listing", "floor_level", "facing", "view"],
                    "🔧 Technical": ["extraction_method", "data_quality_score", "page_number", "position_on_page"]
                }
                
                for category, fields in categories.items():
                    print(f"\n{category}:")
                    for field in fields:
                        value = sample_prop.get(field)
                        if value not in [None, "", [], 0] or field in ["has_virtual_tour", "verified_listing", "featured_listing"]:
                            print(f"   {field}: {value}")
            
            # Save data with timestamp
            timestamp = properties[0].get('extraction_timestamp', '').replace(':', '-').replace('.', '-')[:19]
            filename = f"data/advanced_extraction_{timestamp}.json"
            
            # Ensure data directory exists
            os.makedirs('data', exist_ok=True)
            
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(properties, f, indent=2, ensure_ascii=False)
            
            print(f"\n✅ Advanced extraction test complete!")
            print(f"📁 Data saved to: {filename}")
            
            # Summary statistics
            print(f"\n📊 Summary Statistics:")
            print(f"   Total properties: {len(properties)}")
            
            # Price analysis
            prices = [p.get('price') for p in properties if isinstance(p.get('price'), (int, float))]
            if prices:
                print(f"   Price range: S$ {min(prices):,} - S$ {max(prices):,}")
                print(f"   Average price: S$ {sum(prices) // len(prices):,}")
            
            # Property types
            types = [p.get('property_type') for p in properties if p.get('property_type')]
            if types:
                type_counts = {}
                for t in types:
                    type_counts[t] = type_counts.get(t, 0) + 1
                print(f"   Property types: {dict(type_counts)}")
            
            # Data quality
            quality_scores = [p.get('data_quality_score', 0) for p in properties]
            if quality_scores:
                avg_quality = sum(quality_scores) / len(quality_scores)
                print(f"   Average data quality: {avg_quality:.1%}")
            
        else:
            print("❌ No properties extracted")
    
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        print("\n🔧 Keeping Chrome open for inspection...")
        # Don't close Chrome so you can inspect the results

if __name__ == "__main__":
    test_advanced_extraction()
