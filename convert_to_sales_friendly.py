#!/usr/bin/env python3
"""
🔄 Convert Technical Data to Sales-Friendly Format
Transform the technical property schema into human-readable, sales-oriented format
"""

import json
import sys
from datetime import datetime
from sales_friendly_schema import SalesFriendlyPropertySchema

def convert_property_data(input_file: str, output_file: str = None):
    """Convert technical property data to sales-friendly format"""
    
    print("🔄 CONVERTING TO SALES-FRIENDLY FORMAT")
    print("=" * 60)
    
    try:
        # Load technical data
        print(f"📂 Loading data from: {input_file}")
        with open(input_file, 'r', encoding='utf-8') as f:
            technical_data = json.load(f)
        
        print(f"✅ Loaded {len(technical_data)} properties")
        
        # Convert each property
        sales_friendly_data = []
        conversion_stats = {
            "total": len(technical_data),
            "successful": 0,
            "errors": 0,
            "market_segments": {},
            "property_types": {},
            "transport_scores": {}
        }
        
        for i, property_data in enumerate(technical_data):
            try:
                # Convert to sales-friendly format
                sales_property = SalesFriendlyPropertySchema.convert_technical_to_sales_friendly(property_data)
                sales_friendly_data.append(sales_property)
                conversion_stats["successful"] += 1
                
                # Track statistics
                segment = sales_property["price_analysis"]["market_segment"]
                if segment:
                    conversion_stats["market_segments"][segment] = conversion_stats["market_segments"].get(segment, 0) + 1
                
                prop_type = sales_property["property_type"]
                if prop_type:
                    conversion_stats["property_types"][prop_type] = conversion_stats["property_types"].get(prop_type, 0) + 1
                
                transport = sales_property["transport_score"]
                if transport:
                    conversion_stats["transport_scores"][transport] = conversion_stats["transport_scores"].get(transport, 0) + 1
                
                # Show progress
                if (i + 1) % 10 == 0:
                    print(f"   Converted {i + 1}/{len(technical_data)} properties...")
                
            except Exception as e:
                print(f"⚠️ Error converting property {i}: {e}")
                conversion_stats["errors"] += 1
        
        # Generate output filename if not provided
        if not output_file:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_file = f"data/sales_friendly_{timestamp}.json"
        
        # Save converted data
        print(f"\n💾 Saving sales-friendly data to: {output_file}")
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(sales_friendly_data, f, indent=2, ensure_ascii=False)
        
        # Display conversion statistics
        print(f"\n📊 CONVERSION STATISTICS:")
        print(f"   Total properties: {conversion_stats['total']}")
        print(f"   Successfully converted: {conversion_stats['successful']}")
        print(f"   Errors: {conversion_stats['errors']}")
        print(f"   Success rate: {conversion_stats['successful']/conversion_stats['total']*100:.1f}%")
        
        print(f"\n🏢 MARKET SEGMENTS:")
        for segment, count in conversion_stats["market_segments"].items():
            percentage = count / conversion_stats["successful"] * 100
            print(f"   {segment}: {count} ({percentage:.1f}%)")
        
        print(f"\n🏠 PROPERTY TYPES:")
        for prop_type, count in conversion_stats["property_types"].items():
            percentage = count / conversion_stats["successful"] * 100
            print(f"   {prop_type}: {count} ({percentage:.1f}%)")
        
        print(f"\n🚇 TRANSPORT SCORES:")
        for score, count in conversion_stats["transport_scores"].items():
            percentage = count / conversion_stats["successful"] * 100
            print(f"   {score}: {count} ({percentage:.1f}%)")
        
        # Show sample conversions
        print(f"\n📋 SAMPLE SALES-FRIENDLY SUMMARIES:")
        print("-" * 50)
        
        for i, property_data in enumerate(sales_friendly_data[:3]):
            summary = SalesFriendlyPropertySchema.get_sales_summary(property_data)
            print(f"\n{i+1}. {summary}")
        
        print(f"\n✅ Conversion complete! Sales-friendly data saved to: {output_file}")
        return output_file
        
    except Exception as e:
        print(f"❌ Conversion failed: {e}")
        return None

def analyze_sales_data(sales_file: str):
    """Analyze the sales-friendly data for insights"""
    
    print(f"\n🔍 SALES DATA ANALYSIS")
    print("=" * 60)
    
    try:
        with open(sales_file, 'r', encoding='utf-8') as f:
            sales_data = json.load(f)
        
        print(f"📊 Analyzing {len(sales_data)} properties...")
        
        # Price analysis
        prices = [p["price_analysis"]["asking_price_num"] for p in sales_data if p["price_analysis"]["asking_price_num"]]
        if prices:
            print(f"\n💰 PRICE ANALYSIS:")
            print(f"   Price range: S$ {min(prices):,} - S$ {max(prices):,}")
            print(f"   Average price: S$ {sum(prices) // len(prices):,}")
            print(f"   Median price: S$ {sorted(prices)[len(prices)//2]:,}")
        
        # Transport analysis
        transport_excellent = len([p for p in sales_data if p["transport_score"] == "Excellent"])
        transport_good = len([p for p in sales_data if p["transport_score"] == "Good"])
        transport_fair = len([p for p in sales_data if p["transport_score"] == "Fair"])
        
        print(f"\n🚇 TRANSPORT ACCESSIBILITY:")
        print(f"   Excellent (≤5 min to MRT): {transport_excellent} properties")
        print(f"   Good (6-10 min to MRT): {transport_good} properties")
        print(f"   Fair (>10 min to MRT): {transport_fair} properties")
        
        # Target buyer analysis
        target_buyers = {}
        for prop in sales_data:
            target = prop["lifestyle"]["target_buyer"]
            if target:
                target_buyers[target] = target_buyers.get(target, 0) + 1
        
        print(f"\n🎯 TARGET BUYER SEGMENTS:")
        for buyer, count in sorted(target_buyers.items(), key=lambda x: x[1], reverse=True):
            percentage = count / len(sales_data) * 100
            print(f"   {buyer}: {count} ({percentage:.1f}%)")
        
        # Marketing appeal
        high_appeal = len([p for p in sales_data if p["media"]["marketing_appeal"] == "High"])
        medium_appeal = len([p for p in sales_data if p["media"]["marketing_appeal"] == "Medium"])
        low_appeal = len([p for p in sales_data if p["media"]["marketing_appeal"] == "Low"])
        
        print(f"\n📸 MARKETING APPEAL:")
        print(f"   High appeal (15+ photos): {high_appeal} properties")
        print(f"   Medium appeal (8-14 photos): {medium_appeal} properties")
        print(f"   Low appeal (<8 photos): {low_appeal} properties")
        
        # Investment potential
        affordable_count = len([p for p in sales_data if p["price_analysis"]["market_segment"] == "Affordable"])
        midrange_count = len([p for p in sales_data if p["price_analysis"]["market_segment"] == "Mid-range"])
        premium_count = len([p for p in sales_data if p["price_analysis"]["market_segment"] == "Premium"])
        luxury_count = len([p for p in sales_data if p["price_analysis"]["market_segment"] == "Luxury"])
        
        print(f"\n💎 INVESTMENT SEGMENTS:")
        print(f"   Affordable (<S$500K): {affordable_count} properties")
        print(f"   Mid-range (S$500K-1M): {midrange_count} properties")
        print(f"   Premium (S$1M-3M): {premium_count} properties")
        print(f"   Luxury (>S$3M): {luxury_count} properties")
        
    except Exception as e:
        print(f"❌ Analysis failed: {e}")

if __name__ == "__main__":
    # Convert the existing data
    input_file = "data/advanced_extraction_2025-07-13T17-44-24.json"
    
    print("🏠 PROPERTY DATA CONVERSION TO SALES-FRIENDLY FORMAT")
    print("=" * 80)
    
    # Convert technical data to sales-friendly format
    output_file = convert_property_data(input_file)
    
    if output_file:
        # Analyze the converted data
        analyze_sales_data(output_file)
        
        print(f"\n🎉 SUCCESS!")
        print(f"📁 Sales-friendly data: {output_file}")
        print(f"🔧 Technical data: {input_file}")
        print(f"\n💡 The sales-friendly format is now ready for:")
        print(f"   • Property agents and sales teams")
        print(f"   • Buyer-focused presentations")
        print(f"   • Market analysis and reporting")
        print(f"   • Investment decision making")
        print(f"   • CRM and sales pipeline integration")
    else:
        print("❌ Conversion failed!")
