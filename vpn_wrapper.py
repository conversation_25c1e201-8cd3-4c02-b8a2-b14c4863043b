#!/usr/bin/env python3
"""
VPN Wrapper for PropertyGuru Scraper
Alternative VPN management when SurfShark CLI is not available
"""

import subprocess
import time
import requests
import random
import logging
from typing import Optional, List

logger = logging.getLogger(__name__)


class MacOSVPNWrapper:
    """
    VPN wrapper for macOS that can work with various VPN solutions
    """
    
    def __init__(self):
        self.vpn_active = False
        self.current_method = None
        self.initial_ip = None
        
    def get_current_ip(self) -> str:
        """Get current public IP address"""
        try:
            response = requests.get("https://httpbin.org/ip", timeout=10)
            return response.json().get("origin", "Unknown")
        except Exception as e:
            logger.error(f"Failed to get IP: {e}")
            return "Unknown"
    
    def check_surfshark_gui(self) -> bool:
        """Check if SurfShark GUI app is running"""
        try:
            result = subprocess.run([
                "pgrep", "-f", "Surfshark"
            ], capture_output=True, text=True)
            
            return result.returncode == 0
        except Exception:
            return False
    
    def check_macos_vpn(self) -> bool:
        """Check if macOS built-in VPN is connected"""
        try:
            result = subprocess.run([
                "scutil", "--nc", "list"
            ], capture_output=True, text=True)
            
            if result.returncode == 0:
                # Look for connected VPN services
                lines = result.stdout.split('\n')
                for line in lines:
                    if "Connected" in line and ("VPN" in line or "IPSec" in line):
                        return True
            return False
        except Exception:
            return False
    
    def connect_vpn_manual_prompt(self) -> bool:
        """Prompt user to manually connect VPN"""
        try:
            print("\n🔒 VPN Connection Required")
            print("=" * 50)
            print("Since SurfShark CLI is not available, please manually connect your VPN:")
            print()
            print("Option 1: SurfShark GUI App")
            print("  1. Open SurfShark app")
            print("  2. Connect to Singapore server")
            print("  3. Wait for connection confirmation")
            print()
            print("Option 2: macOS Built-in VPN")
            print("  1. Go to System Preferences > Network")
            print("  2. Select your VPN configuration")
            print("  3. Click Connect")
            print()
            print("Option 3: Other VPN Apps")
            print("  1. Use any VPN app you have installed")
            print("  2. Connect to Singapore or Southeast Asia server")
            print()
            
            # Get initial IP
            initial_ip = self.get_current_ip()
            print(f"📍 Current IP: {initial_ip}")
            
            input("\n⏳ Press Enter after connecting to VPN...")
            
            # Check new IP
            new_ip = self.get_current_ip()
            print(f"📍 New IP: {new_ip}")
            
            if initial_ip != new_ip:
                print("✅ VPN connection detected - IP changed!")
                self.vpn_active = True
                self.current_method = "manual"
                self.initial_ip = initial_ip
                return True
            else:
                print("⚠️ IP did not change - VPN may not be connected")
                response = input("Continue anyway? (y/N): ").strip().lower()
                if response in ['y', 'yes']:
                    self.vpn_active = True
                    self.current_method = "manual"
                    return True
                return False
                
        except KeyboardInterrupt:
            print("\n❌ VPN setup cancelled")
            return False
        except Exception as e:
            logger.error(f"Manual VPN setup failed: {e}")
            return False
    
    def connect_vpn(self, server: str = None) -> bool:
        """Connect to VPN using available methods"""
        try:
            logger.info("🔒 Attempting VPN connection...")
            
            # Method 1: Check if SurfShark GUI is running
            if self.check_surfshark_gui():
                print("✅ SurfShark GUI detected - using existing connection")
                self.vpn_active = True
                self.current_method = "surfshark_gui"
                return True
            
            # Method 2: Check if macOS VPN is connected
            if self.check_macos_vpn():
                print("✅ macOS VPN detected - using existing connection")
                self.vpn_active = True
                self.current_method = "macos_vpn"
                return True
            
            # Method 3: Manual connection prompt
            return self.connect_vpn_manual_prompt()
            
        except Exception as e:
            logger.error(f"VPN connection failed: {e}")
            return False
    
    def disconnect_vpn(self) -> bool:
        """Disconnect from VPN"""
        try:
            if not self.vpn_active:
                return True
            
            if self.current_method == "manual":
                print("\n🔌 Manual VPN Disconnection")
                print("=" * 40)
                print("Please manually disconnect your VPN:")
                print("  • Close SurfShark app or disconnect")
                print("  • Disconnect macOS VPN from System Preferences")
                print("  • Turn off other VPN apps")
                
                input("\n⏳ Press Enter after disconnecting VPN...")
                
                # Verify disconnection
                new_ip = self.get_current_ip()
                if self.initial_ip and new_ip == self.initial_ip:
                    print("✅ VPN disconnected - IP restored")
                else:
                    print(f"📍 Current IP: {new_ip}")
            
            self.vpn_active = False
            self.current_method = None
            return True
            
        except Exception as e:
            logger.error(f"VPN disconnection error: {e}")
            return False
    
    def rotate_server(self) -> bool:
        """Rotate to different VPN server"""
        try:
            if not self.vpn_active:
                return self.connect_vpn()
            
            print("\n🔄 VPN Server Rotation")
            print("=" * 30)
            print("Please change to a different server:")
            print("  • Singapore → Malaysia")
            print("  • Malaysia → Thailand") 
            print("  • Thailand → Indonesia")
            print("  • Indonesia → Philippines")
            print("  • Philippines → Singapore")
            
            old_ip = self.get_current_ip()
            print(f"📍 Current IP: {old_ip}")
            
            input("\n⏳ Press Enter after changing server...")
            
            new_ip = self.get_current_ip()
            print(f"📍 New IP: {new_ip}")
            
            if old_ip != new_ip:
                print("✅ Server rotation successful")
                return True
            else:
                print("⚠️ IP did not change - server may not have rotated")
                return True  # Continue anyway
                
        except Exception as e:
            logger.error(f"Server rotation failed: {e}")
            return False


class AlternativeVPNManager:
    """
    Alternative VPN manager that uses the wrapper
    Compatible with the PropertyGuru scraper
    """
    
    def __init__(self):
        self.wrapper = MacOSVPNWrapper()
        self.vpn_active = False
        self.current_server = None
        self.available_servers = [
            "Singapore",
            "Malaysia", 
            "Thailand",
            "Indonesia",
            "Philippines"
        ]
    
    def connect_vpn(self, server: str = None) -> bool:
        """Connect to VPN (compatible with scraper interface)"""
        if server is None:
            server = random.choice(self.available_servers)
        
        logger.info(f"Connecting to VPN server: {server}")
        
        if self.wrapper.connect_vpn(server):
            self.vpn_active = True
            self.current_server = server
            logger.info(f"Successfully connected to VPN: {server}")
            time.sleep(2)  # Stabilization delay
            return True
        else:
            logger.error(f"Failed to connect to VPN: {server}")
            return False
    
    def disconnect_vpn(self) -> bool:
        """Disconnect from VPN (compatible with scraper interface)"""
        if not self.vpn_active:
            return True
        
        logger.info("Disconnecting from VPN")
        
        if self.wrapper.disconnect_vpn():
            self.vpn_active = False
            self.current_server = None
            logger.info("Successfully disconnected from VPN")
            return True
        else:
            logger.warning("VPN disconnect warning")
            return True  # Continue anyway
    
    def rotate_server(self) -> bool:
        """Rotate to different VPN server (compatible with scraper interface)"""
        try:
            # Get a different server
            available = [s for s in self.available_servers if s != self.current_server]
            if not available:
                available = self.available_servers
            
            new_server = random.choice(available)
            
            if self.wrapper.rotate_server():
                self.current_server = new_server
                logger.info(f"Rotated to server: {new_server}")
                return True
            else:
                logger.error("Server rotation failed")
                return False
                
        except Exception as e:
            logger.error(f"VPN rotation error: {e}")
            return False
    
    def get_current_ip(self) -> str:
        """Get current public IP address (compatible with scraper interface)"""
        return self.wrapper.get_current_ip()


def test_vpn_wrapper():
    """Test the VPN wrapper functionality"""
    print("🧪 Testing VPN Wrapper")
    print("=" * 30)
    
    vpn = AlternativeVPNManager()
    
    # Test connection
    if vpn.connect_vpn():
        print("✅ VPN connection test passed")
        
        # Test IP retrieval
        ip = vpn.get_current_ip()
        print(f"📍 Current IP: {ip}")
        
        # Test disconnection
        if vpn.disconnect_vpn():
            print("✅ VPN disconnection test passed")
        else:
            print("❌ VPN disconnection test failed")
    else:
        print("❌ VPN connection test failed")


if __name__ == "__main__":
    test_vpn_wrapper()
