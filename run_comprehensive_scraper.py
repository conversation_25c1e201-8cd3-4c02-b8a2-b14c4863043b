#!/usr/bin/env python3
"""
Comprehensive PropertyGuru Scraper Runner
Easy-to-use script for running the comprehensive scraper with different configurations
"""

import argparse
import sys
from pathlib import Path

# Add scraper to path
sys.path.append(str(Path(__file__).parent))

from scraper.comprehensive_scraper import ComprehensivePropertyGuru<PERSON>craper


def main():
    """Main runner function"""
    parser = argparse.ArgumentParser(
        description="Comprehensive PropertyGuru Scraper - Target all 52,383 Singapore properties",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Quick test (2 pages, no VPN, visible browser)
  python run_comprehensive_scraper.py --config test

  # Development run (10 pages, with VP<PERSON>, headless)
  python run_comprehensive_scraper.py --config development

  # Full production scrape (unlimited pages, all features)
  python run_comprehensive_scraper.py --config production

  # Custom configuration
  python run_comprehensive_scraper.py --pages 5 --vpn --headless --delay-min 3 --delay-max 8

Features:
  🔒 SurfShark VPN integration for IP rotation
  🕷️ Advanced stealth WebDriver with anti-detection
  📊 Comprehensive data extraction (60+ fields per property)
  🔄 Automatic session rotation and error handling
  💾 JSON output with detailed statistics and quality scoring
        """
    )
    
    # Configuration presets
    parser.add_argument(
        "--config",
        choices=["test", "development", "production"],
        help="Use predefined configuration preset"
    )
    
    # Custom options
    parser.add_argument(
        "--pages",
        type=int,
        help="Maximum pages to scrape (default: unlimited for production)"
    )
    
    parser.add_argument(
        "--vpn",
        action="store_true",
        help="Enable VPN rotation (requires SurfShark CLI)"
    )
    
    parser.add_argument(
        "--no-vpn",
        action="store_true",
        help="Disable VPN rotation"
    )
    
    parser.add_argument(
        "--headless",
        action="store_true",
        help="Run browser in headless mode"
    )
    
    parser.add_argument(
        "--visible",
        action="store_true",
        help="Run browser in visible mode (for debugging)"
    )
    
    parser.add_argument(
        "--delay-min",
        type=float,
        default=5.0,
        help="Minimum delay between requests (seconds)"
    )
    
    parser.add_argument(
        "--delay-max",
        type=float,
        default=15.0,
        help="Maximum delay between requests (seconds)"
    )
    
    parser.add_argument(
        "--session-duration",
        type=int,
        default=60,
        help="Session duration before rotation (minutes)"
    )
    
    args = parser.parse_args()
    
    # Print banner
    print("🚀 Comprehensive PropertyGuru Scraper")
    print("=" * 60)
    print("🎯 Target: All 52,383 Singapore properties (D01-D28)")
    print("🔗 URL: PropertyGuru sale listings with all districts")
    print("=" * 60)
    
    # Determine configuration
    if args.config:
        configs = {
            "test": {
                "headless": False,
                "use_vpn": False,
                "max_pages": 2,
                "delay_range": (2.0, 5.0),
                "session_duration_minutes": 10
            },
            "development": {
                "headless": True,
                "use_vpn": True,
                "max_pages": 10,
                "delay_range": (5.0, 12.0),
                "session_duration_minutes": 30
            },
            "production": {
                "headless": True,
                "use_vpn": True,
                "max_pages": None,  # Unlimited
                "delay_range": (8.0, 20.0),
                "session_duration_minutes": 60
            }
        }
        
        config = configs[args.config]
        print(f"📋 Using {args.config} configuration:")
        
    else:
        # Custom configuration
        config = {
            "headless": True,  # Default to headless
            "use_vpn": False,  # Default to no VPN
            "max_pages": args.pages,
            "delay_range": (args.delay_min, args.delay_max),
            "session_duration_minutes": args.session_duration
        }
        
        # Override with specific arguments
        if args.vpn:
            config["use_vpn"] = True
        if args.no_vpn:
            config["use_vpn"] = False
        if args.headless:
            config["headless"] = True
        if args.visible:
            config["headless"] = False
        
        print("📋 Using custom configuration:")
    
    # Display configuration
    print(f"   🖥️  Headless: {'Yes' if config['headless'] else 'No'}")
    print(f"   🔒 VPN: {'Yes' if config['use_vpn'] else 'No'}")
    print(f"   📄 Max pages: {config['max_pages'] or 'Unlimited'}")
    print(f"   ⏱️  Delay range: {config['delay_range'][0]}-{config['delay_range'][1]}s")
    print(f"   🔄 Session duration: {config['session_duration_minutes']} minutes")
    print()
    
    # VPN warning
    if config["use_vpn"]:
        print("🔒 VPN Features:")
        print("   • Automatic IP rotation using SurfShark VPN CLI")
        print("   • 5 server locations: Singapore, Malaysia, Thailand, Indonesia, Philippines")
        print("   • Session-based rotation for maximum stealth")
        print("   ⚠️  Requires SurfShark VPN CLI to be installed and configured")
        print()
    
    # Stealth features
    print("🕷️ Stealth Features:")
    print("   • Advanced anti-detection WebDriver configuration")
    print("   • Randomized user agents and browser fingerprinting evasion")
    print("   • Human-like behavior simulation (mouse movements, scrolling)")
    print("   • Intelligent delay patterns and session rotation")
    print("   • Comprehensive error handling and retry mechanisms")
    print()
    
    # Data extraction
    print("📊 Data Extraction:")
    print("   • 60+ fields per property (vs 20 in basic scraper)")
    print("   • Individual property page scraping for detailed information")
    print("   • Agent contact details, building specifications, location intelligence")
    print("   • Image galleries, virtual tours, and facility information")
    print("   • Data quality scoring and validation")
    print()
    
    # Confirm start
    try:
        response = input("🚀 Ready to start comprehensive scraping? (y/N): ").strip().lower()
        if response not in ['y', 'yes']:
            print("❌ Scraping cancelled by user")
            return
    except KeyboardInterrupt:
        print("\n❌ Scraping cancelled by user")
        return
    
    print("\n🎬 Starting comprehensive scraping...")
    print("=" * 60)
    
    # Initialize and run scraper
    try:
        scraper = ComprehensivePropertyGuruScraper(**config)
        results = scraper.scrape_all_properties()
        
        # Display results
        if "error" not in results:
            print("\n🎉 Scraping completed successfully!")
            print("=" * 60)
            print("📊 Final Statistics:")
            print(f"   Pages scraped: {results['statistics']['pages_scraped']}")
            print(f"   Properties found: {results['statistics']['properties_found']}")
            print(f"   Properties detailed: {results['statistics']['properties_detailed']}")
            print(f"   Success rate: {results['statistics']['success_rate']:.1f}%")
            print(f"   Errors encountered: {results['statistics']['errors']}")
            print(f"   VPN rotations: {results['statistics']['vpn_rotations']}")
            print()
            print("📈 Data Quality:")
            print(f"   Average quality score: {results['data_quality']['average_quality_score']:.1f}%")
            print(f"   Properties with agent info: {results['data_quality']['properties_with_agent_info']}")
            print(f"   Properties with images: {results['data_quality']['properties_with_images']}")
            print(f"   Properties with facilities: {results['data_quality']['properties_with_facilities']}")
            print()
            print("⏱️ Performance:")
            print(f"   Total duration: {results['scraping_summary']['duration_formatted']}")
            print(f"   Average per property: {results['scraping_summary']['duration_seconds'] / max(results['statistics']['properties_detailed'], 1):.1f}s")
            print()
            print("💾 Results saved to: scraper/comprehensive_results/")
            
        else:
            print(f"\n❌ Scraping failed: {results['error']}")
            if "partial_data" in results:
                print(f"📊 Partial results: {len(results['partial_data'])} properties extracted")
            
            if "recommendation" in results:
                print(f"💡 Recommendation: {results['recommendation']}")
    
    except KeyboardInterrupt:
        print("\n⏹️ Scraping interrupted by user")
        print("🧹 Cleaning up resources...")
    except Exception as e:
        print(f"\n💥 Unexpected error: {e}")
        print("🧹 Cleaning up resources...")
    
    print("\n✅ Comprehensive scraper finished")


if __name__ == "__main__":
    main()
