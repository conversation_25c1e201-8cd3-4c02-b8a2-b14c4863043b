#!/usr/bin/env python3
"""
Automatic Chrome Setup for PropertyGuru Scraping
Automatically starts Chrome with debugging and navigates to PropertyGuru
"""

import os
import sys
import time
import subprocess
import requests
from pathlib import Path

def kill_existing_chrome():
    """Kill any existing Chrome processes"""
    try:
        print("🔄 Closing existing Chrome processes...")
        subprocess.run(["pkill", "-f", "Google Chrome"], capture_output=True)
        time.sleep(2)
        print("✅ Chrome processes closed")
    except Exception as e:
        print(f"⚠️ Could not close Chrome: {e}")

def start_chrome_with_debugging():
    """Start Chrome with remote debugging enabled"""
    try:
        print("🚀 Starting Chrome with debugging enabled...")
        
        chrome_path = "/Applications/Google Chrome.app/Contents/MacOS/Google Chrome"
        debug_port = "9222"
        user_data_dir = "/tmp/chrome_debug_scraper"
        
        # Create user data directory
        os.makedirs(user_data_dir, exist_ok=True)
        
        # Start Chrome with debugging
        cmd = [
            chrome_path,
            f"--remote-debugging-port={debug_port}",
            f"--user-data-dir={user_data_dir}",
            "--no-first-run",
            "--no-default-browser-check",
            "--disable-default-apps",
            "https://www.propertyguru.com.sg/property-for-sale"
        ]
        
        process = subprocess.Popen(cmd)
        
        print("✅ Chrome started with debugging")
        print(f"🔗 Debug port: {debug_port}")
        print(f"📁 User data: {user_data_dir}")
        
        # Wait for Chrome to start
        print("⏳ Waiting for Chrome to initialize...")
        time.sleep(5)
        
        return True
        
    except Exception as e:
        print(f"❌ Failed to start Chrome: {e}")
        return False

def check_chrome_debug():
    """Check if Chrome debugging is working"""
    try:
        print("🔍 Checking Chrome debug connection...")
        response = requests.get("http://localhost:9222/json", timeout=5)
        
        if response.status_code == 200:
            tabs = response.json()
            print(f"✅ Chrome debugging active with {len(tabs)} tabs")
            
            # Show tabs
            for i, tab in enumerate(tabs, 1):
                title = tab.get('title', 'Unknown')[:50]
                url = tab.get('url', 'Unknown')[:60]
                print(f"   {i}. {title}")
                print(f"      {url}")
            
            return True, tabs
        else:
            print("❌ Chrome debug port not responding")
            return False, []
            
    except Exception as e:
        print(f"❌ Debug check failed: {e}")
        return False, []

def run_enhanced_scraper():
    """Run the enhanced browser controller"""
    try:
        print("\n🚀 Starting enhanced PropertyGuru scraper...")
        
        # Add current directory to Python path
        current_dir = Path(__file__).parent
        sys.path.insert(0, str(current_dir))
        
        # Import and run the enhanced controller
        from enhanced_browser_controller import main as enhanced_main
        enhanced_main()
        
    except Exception as e:
        print(f"❌ Scraper failed: {e}")

def main():
    """Main setup and scraping function"""
    print("🔧 Automatic Chrome Setup for PropertyGuru Scraping")
    print("=" * 60)
    print("🎯 This will:")
    print("   1. Close existing Chrome")
    print("   2. Start Chrome with debugging")
    print("   3. Navigate to PropertyGuru")
    print("   4. Start automated scraping")
    print("=" * 60)
    
    try:
        # Step 1: Kill existing Chrome
        kill_existing_chrome()
        
        # Step 2: Start Chrome with debugging
        if not start_chrome_with_debugging():
            print("❌ Failed to start Chrome")
            return
        
        # Step 3: Check debug connection
        debug_active, tabs = check_chrome_debug()
        
        if not debug_active:
            print("❌ Chrome debugging not active")
            print("💡 Try running manually:")
            print("   /Applications/Google\\ Chrome.app/Contents/MacOS/Google\\ Chrome --remote-debugging-port=9222")
            return
        
        # Step 4: Wait for user to handle any CAPTCHA
        print("\n🔐 Manual Steps Required:")
        print("=" * 40)
        print("1. 👀 Check the Chrome window that just opened")
        print("2. 🔐 Log into Google if prompted")
        print("3. 🤖 Solve any CAPTCHA if it appears")
        print("4. ✅ Wait until you see PropertyGuru property listings")
        print("5. 🔄 Return here and press Enter")
        
        input("\n⏳ Press Enter when PropertyGuru is loaded and ready...")
        
        # Step 5: Run the enhanced scraper
        run_enhanced_scraper()
        
    except KeyboardInterrupt:
        print("\n⏹️ Setup interrupted by user")
    except Exception as e:
        print(f"\n💥 Setup failed: {e}")

if __name__ == "__main__":
    main()
