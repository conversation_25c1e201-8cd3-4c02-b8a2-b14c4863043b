# PropertyGuru Pure Data Scraper 🚀

[![Python](https://img.shields.io/badge/Python-3.8+-blue.svg)](https://python.org)
[![Selenium](https://img.shields.io/badge/Selenium-4.0+-green.svg)](https://selenium.dev)
[![Success Rate](https://img.shields.io/badge/Success%20Rate-99.2%25-brightgreen.svg)](https://github.com/Taiwan-Howard-Lee/propertyguru_listing)

A breakthrough solution for reliable, high-volume property data extraction from PropertyGuru Singapore. This scraper overcomes common challenges including Cloudflare protection, dynamic content loading, and bot detection mechanisms.

## 🎯 Key Features

- **High Performance**: Extract 400+ properties in under 10 minutes
- **Reliable**: 99.2% conversion success rate with complete data fields
- **Stealth Operation**: Bypasses anti-bot detection with human-like behavior
- **Comprehensive Data**: Captures all property details including direct property URLs
- **Resilient**: Handles SSL certificate issues and connection challenges automatically

## 📊 Performance Metrics

| Metric | Value |
|--------|-------|
| Pages per minute | ~2.1 |
| Properties per minute | ~41 |
| Conversion success rate | 99.2% |
| Average extraction time per property | ~1.5 seconds |
| Bot detection avoidance | 100% successful |

## 🚀 Quick Start

### Prerequisites

- Python 3.8+
- Chrome browser
- macOS, Windows, or Linux

### Installation

```bash
# Clone the repository
git clone https://github.com/Taiwan-Howard-Lee/propertyguru_listing.git
cd propertyguru_listing

# Install dependencies
pip install -r requirements.txt
```

### Basic Usage

```python
from src.scrapers.pure_data_scraper import PureDataScraper

# Initialize the scraper
scraper = PureDataScraper()

# Start collection (20 pages, starting from page 1)
success = scraper.start_pure_data_collection(max_pages=20, start_page=1)

if success:
    print("Collection completed successfully!")
    # Check data/ folder for results
```

### Command Line Usage

```bash
# Run the main scraper
python main.py

# Test the scraper components
python -m pytest tests/
```

## 📋 Data Schema

Each property record includes comprehensive information:

```json
{
  "property_name": "212 Jurong East Street 21",
  "price_numeric": 718888,
  "price_formatted": "S$ 718,888",
  "bedrooms": 3,
  "bathrooms": 2,
  "floor_area_sqft": 1291,
  "property_type": "HDB Flat",
  "property_url": "https://www.propertyguru.com.sg/listing/hdb-for-sale-212-jurong-east-street-21-60013717",
  "price_range": "500K-800K",
  "district_code": "D22",
  "mrt_station": "Toh Guan MRT Station",
  "mrt_distance_category": "0-5 min",
  "property_age_years": 15,
  "agent_name": "Agent Name",
  "listed_date": "Jul 13, 2025",
  "image_count": 36,
  "extraction_timestamp": "2025-07-13T18:52:37.xxx",
  "data_source": "PropertyGuru"
}
```

## 🏗️ Project Structure

```
propertyguru_listing/
├── 📊 main.py                          # Main entry point
├── 📁 src/
│   ├── scrapers/
│   │   ├── pure_data_scraper.py        # Main scraper implementation
│   │   └── main_scraper.py             # Core scraping logic
│   ├── schemas/
│   │   └── pure_data_schema.py         # Data schema definitions
│   └── extractors/
│       └── advanced_extractor.py       # Property extraction logic
├── 📊 data/                            # Output data files
├── 🧪 tests/                           # Test files
├── 📚 docs/                            # Documentation
└── 🔧 requirements.txt                 # Dependencies
```

## 🔧 Advanced Usage

### Chrome Debug Mode Setup

For optimal performance, connect to an existing Chrome session:

```bash
# Start Chrome in debug mode (macOS)
/Applications/Google\ Chrome.app/Contents/MacOS/Google\ Chrome --remote-debugging-port=9222 --user-data-dir=/tmp/chrome_debug_profile

# Navigate to PropertyGuru in the opened Chrome window
# Then run your scraper - it will connect to the existing session
```

### Scaling for Production

```python
# Large-scale extraction
scraper = PureDataScraper()

# Extract all available properties (estimated ~52,000 properties)
success = scraper.start_pure_data_collection(max_pages=2600, start_page=1)
```

## 📈 Recent Breakthrough Results

**Latest Test (July 15, 2025):**
- ✅ **20 pages scraped** in 9 minutes 41 seconds
- ✅ **395 properties extracted** with 99.2% success rate
- ✅ **Complete property URLs** captured for all listings
- ✅ **Zero bot detection** incidents
- ✅ **Automatic SSL certificate** issue resolution

## 🛡️ Anti-Detection Features

- **Human-like timing patterns** with randomized delays
- **Undetected Chrome driver** for stealth operation
- **Smart browser session management**
- **Automatic Cloudflare bypass**
- **SSL certificate issue resolution**

## 📚 Documentation

- [**Technical Documentation**](data/demo.md) - Comprehensive technical guide
- [**Setup Guide**](docs/setup_guide.md) - Detailed installation instructions
- [**Chrome Setup**](docs/chrome_setup.md) - Browser configuration guide
- [**Data Format Guide**](docs/DATA_FORMAT_OPTIONS.md) - Output format details

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## ⚠️ Disclaimer

This tool is for educational and research purposes. Please respect PropertyGuru's terms of service and implement appropriate rate limiting. Use responsibly and in compliance with local laws and regulations.

## 🙏 Acknowledgments

- Built with [Selenium](https://selenium.dev/) for web automation
- Uses [undetected-chromedriver](https://github.com/ultrafunkamsterdam/undetected-chromedriver) for stealth operation
- Inspired by the need for reliable property market data

---

**⭐ Star this repository if you find it useful!**

For questions or support, please open an issue or contact the maintainer.
