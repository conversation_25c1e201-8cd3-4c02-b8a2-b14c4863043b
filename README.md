# PropertyGuru Scraper Project

A scalable property data collection system that scrapes PropertyGuru, enriches data, and provides APIs for various applications.

## Project Structure

```
├── scraper/                 # Web scraping engine
├── ai_processor/           # AI-powered filter processing
├── database/               # Database models and migrations
├── api/                    # FastAPI backend
├── frontend/               # React frontend
├── tests/                  # Test suites
├── config/                 # Configuration files
├── docs/                   # Documentation
└── monitoring/             # Monitoring and logging
```

## Quick Start

### 1. Setup Virtual Environment

```bash
python3 -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
```

### 2. Install Dependencies

```bash
pip install -r requirements.txt
```

### 3. Environment Configuration

```bash
cp .env.example .env
# Edit .env with your configuration
```

### 4. Database Setup

```bash
# Start PostgreSQL and Redis
# Update DATABASE_URL in .env
python -m alembic upgrade head
```

### 5. Run the Application

```bash
# Start API server
python -m uvicorn api.main:app --reload

# Start scraping worker (in another terminal)
python -m celery -A scraper.tasks worker --loglevel=info
```

## Development

### Running Tests

```bash
pytest tests/
```

### Code Quality

```bash
# Format code
black .

# Lint code
flake8 .
```

## Documentation

See `docs/` directory for detailed documentation.

## License

This project is for educational purposes only. Please respect PropertyGuru's terms of service.
