"""
FastAPI main application entry point
"""
from fastapi import FastAPI, Depends
from fastapi.middleware.cors import CORSMiddleware
from contextlib import asynccontextmanager
from config.settings import settings
from database.connection import DatabaseManager, check_database_connection
from api.routes import properties
import logging

# Setup logging
logging.basicConfig(level=getattr(logging, settings.log_level.upper()))
logger = logging.getLogger(__name__)


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan events"""
    # Startup
    logger.info("Starting PropertyGuru Scraper API...")

    # Initialize database
    try:
        DatabaseManager.init_db()
        logger.info("Database initialized successfully")
    except Exception as e:
        logger.error(f"Failed to initialize database: {e}")
        raise

    yield

    # Shutdown
    logger.info("Shutting down PropertyGuru Scraper API...")


# Create FastAPI app
app = FastAPI(
    title="PropertyGuru Scraper API",
    description="A scalable property data collection system",
    version="0.1.0",
    debug=settings.debug,
    lifespan=lifespan
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"] if settings.debug else ["http://localhost:3000"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include API routes
app.include_router(properties.router, prefix="/api/v1")

@app.get("/")
async def root():
    """Health check endpoint"""
    return {
        "message": "PropertyGuru Scraper API",
        "version": "0.1.0",
        "status": "running"
    }

@app.get("/health")
async def health_check():
    """Detailed health check"""
    # Check database connection
    db_health = DatabaseManager.health_check()

    # Check Redis connection (placeholder for now)
    redis_status = "not_implemented"

    overall_status = "healthy" if db_health["status"] == "healthy" else "degraded"

    return {
        "status": overall_status,
        "database": db_health,
        "redis": redis_status,
        "timestamp": "2025-01-11T00:00:00Z"  # Will be replaced with actual timestamp
    }

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "api.main:app",
        host=settings.api_host,
        port=settings.api_port,
        reload=settings.debug
    )
