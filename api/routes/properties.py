"""
Property-related API routes
"""
from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from database.connection import get_db
from database.repositories.property_repository import PropertyRepository
from database.schemas import (
    PropertyResponse, PropertyCreate, PropertyUpdate,
    PropertySearchFilters, PropertySearchResponse,
    PropertyHistoryResponse
)
import logging
import json
import os
from typing import Dict, Any

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/properties", tags=["properties"])


def load_mock_data() -> List[Dict[str, Any]]:
    """Load mock data for development"""
    try:
        # Find the latest mock data file
        mock_files = [f for f in os.listdir('.') if f.startswith('mock_properties_') and f.endswith('.json')]
        if not mock_files:
            return []

        latest_file = sorted(mock_files)[-1]
        with open(latest_file, 'r') as f:
            data = json.load(f)

        return data.get('properties', [])
    except Exception as e:
        logger.warning(f"Could not load mock data: {e}")
        return []


def load_processed_data() -> List[Dict[str, Any]]:
    """Load processed real data"""
    try:
        # Find the latest processed data file
        processed_files = [f for f in os.listdir('.') if f.startswith('processed_data_analysis_') and f.endswith('.json')]
        if not processed_files:
            return []

        latest_file = sorted(processed_files)[-1]
        with open(latest_file, 'r') as f:
            data = json.load(f)

        return data.get('processed_properties', [])
    except Exception as e:
        logger.warning(f"Could not load processed data: {e}")
        return []


@router.get("/", response_model=PropertySearchResponse)
async def search_properties(
    property_type: Optional[str] = Query(None, description="Property type (HDB, Condo, Landed, Commercial)"),
    listing_type: Optional[str] = Query(None, description="Listing type (sale, rent)"),
    min_price: Optional[float] = Query(None, description="Minimum price"),
    max_price: Optional[float] = Query(None, description="Maximum price"),
    bedrooms: Optional[int] = Query(None, description="Number of bedrooms"),
    min_bedrooms: Optional[int] = Query(None, description="Minimum bedrooms"),
    max_bedrooms: Optional[int] = Query(None, description="Maximum bedrooms"),
    district: Optional[int] = Query(None, description="District number"),
    postal_code: Optional[str] = Query(None, description="Postal code"),
    mrt_distance_max: Optional[int] = Query(None, description="Maximum MRT distance in meters"),
    status: Optional[str] = Query("active", description="Property status"),
    page: int = Query(1, ge=1, description="Page number"),
    limit: int = Query(20, ge=1, le=100, description="Items per page"),
    sort_by: str = Query("created_at", description="Sort field"),
    sort_order: str = Query("desc", description="Sort order (asc, desc)"),
    db: Session = Depends(get_db)
):
    """Search properties with filters and pagination"""
    try:
        # Create search filters
        filters = PropertySearchFilters(
            property_type=property_type,
            listing_type=listing_type,
            min_price=min_price,
            max_price=max_price,
            bedrooms=bedrooms,
            min_bedrooms=min_bedrooms,
            max_bedrooms=max_bedrooms,
            district=district,
            postal_code=postal_code,
            mrt_distance_max=mrt_distance_max,
            status=status
        )

        # Search properties
        repo = PropertyRepository(db)
        result = repo.search_properties(filters, page, limit, sort_by, sort_order)

        return PropertySearchResponse(**result)

    except Exception as e:
        logger.error(f"Error searching properties: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/{property_id}", response_model=PropertyResponse)
async def get_property(
    property_id: int,
    db: Session = Depends(get_db)
):
    """Get property by ID"""
    try:
        repo = PropertyRepository(db)
        property_obj = repo.get_property_by_id(property_id)

        if not property_obj:
            raise HTTPException(status_code=404, detail="Property not found")

        return property_obj

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting property {property_id}: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.post("/", response_model=PropertyResponse)
async def create_property(
    property_data: PropertyCreate,
    db: Session = Depends(get_db)
):
    """Create a new property"""
    try:
        repo = PropertyRepository(db)

        # Check if property already exists
        existing = repo.get_property_by_guru_id(property_data.property_guru_id)
        if existing:
            raise HTTPException(
                status_code=400,
                detail=f"Property with PropertyGuru ID {property_data.property_guru_id} already exists"
            )

        property_obj = repo.create_property(property_data)
        return property_obj

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error creating property: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.put("/{property_id}", response_model=PropertyResponse)
async def update_property(
    property_id: int,
    property_data: PropertyUpdate,
    db: Session = Depends(get_db)
):
    """Update an existing property"""
    try:
        repo = PropertyRepository(db)
        property_obj = repo.update_property(property_id, property_data)

        if not property_obj:
            raise HTTPException(status_code=404, detail="Property not found")

        return property_obj

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating property {property_id}: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.delete("/{property_id}")
async def delete_property(
    property_id: int,
    db: Session = Depends(get_db)
):
    """Delete a property"""
    try:
        repo = PropertyRepository(db)
        success = repo.delete_property(property_id)

        if not success:
            raise HTTPException(status_code=404, detail="Property not found")

        return {"message": "Property deleted successfully"}

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting property {property_id}: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/{property_id}/history", response_model=List[PropertyHistoryResponse])
async def get_property_history(
    property_id: int,
    db: Session = Depends(get_db)
):
    """Get property change history"""
    try:
        repo = PropertyRepository(db)

        # Check if property exists
        property_obj = repo.get_property_by_id(property_id)
        if not property_obj:
            raise HTTPException(status_code=404, detail="Property not found")

        history = repo.get_property_history(property_id)
        return history

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting property history {property_id}: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/stats/overview")
async def get_property_stats(
    db: Session = Depends(get_db)
):
    """Get property statistics overview"""
    try:
        repo = PropertyRepository(db)
        stats = repo.get_property_stats()
        return stats

    except Exception as e:
        logger.error(f"Error getting property stats: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/recent/listings", response_model=List[PropertyResponse])
async def get_recent_properties(
    limit: int = Query(20, ge=1, le=100, description="Number of recent properties"),
    db: Session = Depends(get_db)
):
    """Get recently added properties"""
    try:
        repo = PropertyRepository(db)
        properties = repo.get_recent_properties(limit)
        return properties

    except Exception as e:
        logger.error(f"Error getting recent properties: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/enhanced/all", response_model=Dict[str, Any])
async def get_all_properties_enhanced(
    include_mock: bool = Query(True, description="Include mock data for development"),
    include_processed: bool = Query(True, description="Include processed real data"),
    property_type: Optional[str] = Query(None, description="Filter by property type"),
    listing_type: Optional[str] = Query(None, description="Filter by listing type"),
    min_price: Optional[float] = Query(None, description="Minimum price"),
    max_price: Optional[float] = Query(None, description="Maximum price"),
    skip: int = Query(0, ge=0, description="Skip items"),
    limit: int = Query(50, ge=1, le=200, description="Limit items"),
    db: Session = Depends(get_db)
):
    """Get all properties from all sources (database, processed real data, mock data)"""
    try:
        all_properties = []

        # Get database properties
        repo = PropertyRepository(db)

        # Create search filters
        filters = PropertySearchFilters(
            property_type=property_type,
            listing_type=listing_type,
            min_price=min_price,
            max_price=max_price
        )

        # Search properties
        db_result = repo.search_properties(filters, page=1, limit=1000)
        db_properties = db_result.get('properties', [])

        # Convert DB properties to dict format
        for prop in db_properties:
            prop_dict = {
                "id": prop.id,
                "property_guru_id": prop.property_guru_id,
                "title": prop.title,
                "property_type": prop.property_type,
                "listing_type": prop.listing_type,
                "price": prop.price,
                "bedrooms": prop.bedrooms,
                "bathrooms": prop.bathrooms,
                "floor_area_sqft": prop.floor_area_sqft,
                "address": prop.address,
                "district": prop.district,
                "scraped_at": prop.scraped_at.isoformat() if prop.scraped_at else None,
                "data_source": "Database",
                "is_real_data": True
            }
            all_properties.append(prop_dict)

        # Add processed real data
        if include_processed:
            processed_properties = load_processed_data()
            for prop in processed_properties:
                prop["data_source"] = "Real Data (Processed)"
                prop["is_real_data"] = True

                # Apply filters
                if property_type and prop.get('property_type') != property_type:
                    continue
                if listing_type and prop.get('listing_type') != listing_type:
                    continue
                if min_price and prop.get('price', 0) < min_price:
                    continue
                if max_price and prop.get('price', float('inf')) > max_price:
                    continue

                all_properties.append(prop)

        # Add mock data
        if include_mock:
            mock_properties = load_mock_data()
            for prop in mock_properties:
                prop["is_real_data"] = False

                # Apply filters
                if property_type and prop.get('property_type') != property_type:
                    continue
                if listing_type and prop.get('listing_type') != listing_type:
                    continue
                if min_price and prop.get('price', 0) < min_price:
                    continue
                if max_price and prop.get('price', float('inf')) > max_price:
                    continue

                all_properties.append(prop)

        # Apply pagination
        total_properties = len(all_properties)
        paginated_properties = all_properties[skip:skip + limit]

        # Calculate statistics
        real_count = len([p for p in all_properties if p.get('is_real_data', False)])
        mock_count = len([p for p in all_properties if not p.get('is_real_data', True)])

        return {
            "properties": paginated_properties,
            "pagination": {
                "total": total_properties,
                "skip": skip,
                "limit": limit,
                "has_more": skip + limit < total_properties
            },
            "statistics": {
                "total_properties": total_properties,
                "real_properties": real_count,
                "mock_properties": mock_count,
                "sources": {
                    "database": len([p for p in all_properties if p.get('data_source') == 'Database']),
                    "processed_real": len([p for p in all_properties if p.get('data_source') == 'Real Data (Processed)']),
                    "mock_generated": len([p for p in all_properties if p.get('data_source') == 'Mock Generator'])
                }
            },
            "filters_applied": {
                "property_type": property_type,
                "listing_type": listing_type,
                "min_price": min_price,
                "max_price": max_price,
                "include_mock": include_mock,
                "include_processed": include_processed
            }
        }

    except Exception as e:
        logger.error(f"Error getting enhanced properties: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/stats/comprehensive", response_model=Dict[str, Any])
async def get_comprehensive_stats(db: Session = Depends(get_db)):
    """Get comprehensive property statistics from all data sources"""
    try:
        # Load all data sources
        repo = PropertyRepository(db)

        # Get all properties from database
        filters = PropertySearchFilters()  # Empty filters to get all
        db_result = repo.search_properties(filters, page=1, limit=1000)
        db_properties = db_result.get('properties', [])
        processed_properties = load_processed_data()
        mock_properties = load_mock_data()

        # Combine all properties
        all_properties = []

        # Add DB properties
        for prop in db_properties:
            all_properties.append({
                "price": prop.price,
                "property_type": prop.property_type,
                "listing_type": prop.listing_type,
                "bedrooms": prop.bedrooms,
                "bathrooms": prop.bathrooms,
                "floor_area_sqft": prop.floor_area_sqft,
                "data_source": "Database",
                "is_real": True
            })

        # Add processed properties
        for prop in processed_properties:
            all_properties.append({
                "price": prop.get('price'),
                "property_type": prop.get('property_type'),
                "listing_type": prop.get('listing_type'),
                "bedrooms": prop.get('bedrooms'),
                "bathrooms": prop.get('bathrooms'),
                "floor_area_sqft": prop.get('floor_area_sqft'),
                "data_source": "Processed Real",
                "is_real": True
            })

        # Add mock properties
        for prop in mock_properties:
            all_properties.append({
                "price": prop.get('price'),
                "property_type": prop.get('property_type'),
                "listing_type": prop.get('listing_type'),
                "bedrooms": prop.get('bedrooms'),
                "bathrooms": prop.get('bathrooms'),
                "floor_area_sqft": prop.get('floor_area_sqft'),
                "data_source": "Mock",
                "is_real": False
            })

        # Calculate comprehensive statistics
        total_count = len(all_properties)
        real_count = len([p for p in all_properties if p.get('is_real', False)])
        mock_count = total_count - real_count

        # Price statistics
        prices = [p['price'] for p in all_properties if p.get('price')]
        price_stats = {}
        if prices:
            price_stats = {
                "count": len(prices),
                "min": min(prices),
                "max": max(prices),
                "average": sum(prices) / len(prices),
                "median": sorted(prices)[len(prices) // 2]
            }

        # Property type distribution
        type_dist = {}
        for prop in all_properties:
            prop_type = prop.get('property_type', 'Unknown')
            type_dist[prop_type] = type_dist.get(prop_type, 0) + 1

        # Listing type distribution
        listing_dist = {}
        for prop in all_properties:
            listing_type = prop.get('listing_type', 'Unknown')
            listing_dist[listing_type] = listing_dist.get(listing_type, 0) + 1

        # Room statistics
        bedrooms = [p['bedrooms'] for p in all_properties if p.get('bedrooms') is not None]
        bathrooms = [p['bathrooms'] for p in all_properties if p.get('bathrooms') is not None]

        return {
            "overview": {
                "total_properties": total_count,
                "real_properties": real_count,
                "mock_properties": mock_count,
                "data_completeness_percentage": (real_count / total_count * 100) if total_count > 0 else 0
            },
            "data_sources": {
                "database": len([p for p in all_properties if p.get('data_source') == 'Database']),
                "processed_real": len([p for p in all_properties if p.get('data_source') == 'Processed Real']),
                "mock_generated": len([p for p in all_properties if p.get('data_source') == 'Mock'])
            },
            "price_analysis": price_stats,
            "property_type_distribution": type_dist,
            "listing_type_distribution": listing_dist,
            "room_statistics": {
                "bedrooms": {
                    "count": len(bedrooms),
                    "min": min(bedrooms) if bedrooms else 0,
                    "max": max(bedrooms) if bedrooms else 0,
                    "average": sum(bedrooms) / len(bedrooms) if bedrooms else 0
                },
                "bathrooms": {
                    "count": len(bathrooms),
                    "min": min(bathrooms) if bathrooms else 0,
                    "max": max(bathrooms) if bathrooms else 0,
                    "average": sum(bathrooms) / len(bathrooms) if bathrooms else 0
                }
            },
            "generated_at": "2025-07-12T09:30:00Z"
        }

    except Exception as e:
        logger.error(f"Error getting comprehensive stats: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")
