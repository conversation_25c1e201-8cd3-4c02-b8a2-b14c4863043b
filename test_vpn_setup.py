#!/usr/bin/env python3
"""
SurfShark VPN Setup Verification Script
Tests VPN CLI installation and connectivity for PropertyGuru scraper
"""

import subprocess
import time
import requests
import json
from datetime import datetime


def run_command(command, timeout=30):
    """Run shell command and return result"""
    try:
        result = subprocess.run(
            command.split(),
            capture_output=True,
            text=True,
            timeout=timeout
        )
        return result.returncode == 0, result.stdout, result.stderr
    except subprocess.TimeoutExpired:
        return False, "", "Command timeout"
    except Exception as e:
        return False, "", str(e)


def get_current_ip():
    """Get current public IP address"""
    try:
        response = requests.get("https://httpbin.org/ip", timeout=10)
        return response.json().get("origin", "Unknown")
    except Exception as e:
        return f"Error: {e}"


def test_vpn_installation():
    """Test if SurfShark VPN CLI is installed"""
    print("🔍 Testing SurfShark VPN CLI installation...")
    
    success, stdout, stderr = run_command("surfshark-vpn --version")
    
    if success:
        print(f"✅ SurfShark VPN CLI installed: {stdout.strip()}")
        return True
    else:
        print(f"❌ SurfShark VPN CLI not found: {stderr}")
        print("💡 Please install SurfShark VPN CLI first")
        return False


def test_vpn_login():
    """Test if logged into SurfShark"""
    print("\n🔐 Testing SurfShark login status...")
    
    success, stdout, stderr = run_command("surfshark-vpn status")
    
    if success and "logged in" in stdout.lower():
        print("✅ Successfully logged into SurfShark")
        return True
    else:
        print("❌ Not logged into SurfShark")
        print("💡 Run: surfshark-vpn login")
        return False


def test_server_list():
    """Test server availability"""
    print("\n🌏 Testing server availability...")
    
    # Test Singapore servers
    success, stdout, stderr = run_command("surfshark-vpn servers --country sg")
    
    if success and stdout.strip():
        servers = [line.strip() for line in stdout.split('\n') if line.strip()]
        print(f"✅ Found {len(servers)} Singapore servers")
        if servers:
            print(f"   Example: {servers[0]}")
        return True
    else:
        print("❌ Could not retrieve server list")
        return False


def test_vpn_connection():
    """Test VPN connection and IP change"""
    print("\n🔄 Testing VPN connection...")
    
    # Get initial IP
    initial_ip = get_current_ip()
    print(f"📍 Initial IP: {initial_ip}")
    
    # Try to connect to Singapore server
    print("🔗 Connecting to Singapore server...")
    success, stdout, stderr = run_command("surfshark-vpn connect sg-sng.prod.surfshark.com", timeout=60)
    
    if not success:
        print(f"❌ Failed to connect: {stderr}")
        return False
    
    # Wait for connection to stabilize
    print("⏳ Waiting for connection to stabilize...")
    time.sleep(5)
    
    # Check new IP
    new_ip = get_current_ip()
    print(f"📍 New IP: {new_ip}")
    
    # Disconnect
    print("🔌 Disconnecting...")
    run_command("surfshark-vpn disconnect", timeout=30)
    
    # Wait for disconnection
    time.sleep(3)
    
    # Check final IP
    final_ip = get_current_ip()
    print(f"📍 Final IP: {final_ip}")
    
    if initial_ip != new_ip:
        print("✅ VPN connection successful - IP changed")
        return True
    else:
        print("❌ VPN connection failed - IP did not change")
        return False


def test_scraper_integration():
    """Test VPN integration with PropertyGuru scraper"""
    print("\n🕷️ Testing scraper VPN integration...")
    
    try:
        # Import VPN manager from scraper
        import sys
        from pathlib import Path
        sys.path.append(str(Path(__file__).parent))
        
        from scraper.comprehensive_scraper import VPNManager
        
        # Test VPN manager
        vpn_manager = VPNManager()
        print("✅ VPN Manager imported successfully")
        
        # Test connection
        print("🔗 Testing VPN Manager connection...")
        if vpn_manager.connect_vpn("sg-sng.prod.surfshark.com"):
            print("✅ VPN Manager connection successful")
            
            # Get IP through manager
            ip = vpn_manager.get_current_ip()
            print(f"📍 IP via VPN Manager: {ip}")
            
            # Disconnect
            vpn_manager.disconnect_vpn()
            print("✅ VPN Manager disconnection successful")
            return True
        else:
            print("❌ VPN Manager connection failed")
            return False
            
    except ImportError as e:
        print(f"❌ Could not import scraper VPN manager: {e}")
        return False
    except Exception as e:
        print(f"❌ VPN Manager test failed: {e}")
        return False


def main():
    """Main test function"""
    print("🚀 SurfShark VPN Setup Verification")
    print("=" * 50)
    print("🎯 Testing VPN setup for PropertyGuru scraper")
    print("=" * 50)
    
    tests = [
        ("VPN CLI Installation", test_vpn_installation),
        ("VPN Login Status", test_vpn_login),
        ("Server Availability", test_server_list),
        ("VPN Connection", test_vpn_connection),
        ("Scraper Integration", test_scraper_integration)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"❌ {test_name} failed with error: {e}")
            results[test_name] = False
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 TEST RESULTS SUMMARY")
    print("=" * 50)
    
    passed = 0
    total = len(tests)
    
    for test_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
        if result:
            passed += 1
    
    print(f"\n🎯 Overall: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 VPN setup is complete and ready for PropertyGuru scraping!")
        print("\n🚀 You can now run:")
        print("   python run_comprehensive_scraper.py --config development")
        print("   python run_comprehensive_scraper.py --config production")
    else:
        print("\n⚠️  VPN setup needs attention. Please fix the failed tests.")
        print("\n💡 Common solutions:")
        print("   1. Install SurfShark VPN CLI")
        print("   2. Run: surfshark-vpn login")
        print("   3. Check your SurfShark subscription")
        print("   4. Test manual connection: surfshark-vpn connect sg-sng.prod.surfshark.com")
    
    # Save results
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    results_file = f"vpn_test_results_{timestamp}.json"
    
    test_results = {
        "timestamp": timestamp,
        "tests_passed": passed,
        "tests_total": total,
        "success_rate": (passed / total) * 100,
        "results": results
    }
    
    with open(results_file, 'w') as f:
        json.dump(test_results, f, indent=2)
    
    print(f"\n💾 Results saved to: {results_file}")


if __name__ == "__main__":
    main()
